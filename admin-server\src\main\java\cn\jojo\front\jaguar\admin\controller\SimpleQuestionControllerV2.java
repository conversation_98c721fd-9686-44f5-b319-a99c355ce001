package cn.jojo.front.jaguar.admin.controller;

import cn.jojo.common.api.ex.DefaultHttpActionResult;
import cn.jojo.common.api.ex.IHttpActionResult;
import cn.jojo.front.jaguar.common.pojo.req.SimpleQuestionSaveReq;
import cn.jojo.front.jaguar.common.pojo.vo.SimpleQuestionDetailVo;
import cn.jojo.front.jaguar.core.service.question.SimpleQuestionService;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping(value = "admin")
@Tag(name = "Jaguar管理后台")
public class SimpleQuestionControllerV2 extends BaseController {

    @Resource
    private SimpleQuestionService simpleQuestionService;

    @GetMapping("/questions/{id}")
    @Operation(summary = "获取题目详情", description = "通过题目id获取题目详情")
    public IHttpActionResult<SimpleQuestionDetailVo> getQuestionDetail(@PathVariable("id")
                                                                       @NotNull(message = "id is null") Long id) {
        SimpleQuestionDetailVo simpleQuestionListVo = simpleQuestionService.getSimpleQuestionDetail(id);
        return DefaultHttpActionResult.successWithData(simpleQuestionListVo);
    }

    @PostMapping("/questions")
    @Operation(summary = "保存题目", description = "保存题目详情")
    public IHttpActionResult<Boolean> save(@Validated @RequestBody SimpleQuestionSaveReq body) {
        boolean result = simpleQuestionService.saveOrUpdate(body, getEmployeeId(), getEmployeeName());
        return DefaultHttpActionResult.successWithData(result);
    }
}
