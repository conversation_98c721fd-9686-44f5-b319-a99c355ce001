package cn.jojo.front.jaguar.adaptor.service;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface IUserActivityRpcClient {

    /**
     * 获取活动参与人数
     *
     * @param classTagActivityIds 真正的活动id
     * @param activityId          活动id
     * @param classId             班级id
     * @param classTeacherId      班级老师id
     * @return 参与人数
     */
    Integer countParticipateUser(List<Long> classTagActivityIds, Long activityId, Long classId,
            Long classTeacherId);
}
