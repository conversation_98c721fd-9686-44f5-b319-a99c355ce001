package cn.jojo.front.jaguar.admin.model.req.activity;

import cn.jojo.front.jaguar.common.enums.task.ActivityThemePageTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@Builder
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "新增活动主题页面")
public class ActivityThemePageSaveReq implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 页面类型
     *
     * @see ActivityThemePageTypeEnum
     */
    @Schema(description = "页面类型", implementation = ActivityThemePageTypeEnum.class)
    private String pageType;

    /**
     * 页面名称
     */
    @Schema(description = "页面名称")
    private String pageName;

    /**
     * 序号
     */
    @Schema(description = "序号")
    private Integer orderNum;


    /**
     * 组件集合
     */
    @Schema(description = "组件集合")
    private List<ActivityThemeComponentSaveReq> components;

}