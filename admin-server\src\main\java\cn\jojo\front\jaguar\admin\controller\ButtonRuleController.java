package cn.jojo.front.jaguar.admin.controller;

import cn.jojo.front.jaguar.common.pojo.bo.EmployeeBo;
import cn.jojo.front.jaguar.common.pojo.entity.button.ButtonGroup;
import cn.jojo.front.jaguar.common.pojo.req.ButtonRuleListReq;
import cn.jojo.front.jaguar.common.pojo.req.ButtonRuleSaveReq;
import cn.jojo.front.jaguar.common.pojo.req.PriorityUpdateReq;
import cn.jojo.front.jaguar.common.pojo.vo.ButtonRuleDetailVo;
import cn.jojo.front.jaguar.common.pojo.vo.ButtonRuleListVo;
import cn.jojo.front.jaguar.core.service.button.ButtonService;
import cn.jojo.infra.sdk.api.metadata.DefaultHttpResult;
import cn.jojo.infra.sdk.api.metadata.DefaultPageResp;
import cn.jojo.infra.sdk.api.metadata.IHttpResult;
import cn.jojo.infra.sdk.api.metadata.IPageResp;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import java.util.Collections;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.constraints.Positive;

import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/admin/button")
@Tag(name = "Jaguar管理后台")
@Validated
public class ButtonRuleController extends BaseController {

    @Resource
    private ButtonService buttonService;

    @Operation(summary ="buttonGroup列表",   description = "获取buttonGroup列表")
    @GetMapping("/getButtonGroupList")
    public IPageResp<ButtonRuleListVo> getButtonGroupList(ButtonRuleListReq<Void> req) {
        IPage<ButtonRuleListVo> page = buttonService.listButtonRule(req);
        return DefaultPageResp.buildPageResp(req, page.getTotal(), page.getRecords());
    }

    @Operation(summary ="buttonGroup优先级",   description = "更新ButtonGroup优先级")
    @PostMapping("/updateButtonGroupPriority")
    public IHttpResult<Boolean> updateButtonGroupPriority(@RequestBody @Validated PriorityUpdateReq req) {
        buttonService.updatePriority(req);
        buttonService.deleteButtonCache(Collections.singleton(req.getBusinessId()));
        return DefaultHttpResult.successWithData(true);
    }

    @Operation(summary ="buttonGroup详情",   description = "获取ButtonGroup详情")
    @GetMapping("/getButtonGroupDetail")
    public IHttpResult<ButtonRuleDetailVo> getButtonGroupDetail(@RequestParam("buttonGroupId") @Positive Integer buttonGroupId) {
        List<ButtonRuleDetailVo> resultData = buttonService
            .listButtonDetailByGroupIds(Collections.singleton(buttonGroupId));
        return DefaultHttpResult.successWithData(CollectionUtils.isEmpty(resultData) ? null : resultData.get(0));
    }

    @Operation(summary ="buttonGroup保存",   description = "保存ButtonGroup")
    @PostMapping("/saveButtonGroup")
    public IHttpResult<Integer> saveButtonGroup(@RequestBody @Validated ButtonRuleSaveReq req) {
        ButtonGroup buttonGroup = buttonService.saveOrUpdateButtonGroup(req, new EmployeeBo()
            .setEmployeeId(getEmployeeId())
            .setEmployeeName(getEmployeeName()));
        buttonService.deleteButtonCache(Collections.singleton(buttonGroup.getId()));
        return DefaultHttpResult.successWithData(buttonGroup.getId());
    }

    @Operation(summary ="删除按钮组",   description = "删除按钮组")
    @GetMapping("/deleteButtonGroup")
    public IHttpResult<Boolean> deleteButtonGroup(@RequestParam("buttonGroupId") @Positive Integer buttonGroupId) {
        buttonService.deleteButtonGroup(buttonGroupId, new EmployeeBo()
            .setEmployeeId(getEmployeeId())
            .setEmployeeName(getEmployeeName()));
        buttonService.deleteButtonCache(Collections.singleton(buttonGroupId));
        return DefaultHttpResult.successWithData(true);
    }
}
