package cn.jojo.front.jaguar.adaptor.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.jojo.front.jaguar.adaptor.service.SkuServiceRpcClient;
import cn.jojo.front.jaguar.common.exception.BusinessException;
import cn.jojo.infra.sdk.api.constants.ApiResultPlatformCodeConstants;
import cn.jojo.infra.sdk.api.metadata.IApiResult;
import cn.jojo.infra.sdk.api.metadata.IRpcResult;
import cn.tinman.clouds.jojoread.common.api.model.dto.CommonRespDTO;
import cn.tinman.clouds.jojoread.common.api.utils.CommonGsonUtil;
import cn.tinman.sharedservices.mall.product.api.response.sku.SkuDto;
import cn.tinman.sharedservices.mall.product.api.response.sku.SkuSaleInfoResp;
import cn.tinman.sharedservices.mall.product.api.service.ISkuApiService;
import cn.tinman.sharedservices.mall.product.api.service.ISkuService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Service
public class SkuServiceRpcClientImpl implements SkuServiceRpcClient {

    @DubboReference
    private ISkuApiService skuApiService;
    @DubboReference
    private ISkuService skuService;

    @Override
    public List<SkuDto> getSkuSaleInfo(List<Long> skuIds) {
        if (CollUtil.isEmpty(skuIds)) {
            return Collections.emptyList();
        }
        List<SkuDto> dtoList = Lists.newArrayList();
        try {
            IRpcResult<List<SkuDto>> rpcResult = skuApiService.listBySkuIds(skuIds);
            if (Objects.isNull(rpcResult) || !rpcResult.checkSuccess()) {
                log.error("调用 skuService.listSaleInfosAnyState 失败，参数:{}，message=[{}]", skuIds,
                    Optional.ofNullable(rpcResult).map(IApiResult::getMessage).orElse(""));
                throw new BusinessException(ApiResultPlatformCodeConstants.BIZ_ERROR,"mall");
            }
            dtoList = rpcResult.getData();
            if (CollectionUtils.isEmpty(dtoList)) {
                return Collections.emptyList();
            }
            dtoList = dtoList.stream().filter(i -> i.getResourcePlatform() == 1).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("调用 skuService.listSaleInfosAnyState 异常", e);
            return Collections.emptyList();
        }
        log.info("调用 skuApiService.listBySkuIds 成功，参数:{},结果: {}", skuIds, CommonGsonUtil.toJson(dtoList));
        return dtoList;
    }

    @Override
    public List<SkuSaleInfoResp> listSaleInfosAnyState(List<Long> skuIds) {
        if (CollUtil.isEmpty(skuIds)) {
            return Collections.emptyList();
        }
        List<SkuSaleInfoResp> dtoList = Lists.newArrayList();
        try {
            CommonRespDTO<List<SkuSaleInfoResp>, Enum> rpcResult = skuService.listSaleInfosAnyState(skuIds);
            if (Objects.isNull(rpcResult) || !rpcResult.isSuccess()) {
                log.error("调用 skuService.listSaleInfosAnyState 失败，参数:{}，message=[{}]", skuIds,
                    Optional.ofNullable(rpcResult).map(CommonRespDTO::getMessage).orElse(""));
                throw new BusinessException(ApiResultPlatformCodeConstants.BIZ_ERROR,"mall");
            }
            dtoList = rpcResult.getData();
            if (CollectionUtils.isEmpty(dtoList)) {
                return Collections.emptyList();
            }
            log.info("调用 skuService.listSaleInfosAnyState 成功，参数:{},结果: {}", skuIds, CommonGsonUtil.toJson(dtoList));
            dtoList = dtoList.stream()
                // 当subSku为空时, 需要判断主sku的resourcePlatform=1
                // 当subSku不为空时, 需要判断所有非赠品sku的resourcePlatform=1, 且此时主sku的resourcePlatform不能使用(可能是空或者0)
                .filter(i -> (CollectionUtils.isEmpty(i.getSubSku()) && i.getResourcePlatform() == 1)
                    || (CollectionUtils.isNotEmpty(i.getSubSku()) && i.getSubSku().stream()
                    // 没有 resourcePlatform 不为 1的主sku
                    .noneMatch(sSku -> BooleanUtils.isFalse(sSku.getGift()) &&
                        !Objects.equals(sSku.getResourcePlatform(), 1)))
                )
                .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("调用 skuService.listSaleInfosAnyState 异常", e);
            return Collections.emptyList();
        }
        return dtoList;
    }
}
