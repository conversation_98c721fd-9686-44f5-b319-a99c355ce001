package cn.jojo.front.jaguar.admin.controller;

import cn.jojo.front.jaguar.common.pojo.req.CommonConditionReq;
import cn.jojo.front.jaguar.common.pojo.vo.CommonConditionVo;
import cn.jojo.front.jaguar.core.service.common.CommonConditionService;
import cn.jojo.infra.sdk.api.metadata.DefaultHttpResult;
import cn.jojo.infra.sdk.api.metadata.IHttpResult;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.constraints.Positive;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping(value = "admin/common-condition")
@Tag(name = "Jaguar管理后台")
@Validated
public class CommonConditionController {

    @Resource
    private CommonConditionService commonConditionService;

    @GetMapping("/list")
    @Operation(summary ="快捷规则列表",   description = "获取快捷规则列表")
    public IHttpResult<List<CommonConditionVo>> getAllCommonCondition() {
        return DefaultHttpResult.successWithData(commonConditionService.getAllCommonCondition());
    }

    @PostMapping("/save")
    @Operation(summary ="保存快捷规则",   description = "保存快捷规则")
    public IHttpResult<Boolean> save(@RequestBody @Validated CommonConditionReq req) {
        return DefaultHttpResult.successWithData(commonConditionService.save(req));
    }

    @GetMapping("/delete")
    @Operation(summary ="删除快捷规则",   description = "删除快捷规则")
    public IHttpResult<Boolean> save(@RequestParam("id") @Positive Integer id) {
        return DefaultHttpResult.successWithData(commonConditionService.deleteById(id));
    }
}
