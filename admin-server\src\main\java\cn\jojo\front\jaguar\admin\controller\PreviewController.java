package cn.jojo.front.jaguar.admin.controller;

import cn.jojo.front.jaguar.biz.service.QrCodeBizService;
import cn.jojo.front.jaguar.biz.service.config.ProjectAdminConfig;
import cn.jojo.front.jaguar.common.pojo.entity.MappQrCode;
import cn.jojo.front.jaguar.common.pojo.vo.MappQrCodeVo;
import cn.jojo.front.jaguar.common.utils.ModelConvertUtil;
import cn.jojo.infra.sdk.api.metadata.DefaultHttpResult;
import cn.jojo.infra.sdk.api.metadata.IHttpResult;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import javax.annotation.Resource;
import javax.validation.constraints.NotNull;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/admin/preview")
@Tag(name = "Jaguar管理后台")
public class PreviewController extends BaseController {

    @Resource
    private QrCodeBizService qrCodeBizService;

    @Resource
    private ProjectAdminConfig projectAdminConfig;

    @Operation(summary ="获取预览小程序二维码")
    @GetMapping(value = "/getPreviewQrCode", produces = "application/json")
    public IHttpResult<MappQrCodeVo> getPreviewQrCode(
        @Parameter(name = "businessType", description = "素材所属类型，1：banner，2：按钮，3：弹窗，4：板块", required = true)
        @RequestParam @NotNull Integer businessType,
        @Parameter(name = "businessId", description = "素材id", required = true)
        @RequestParam @NotNull Integer businessId) {

        MappQrCode mappQrCode = qrCodeBizService
            .createMappQrCode(businessType, businessId, projectAdminConfig.getMappTargetPath());
        return DefaultHttpResult.successWithData(ModelConvertUtil.build(mappQrCode, MappQrCodeVo.class));
    }
}
