package cn.jojo.front.jaguar.adaptor.service;

import cn.jojo.pagani.rpc.api.domain.dto.UserEvaluationSimpleResultDto;
import cn.jojo.pagani.rpc.api.domain.req.evaluate.EvaluationRecordSaveReq;

/**
 * <AUTHOR>
 */
public interface IUserRatingRpcClient {
    public boolean userRatingChild(Long userId);

    Long saveRatingRecord(EvaluationRecordSaveReq req);

    UserEvaluationSimpleResultDto getRatingRecord(Long reportId);
}
