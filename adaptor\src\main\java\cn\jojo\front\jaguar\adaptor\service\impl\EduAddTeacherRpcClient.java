package cn.jojo.front.jaguar.adaptor.service.impl;

import cn.jojo.front.jaguar.adaptor.service.IEduAddTeacherRpcClient;
import cn.jojo.infra.sdk.api.metadata.IRpcResult;
import cn.jojo.pagani.rpc.api.domain.req.AddTeacherReq;
import cn.jojo.pagani.rpc.api.service.AddTeacherRpcService;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class EduAddTeacherRpcClient implements IEduAddTeacherRpcClient {

    @DubboReference
    private AddTeacherRpcService addTeacherRpcService;

    @Value("${query.miniApp.switch:true}")
    private Boolean queryMiniAppSwitch;

    @Override
    public String getAddTeacherUrl(Long classTeacherId, Long orderId, String scene) {
        try {
            AddTeacherReq addTeacherReq = new AddTeacherReq()
                .setScene(scene)
                .setMiniApp(queryMiniAppSwitch);
            if (Objects.nonNull(classTeacherId)) {
                addTeacherReq.setClassTeacherId(classTeacherId);
            }
            if (Objects.nonNull(orderId)) {
                addTeacherReq.setOrderId(orderId);
            }
            IRpcResult<String> rpcResult = addTeacherRpcService.getAddTeacherUrl(addTeacherReq);
            if (!rpcResult.checkSuccess() || Objects.isNull(rpcResult.getData())) {
                log.error("调用 addTeacherRpcService.getAddTeacherUrl 出现错误，param=[{}]，message=[{}]",
                    JSON.toJSONString(addTeacherReq), rpcResult.getSubMessage());
                return null;
            }
            return rpcResult.getData();
        } catch (Exception e) {
            log.error("[addTeacherRpcService][getAddTeacherUrl] 获取添加老师地址 失败，：{}", e.getMessage());
        }
        return null;
    }
}
