package cn.jojo.front.jaguar.adaptor.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.jojo.front.jaguar.adaptor.service.IUserCenterRpcServiceClient;
import cn.jojo.front.jaguar.common.pojo.bo.UserLoginInfoVO;
import cn.jojo.uc.api.domain.dto.LoginAppRespDto;
import cn.jojo.uc.api.domain.dto.WechatUserFollowSimpleDto;
import cn.jojo.uc.api.domain.request.LoginAppRpcReq;
import cn.jojo.uc.api.enums.LoginAppErrorStatus;
import cn.jojo.uc.api.enums.WechatUserFollowErrorStatus;
import cn.jojo.uc.api.service.LoginAppRpcService;
import cn.jojo.uc.api.service.WechatUserFollowService;
import cn.jojo.uc.common.domain.dto.CommonRespDto;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Component;


@Component
@Slf4j
public class UserCenterRpcServiceClient implements IUserCenterRpcServiceClient {


    @Reference
    private LoginAppRpcService loginAppRpcService;
    @DubboReference
    private WechatUserFollowService wechatUserFollowService;


    @Override
    public UserLoginInfoVO getLoginTimeByPackageName(String packageName, Long userId) {

        LoginAppRpcReq req = new LoginAppRpcReq();
        req.setUserId(userId);
        req.setPackageName(packageName);

        try {
            CommonRespDto<LoginAppRespDto, LoginAppErrorStatus> loginAppInfo  = loginAppRpcService.getLoginAppInfo(req);
            //调用用户中心rpc接口，获取首次登录时间
            if (loginAppInfo.isSuccess()) {
                LoginAppRespDto data = loginAppInfo.getData();
                return BeanUtil.copyProperties(data, UserLoginInfoVO.class);
            }
        } catch (Exception e) {
            log.error("调用用户中心失败，message=[{}],req=[{}]",e.getMessage(), JSON.toJSONString(req));
        }
        return null;
    }

    @Override
    public WechatUserFollowSimpleDto getFollowStatusByAppIdAndUserId(Long userId, String appId) {
        CommonRespDto<WechatUserFollowSimpleDto, WechatUserFollowErrorStatus> respDto = null;
        try {
            respDto = wechatUserFollowService
                .getByUserIdAndWechatAppIdUseCache(userId, appId);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        if (respDto.withoutData()) {
            log.info("根据用户ID查找用户WechatUserFollowDto失败: userId:{},appId:{},message={}", userId, appId,
                JSON.toJSONString(respDto));
            return null;
        }
        return respDto.getData();
    }
}
