package cn.jojo.front.jaguar.admin.model.convert;

import cn.jojo.edu.common.utils.convert.BaseModelConvert;
import cn.jojo.front.jaguar.admin.model.req.activity.ClassScopesQueryReq;
import cn.jojo.front.jaguar.biz.service.pojo.bo.activity.ClassScopesQueryBo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 下拉选查询req 转bo
 *
 * <AUTHOR>
 * @date 2024/07/03
 */
@Mapper
public interface ClassScopesQueryReq2BoConvert extends BaseModelConvert<ClassScopesQueryReq, ClassScopesQueryBo> {

    ClassScopesQueryReq2BoConvert INSTANCE = Mappers.getMapper(ClassScopesQueryReq2BoConvert.class);
}
