package cn.jojo.front.jaguar.adaptor.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.jojo.front.jaguar.adaptor.service.MallActivityRpcClient;
import cn.jojo.front.jaguar.common.exception.BusinessException;
import cn.jojo.infra.sdk.api.constants.ApiResultPlatformCodeConstants;
import cn.jojo.infra.sdk.api.metadata.IApiResult;
import cn.jojo.infra.sdk.api.metadata.IRpcResult;
import cn.jojo.mall.boom.rpc.api.dto.ActivityDto;
import cn.jojo.mall.boom.rpc.api.req.ActivityReq;
import cn.jojo.mall.boom.rpc.api.service.ActivityRpcService;
import cn.tinman.clouds.jojoread.common.api.utils.CommonGsonUtil;
import cn.tinman.sharedservices.mall.product.api.service.ISkuApiService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Slf4j
@Service
public class MallActivityRpcClientImpl implements MallActivityRpcClient {

    @DubboReference
    private ISkuApiService skuApiService;
    @DubboReference
    private ActivityRpcService activityRpcService;


    @Override
    public List<ActivityDto> listActivity(List<Long> activityIds) {

        if (CollUtil.isEmpty(activityIds)) {
            return Collections.emptyList();
        }
        ActivityReq req = new ActivityReq();
        req.setActivityIds(activityIds);

        List<ActivityDto> dtoList;
        try {
            IRpcResult<List<ActivityDto>> rpcResult = activityRpcService.listActivity(req);
            if (Objects.isNull(rpcResult) || !rpcResult.checkSuccess()) {
                log.error("调用 MallActivityRpcClient.listActivity 失败，参数:{}，message=[{}]", activityIds,
                    Optional.ofNullable(rpcResult).map(IApiResult::getMessage).orElse(""));
                throw new BusinessException(ApiResultPlatformCodeConstants.BIZ_ERROR,
                    "MallActivityRpcClient#listActivity rpc failed");
            }
            dtoList = rpcResult.getData();
            if (CollectionUtils.isEmpty(dtoList)) {
                return Collections.emptyList();
            }
        } catch (Exception e) {
            log.error("调用 MallActivityRpcClient.listActivity 异常", e);
            throw new BusinessException(ApiResultPlatformCodeConstants.BIZ_ERROR, e.getMessage());
        }
        log.info("调用 MallActivityRpcClient.listActivity 成功，参数:{},结果: {}", activityIds,
            CommonGsonUtil.toJson(dtoList));
        return dtoList;
    }
}
