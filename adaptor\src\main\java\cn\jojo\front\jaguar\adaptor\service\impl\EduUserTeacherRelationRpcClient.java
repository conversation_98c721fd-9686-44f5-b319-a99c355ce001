package cn.jojo.front.jaguar.adaptor.service.impl;

import cn.jojo.edu.malacca.rpc.api.dto.EduUserTeacherRelationDto;
import cn.jojo.edu.malacca.rpc.api.req.EduUserTeacherWeChatStatusReq;
import cn.jojo.edu.malacca.rpc.api.service.EduUserTeacherRelationRpcService;
import cn.jojo.front.jaguar.adaptor.service.IEduUserTeacherRelationRpcClient;
import cn.jojo.infra.sdk.api.metadata.IRpcResult;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Component
@Slf4j
public class EduUserTeacherRelationRpcClient implements IEduUserTeacherRelationRpcClient {

    @DubboReference(timeout = 5000)
    private EduUserTeacherRelationRpcService eduUserTeacherRelationRpcService;


    @Override
    public EduUserTeacherRelationDto queryUserTeacherWeChatStatus(EduUserTeacherWeChatStatusReq req) {
        if(Objects.isNull(req)){
            return null;
        }
        try{
            IRpcResult<EduUserTeacherRelationDto> rpcResult = eduUserTeacherRelationRpcService.queryUserTeacherWeChatStatus(
                req);
            if (!rpcResult.checkSuccess()) {
                log.error("调用 eduUserTeacherRelationRpcService.queryUserTeacherWeChatStatus 出现异常，req=[{}]",
                    JSON.toJSONString(req));
                return null;
            }
            return rpcResult.getData();
        }catch (Exception e){
            log.error("调用 eduUserTeacherRelationRpcService.queryUserTeacherWeChatStatus error，req=[{}],msg={}",
                JSON.toJSONString(req),e.getMessage());
        }
        return null;
    }
}
