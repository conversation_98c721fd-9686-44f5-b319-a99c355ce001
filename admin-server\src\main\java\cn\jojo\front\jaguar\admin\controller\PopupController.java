package cn.jojo.front.jaguar.admin.controller;

import cn.jojo.front.jaguar.common.enums.MaterialFormatEnum;
import cn.jojo.front.jaguar.common.enums.PopupType;
import cn.jojo.front.jaguar.common.pojo.bo.EmployeeBo;
import cn.jojo.front.jaguar.common.pojo.entity.popup.Popup;
import cn.jojo.front.jaguar.common.pojo.req.PopupListReq;
import cn.jojo.front.jaguar.common.pojo.req.PopupSaveReq;
import cn.jojo.front.jaguar.common.pojo.req.PriorityUpdateReq;
import cn.jojo.front.jaguar.common.pojo.vo.PopupDetailVo;
import cn.jojo.front.jaguar.common.pojo.vo.PopupListVo;
import cn.jojo.front.jaguar.core.service.popup.PopupService;
import cn.jojo.infra.sdk.api.metadata.DefaultHttpResult;
import cn.jojo.infra.sdk.api.metadata.DefaultPageResp;
import cn.jojo.infra.sdk.api.metadata.IHttpResult;
import cn.jojo.infra.sdk.api.metadata.IPageResp;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import java.util.Collections;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.constraints.NotNull;

import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/admin/popup")
@Tag(name = "Jaguar管理后台")
@Validated
public class PopupController extends BaseController {

    @Resource
    private PopupService popupService;

    @GetMapping("/getPopupList")
    @Operation(summary ="弹窗列表",   description = "已迁移至 cn.jojo.front.jaguar.admin.controller.PopupControllerV2.getPopupList")
    public IPageResp<PopupListVo> getPopupList(PopupListReq<Void> req) {
        IPage<PopupListVo> page = popupService.listPopup(req);
        return DefaultPageResp.buildPageResp(req, page.getTotal(), page.getRecords());
    }

    @PostMapping("/updatePopupPriority")
    @Operation(summary ="弹窗优先级",   description = "更新弹窗优先级")
    public IHttpResult<Boolean> updatePopupPriority(@RequestBody @Validated PriorityUpdateReq req) {
        Popup popup = popupService.updatePriority(req);
        popupService.deleteCache(Collections.singleton(req.getBusinessId()), PopupType.of(popup.getPopupKey()));
        return DefaultHttpResult.successWithData(true);
    }

    @GetMapping("/getPopupInfo")
    @Operation(summary ="弹窗详情",   description = "已迁移至 cn.jojo.front.jaguar.admin.controller.PopupControllerV2.getPopupInfo")
    public IHttpResult<PopupDetailVo> getPopupInfo(@RequestParam("popupId") @NotNull Integer popupId) {
        List<PopupDetailVo> list = popupService.listPopupDetailByIds(Collections.singleton(popupId));
        return DefaultHttpResult.successWithData(CollectionUtils.isEmpty(list) ? null : list.get(0));
    }

    @PostMapping("savePopup")
    @Operation(summary ="弹窗保存",   description = "已迁移至 cn.jojo.front.jaguar.admin.controller.PopupControllerV2.savePopup")
    public IHttpResult<Integer> savePopup(@RequestBody @Validated PopupSaveReq req) {
        //兼容前端未发布的场景，后面可以删掉
        if (req.getMaterialFormat() == null) {
            req.setMaterialFormat(MaterialFormatEnum.PICTURE.getCode());
        }
        
        req.validateSelf();
        Popup popup = popupService.saveOrUpdatePopup(req, new EmployeeBo()
            .setEmployeeId(getEmployeeId())
            .setEmployeeName(getEmployeeName()));
        popupService.deleteCache(Collections.singleton(popup.getId()), PopupType.of(popup.getPopupKey()));
        popupService.removeUserPopup(popup);
        return DefaultHttpResult.successWithData(popup.getId());
    }

    @GetMapping("/deletePopup")
    public IHttpResult<Boolean> deletePopup(@RequestParam("popupId") @NotNull Integer popupId) {
        Popup popup = popupService.deletePopup(popupId, new EmployeeBo()
            .setEmployeeId(getEmployeeId())
            .setEmployeeName(getEmployeeName()));
        popupService.deleteCache(Collections.singleton(popupId), PopupType.of(popup.getPopupKey()));
        return DefaultHttpResult.successWithData(true);
    }

}
