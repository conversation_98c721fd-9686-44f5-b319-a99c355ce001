package cn.jojo.front.jaguar.admin.controller.classes;


import cn.hutool.core.util.ObjectUtil;
import cn.jojo.edu.common.utils.exceptions.ParamValidException;
import cn.jojo.front.jaguar.admin.controller.BaseController;
import cn.jojo.front.jaguar.admin.model.convert.ClassQueryReq2BoConvert;
import cn.jojo.front.jaguar.admin.model.req.activity.ClassQueryReq;
import cn.jojo.front.jaguar.biz.service.activitity.IClassBizService;
import cn.jojo.front.jaguar.biz.service.pojo.bo.activity.ClassQueryBo;
import cn.jojo.front.jaguar.common.pojo.vo.activity.ClassAdminVo;
import cn.jojo.infra.sdk.api.metadata.DefaultHttpResult;
import cn.jojo.infra.sdk.api.metadata.IHttpResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Validated
@RestController
@RequestMapping("admin/classes")
@Tag(name = "班级查询")
public class ClassController extends BaseController {

    @Resource
    private IClassBizService classBizService;

    @Operation(summary = "班级查询列表", description = "班级查询")
    @GetMapping
    public IHttpResult<List<ClassAdminVo>> list(@ModelAttribute ClassQueryReq classQueryReq) {

        if (ObjectUtil.isEmpty(classQueryReq)) {
            throw new ParamValidException("input data must not null");
        }
        if (ObjectUtil.isAllEmpty(classQueryReq.getClassIds(),
            classQueryReq.getClassKeys(), classQueryReq.getCourseId())) {
            throw new ParamValidException("Please pass in parameters ");
        }

        ClassQueryBo classQueryBo = ClassQueryReq2BoConvert.INSTANCE.model1ToModel2(classQueryReq);
        return DefaultHttpResult.successWithData(classBizService.list(classQueryBo));
    }
}
