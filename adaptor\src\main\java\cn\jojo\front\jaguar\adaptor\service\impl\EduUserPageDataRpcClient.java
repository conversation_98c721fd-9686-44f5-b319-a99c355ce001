package cn.jojo.front.jaguar.adaptor.service.impl;

import cn.jojo.edu.study.rpc.api.dto.UserHasBeenStudiedDto;
import cn.jojo.edu.study.rpc.api.req.UserHasBeenStudiedReq;
import cn.jojo.edu.study.rpc.api.service.EduUserPageDataService;
import cn.jojo.infra.sdk.api.metadata.IRpcResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * 2024/12/11 13:49
 */
@Service
@Slf4j
public class EduUserPageDataRpcClient {
    @DubboReference
    private EduUserPageDataService eduUserPageDataService;

    public List<UserHasBeenStudiedDto> getUserStudied(Long userId, List<Long> classIds) {
        if (userId == null || CollectionUtils.isEmpty(classIds)) {
            return Collections.emptyList();
        }
        try {
            UserHasBeenStudiedReq req = UserHasBeenStudiedReq.builder()
                    .userId(userId)
                    .classIds(classIds)
                    .build();
            IRpcResult<List<UserHasBeenStudiedDto>> rpcResult = eduUserPageDataService.hasBeenStudied(req);
            if (!rpcResult.checkSuccess()) {
                log.error("getUserStudied rpcResult error:{}", rpcResult.getMessage());
                return Collections.emptyList();
            }
            return Optional.ofNullable(rpcResult.getData()).orElse(Collections.emptyList());
        } catch (Exception e) {
            log.error("getUserStudied error", e);
            return Collections.emptyList();
        }
    }
}
