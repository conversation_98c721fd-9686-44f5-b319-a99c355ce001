package cn.jojo.front.jaguar.admin.controller;

import cn.jojo.common.api.ex.DefaultHttpActionPageResult;
import cn.jojo.common.api.ex.DefaultHttpActionResult;
import cn.jojo.common.api.ex.IHttpActionResult;
import cn.jojo.front.jaguar.common.pojo.bo.PageBo;
import cn.jojo.front.jaguar.common.pojo.req.CustomPageListReq;
import cn.jojo.front.jaguar.common.pojo.req.CustomPageSaveReq;
import cn.jojo.front.jaguar.common.pojo.vo.CustomPageDetailVo;
import cn.jojo.front.jaguar.common.pojo.vo.CustomPageListVo;
import cn.jojo.front.jaguar.core.service.impl.custompage.CustomPageService;
import cn.jojo.infra.sdk.api.metadata.IPageResp;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/admin/custom-page")
@Validated
@Tag(name = "自定义页面接口")
public class CustomPageController extends BaseController {
    @Resource
    private CustomPageService customPageService;

    @GetMapping("/list")
    @Operation(summary = "获取自定义页面列表", description = "获取自定义页面列表")
    public IHttpActionResult<IPageResp<CustomPageListVo>> listPage(CustomPageListReq req) {
        PageBo<CustomPageListVo> page = customPageService.listPage(req, getEmployeeId());
        return DefaultHttpActionPageResult.successWithPageData(
                page.getPageNum(), page.getPageSize(), page.getTotal(), page.getData());
    }

    @GetMapping("/detail")
    @Operation(summary = "获取自定义页面详情", description = "获取自定义页面详情")
    public IHttpActionResult<CustomPageDetailVo> detail(@NotNull Long id) {
        CustomPageDetailVo result = customPageService.getDetailById(id);
        return DefaultHttpActionResult.successWithData(result);
    }

    @PostMapping("/detail")
    @Operation(summary = "更新或新增自定义页面", description = "更新或新增自定义页面")
    public IHttpActionResult<Long> update(@RequestBody CustomPageSaveReq req) {
        Long id = customPageService.saveOrUpdate(req, getEmployeeId(), getEmployeeName());
        return DefaultHttpActionResult.successWithData(id);
    }
}
