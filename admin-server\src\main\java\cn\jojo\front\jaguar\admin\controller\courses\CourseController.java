package cn.jojo.front.jaguar.admin.controller.courses;


import cn.jojo.front.jaguar.admin.controller.BaseController;
import cn.jojo.front.jaguar.biz.service.CourseBizService;
import cn.jojo.front.jaguar.biz.service.activitity.IClassBizService;
import cn.jojo.front.jaguar.biz.service.pojo.bo.activity.ClassGroupQueryBo;
import cn.jojo.front.jaguar.biz.service.pojo.bo.activity.CourseQueryBo;
import cn.jojo.front.jaguar.common.pojo.req.course.QueryCourseReq;
import cn.jojo.front.jaguar.common.pojo.vo.SimpleCourseVo;
import cn.jojo.front.jaguar.common.pojo.vo.eduUserClass.EduClassGroupVo;
import cn.jojo.front.jaguar.common.utils.CustomBeanUtils;
import cn.jojo.infra.sdk.api.metadata.DefaultHttpResult;
import cn.jojo.infra.sdk.api.metadata.IHttpResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.constraints.Positive;
import java.util.List;

/**
 * <AUTHOR>
 */
@Validated
@RestController
@RequestMapping("admin/courses")
@Tag(name = "课程班级组查询")
public class CourseController extends BaseController {

    @Resource
    private IClassBizService classBizService;

    @Resource
    private CourseBizService courseBizService;

    @Operation(summary = "课程班级组查询", description = "课程班级组查询")
    @GetMapping("/{courseId}/class-groups")
    public IHttpResult<List<EduClassGroupVo>> listGroups(@PathVariable @Positive Long courseId) {

        ClassGroupQueryBo classGroupQueryBo = ClassGroupQueryBo.builder().courseId(courseId).build();
        return DefaultHttpResult.successWithData(classBizService.listGroups(classGroupQueryBo));
    }

    @Operation(summary = "查询课程", description = "查询课程")
    @GetMapping()
    public IHttpResult<List<SimpleCourseVo>> listCourses(@ModelAttribute QueryCourseReq queryCourseReq) {

        CourseQueryBo courseQueryBo = CustomBeanUtils.copyProperties(queryCourseReq, CourseQueryBo::new);
        return DefaultHttpResult.successWithData(courseBizService.listCourses(courseQueryBo));
    }

}
