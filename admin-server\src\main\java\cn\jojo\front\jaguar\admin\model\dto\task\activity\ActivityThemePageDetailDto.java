package cn.jojo.front.jaguar.admin.model.dto.task.activity;

import cn.jojo.front.jaguar.common.enums.task.ActivityThemePageTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@Builder
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "主题单个页面详情对象")
public class ActivityThemePageDetailDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键页ID
     */
    @Schema(description = "页ID")
    private Long id;
    /**
     * 主题id，编辑时得传入
     */
    @Schema(description = "主题ID")
    private Long themeId;
    /**
     * 页面类型
     *
     * @see cn.jojo.front.jaguar.common.enums.task.ActivityThemePageTypeEnum
     */
    @Schema(description = "页面类型", implementation = ActivityThemePageTypeEnum.class)
    private String pageType;

    @Schema(description = "页面类型描述")
    private String pageTypeDesc;

    /**
     * 页面名称
     */
    @Schema(description = "页面名称")
    private String pageName;


    /**
     * 序号
     */
    @Schema(description = "序号")
    private Integer orderNum;


    /**
     * 组件集合
     */
    @Schema(description = "组件集合")
    private List<ActivityThemeComponentDetailDto> components;

}