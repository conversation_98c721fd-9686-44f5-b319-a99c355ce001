package cn.jojo.front.jaguar.adaptor.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.EnumUtil;
import cn.jojo.edu.fantasy.common.dict.hub.HubStatus;
import cn.jojo.edu.fantasy.rpc.api.dto.*;
import cn.jojo.edu.fantasy.rpc.api.req.*;
import cn.jojo.edu.fantasy.rpc.api.req.enums.EduHubDetailAggregationType;
import cn.jojo.edu.fantasy.rpc.api.service.IEduHubRpcService;
import cn.jojo.front.jaguar.adaptor.service.IEduHubRpcClient;
import cn.jojo.front.jaguar.common.enums.ResourceStatus;
import cn.jojo.front.jaguar.common.pojo.bo.*;
import cn.jojo.front.jaguar.common.pojo.req.EduHubRpcReq;
import cn.jojo.front.jaguar.common.utils.JsonBeanUtil;
import cn.jojo.infra.sdk.api.metadata.IPageResp;
import cn.jojo.infra.sdk.api.metadata.IRpcResult;
import cn.jojo.infra.sdk.logging.JoJoLogging;
import cn.jojo.uc.common.enums.BooleanStatus;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * @Description: 专辑相关rpc调用client
 * @Copyright: Copyright (c) 2022  ALL RIGHTS RESERVED.
 * @Company: 书声科技有限公司
 * @Author: chenHao
 * @CreateDate: 2022/9/30 15:46
 * @UpdateDate: 2022/9/30 15:46
 * @UpdateRemark: init
 * @Version: 1.0
 */
@Component
@Slf4j
public class EduHubRpcClient implements IEduHubRpcClient {

    @DubboReference
    private IEduHubRpcService eduHubRpcService;

    @Value("${hub.id.max.size:50}")
    private Integer hubIdMaxSize;

    @Value("${recommend.query.max.size:500}")
    private Long recommendQueryMaxSize;


    /**
     * 调用教务查询hub资源聚合信息
     *
     * @param req 教务hub资源聚合rpc接口入参
     * @return List<EduHubAggregationDto>
     */
    private List<EduHubAggregationDto> queryAggregation(EduHubExtensionReq req) {
        Function<EduHubExtensionReq, List<EduHubAggregationDto>> doQuery = query -> {
            List<EduHubAggregationDto> data = Collections.emptyList();
            try {
                IRpcResult<List<EduHubAggregationDto>> rpcResult = eduHubRpcService.queryHubExtension(query);
                JoJoLogging.logger(log)
                        .unIndex("req", JSON.toJSONString(req))
                        .unIndex("rpcResult", JSON.toJSONString(rpcResult))
                        .info("EduHubRpcClient.queryAggregation");
                if (!rpcResult.checkSuccess()) {
                    log.warn("invoke eduHubRpcService.queryHubExtension fail:{}", rpcResult.getMessage());
                    return Collections.emptyList();
                }
                data = rpcResult.getData();
            } catch (Exception e) {
                log.error("invoke eduHubRpcService.queryHubExtension fail:{}",e.getMessage());
                return Collections.emptyList();
            }
            return data;
        };

        // 如果没有传hubIds参数时，或者请求为空时，则直接按照传入的参数查询
        if (Objects.isNull(req.getQueryReq()) || CollectionUtils.isEmpty(req.getQueryReq().getHubIds())) {
            return doQuery.apply(req);
        }

        // 如果传了hubIds参数时，则将hubIds列表拆分为分批查询
        return Lists.partition(req.getQueryReq().getHubIds(), hubIdMaxSize).stream()
                .map(hubIds -> EduHubExtensionReq.builder()
                        .aggregationTypes(req.getAggregationTypes())
                        .queryReq(EduHubQueryReq.builder()
                                .hubIds(hubIds)
                                .containsInvalid(req.getQueryReq().getContainsInvalid())
                                .build())
                        .build())
                .map(doQuery)
                .flatMap(List::stream).collect(Collectors.toList());
    }

    @Override
    public List<EduHubAggregationBo> queryHubExtension(List<Long> hubIds, List<Integer> typeList, ResourceStatus status)
    {
        if (CollectionUtils.isEmpty(hubIds)) {
            return Collections.emptyList();
        }

        EduHubQueryReq queryReq = EduHubQueryReq.builder()
                .hubIds(hubIds).build();
        List<EduHubDetailAggregationType> aggregationTypes = typeList.stream().map(EduHubDetailAggregationType::get)
                .collect(Collectors.toList());
        EduHubExtensionReq req = EduHubExtensionReq.builder()
                .queryReq(queryReq)
                .aggregationTypes(aggregationTypes)
                .build();
        return queryAggregation(req)
                .stream()
                .map(o -> convert(o, status))
                .collect(Collectors.toList());
    }

    @Override
    public List<EduHubRelationBo> queryHubRelation(List<Long> hubIds, Boolean isAlbumLevel) {
        if (CollectionUtils.isEmpty(hubIds)) {
            return Collections.emptyList();
        }

        return Lists.partition(hubIds, hubIdMaxSize).stream()
            .map(ids -> EduHubRelationReq.builder().hubIds(ids).pointAlbumLevel(isAlbumLevel).build())
            .map(req -> {
                List<EduHubRelationDto> data = Collections.emptyList();
                try {
                    IRpcResult<List<EduHubRelationDto>> rpcResult = eduHubRpcService.queryHubRelation(req);
                    if (!rpcResult.checkSuccess()) {
                        log.warn("invoke eduHubRpcService.queryHubRelation fail:{}", rpcResult.getMessage());
                        return Collections.<EduHubRelationBo>emptyList();
                    }

                    if (CollUtil.isEmpty(rpcResult.getData())) {
                        return Collections.<EduHubRelationBo>emptyList();
                    }
                    data = rpcResult.getData();
                } catch (Exception e) {
                    log.error("invoke eduHubRpcService.queryHubRelation fail:{}", e.getMessage());
                }
                return JsonBeanUtil.beanListTrans(data, EduHubRelationBo.class);
            }).flatMap(List::stream).filter(Objects::nonNull).collect(Collectors.toList());
    }

    @Override
    public List<HubBo> queryHubsByHubIds(List<Long> hubIds) {
        if (CollectionUtils.isEmpty(hubIds)) {
            return Collections.emptyList();
        }

        return Lists.partition(hubIds, hubIdMaxSize).stream()
            .map(partition -> EduHubExtensionReq.builder()
                .queryReq(EduHubQueryReq.builder().hubIds(partition).build())
                .aggregationTypes(Collections.singletonList(EduHubDetailAggregationType.ALBUM))
                .build())
            .map(this::getHubBos)
            .flatMap(List::stream)
            .collect(Collectors.toList());
    }

    @Override
    public Page<HubBo> getHubsByFuzzyQuery(String contentType, Long hubId, String hubName,
                                           Integer pageNum, Long pageSize) {
        if (CharSequenceUtil.isEmpty(contentType)) {
            return new Page<>();
        }
        EduHubReq req = EduHubReq.builder()
                .hubIds(Objects.isNull(hubId) ? Collections.emptyList() : Collections.singletonList(hubId))
                .hubName(hubName)
                .fuzzyQuery(CharSequenceUtil.isNotBlank(hubName) ? 1 : 0)
                .pageSize(pageSize)
                .statues(Collections.singletonList(HubStatus.ACTIVATED.getStatus()))
                .pageNum(pageNum)
                .contentTypes(Collections.singletonList(contentType)).build();
        return getHubBoPages(req);
    }

    @Override
    public List<HubContentUpdateBo> getContentUpdateHubs(List<Long> hubIds) {
        if (CollectionUtils.isEmpty(hubIds)) {
            return Collections.emptyList();
        }
        EduContentUpdateHubsReq req = EduContentUpdateHubsReq.builder().hubIds(hubIds).build();
        IRpcResult<List<EduHubContentUpdateDto>> rpcResult = eduHubRpcService.getContentUpdateHubs(req);
        if (!rpcResult.checkSuccess()) {
            log.error("invoke eduHubRpcService.getContentUpdateHubs failed, messages={}", rpcResult.getMessage());
            return Collections.emptyList();
        }
        return Optional.ofNullable(rpcResult.getData())
                .orElse(Collections.emptyList()).stream()
                .map(dto -> new HubContentUpdateBo()
                        .setHubId(dto.getHubId())
                        .setUpdateTime(dto.getUpdateTime())).collect(Collectors.toList());
    }

    @Override
    public List<EduHubPageBo> getPlateRecommendAllHub() {
        List<EduHubPageBo> allHubList = Lists.newArrayList();
        long pageNum = 1;
        try {
            EduHubPageReq req = new EduHubPageReq();
            req.setRecommendationFlag(BooleanStatus.TRUE.getType());
            req.setPageSize(50L);
            req.setStatues(Collections.singletonList("ACTIVATED"));
            // 分页获取全量
            while (true) {
                req.setPageNum(pageNum);
                IRpcResult<IPageResp<EduHubPageDto>> rpcResult = eduHubRpcService.queryHubsPage(req);
                JoJoLogging.logger(log)
                        .unIndex("req", JSON.toJSONString(req))
                        .unIndex("rpcResult", JSON.toJSONString(rpcResult))
                        .info("plate recommend getPlateRecommendAllHub pageNum:{}", pageNum);
                if (!rpcResult.checkSuccess() || Objects.isNull(rpcResult.getData())) {
                    return allHubList;
                }
                IPageResp<EduHubPageDto> pageResp = rpcResult.getData();
                if (CollUtil.isNotEmpty(pageResp.getPageRecords())) {
                    allHubList.addAll(convertEduHubPageBo(pageResp.getPageRecords()));
                }
                if (pageNum >= pageResp.getPageCount() || CollUtil.isEmpty(pageResp.getPageRecords())
                        || allHubList.size() >= recommendQueryMaxSize) { //最多查询500个
                    break;
                }
                pageNum++;
            }
        } catch (Exception e) {
            log.error("plate recommend getPlateRecommendAllHub error", e);
        }
        return allHubList;
    }

    @Override
    public List<HubBo> queryHubs(EduHubRpcReq rpcReq) {
        EduHubReq req = new EduHubReq();
        BeanUtils.copyProperties(rpcReq, req);
        List<EduHubDto> results = new ArrayList<>();
        boolean hasNextPage = true;
        int currentPageNum = req.getPageNum();
        req.setPageSize(100L);
        while (hasNextPage) {
            hasNextPage = false;
            try {
                req.setPageNum(currentPageNum++);
                IRpcResult<IPageResp<EduHubDto>> hubRpcResult = eduHubRpcService.queryHubs(req);
                if (!hubRpcResult.checkSuccess()) {
                    log.error("invoke eduHubRpcService.queryHubs failed, req={}, messages={}",
                        req, hubRpcResult.getMessage());
                    break;
                }

                IPageResp<EduHubDto> pageResp = hubRpcResult.getData();
                if (pageResp != null && !CollectionUtils.isEmpty(pageResp.getPageRecords())) {
                    // 如果当前页小于总页数, 则继续查询
                    if (currentPageNum <= pageResp.getPageCount()) {
                        hasNextPage = true;
                    }
                    results.addAll(pageResp.getPageRecords());
                }
            } catch (Exception e) {
                log.error("invoke eduHubRpcService.queryHubs failed", e);
            }
        }

        return results.stream().map(item -> {
            HubBo bo = new HubBo();
            BeanUtils.copyProperties(item, bo);
            return bo;
        }).collect(Collectors.toList());
    }

    /**
     * 获取专辑库基础资源信息
     *
     * @param req 专辑请求
     * @return List<HubBo>
     */
    private List<HubBo> getHubBos(EduHubExtensionReq req) {
        IRpcResult<List<EduHubAggregationDto>> listHubRpcResult = eduHubRpcService.queryHubExtension(req);
        if (!listHubRpcResult.checkSuccess()) {
            log.error("invoke eduHubRpcService.queryHubExtension failed, messages={}", listHubRpcResult.getMessage());
            return Collections.emptyList();
        }

        return Optional.ofNullable(listHubRpcResult.getData()).orElse(Collections.emptyList())
            .stream().map(EduHubAggregationDto::getHub).collect(Collectors.toList())
            .stream()
            .map(item -> {
                HubBo bo = new HubBo();
                BeanUtils.copyProperties(item, bo);
                return bo;
            }).collect(Collectors.toList());
    }

    /**
     * 获取专辑库基础信息
     *
     * @param req 专辑库请求
     * @return Page<HubBo>
     */
    private Page<HubBo> getHubBoPages(EduHubReq req) {
        IRpcResult<IPageResp<EduHubDto>> hubRpcResult = eduHubRpcService.queryHubs(req);
        if (!hubRpcResult.checkSuccess()) {
            log.error("invoke eduHubRpcService.queryHubs failed, messages={}", hubRpcResult.getMessage());
            return new Page<>();
        }
        Optional<IPageResp<EduHubDto>> pageResult = Optional.ofNullable(hubRpcResult.getData());
        return new Page<HubBo>()
                .setRecords(pageResult
                        .map(IPageResp::getPageRecords).orElse(Collections.emptyList())
                        .stream()
                        .map(item -> {
                            HubBo bo = new HubBo();
                            BeanUtils.copyProperties(item, bo);
                            return bo;
                        }).collect(Collectors.toList()))
                .setTotal(pageResult.map(IPageResp::getTotalCount).orElse(0L))
                .setCurrent(pageResult.map(IPageResp::getPageNum).orElse(0L))
                .setSize(pageResult.map(IPageResp::getPageSize).orElse(0L));
    }

    /**
     * 数据转换，解析，由资源状态进行组装
     *
     * @param dto    聚合传输对象
     * @param status 资源状态，有效，无效
     * @return  EduHubAggregationBo
     */
    private EduHubAggregationBo convert(EduHubAggregationDto dto, ResourceStatus status) {
        return Optional.ofNullable(dto)
                .map(item -> {
                    HubBo hub = Optional.ofNullable(dto.getHub())
                            .map(i -> {
                                HubBo bo = new HubBo();
                                BeanUtils.copyProperties(i, bo);
                                return bo;
                            }).orElse(null);
                    List<EduAlbumAggregationBo> aggregationList = Optional.ofNullable(dto.getAlbumList())
                            .orElse(Collections.emptyList())
                            .stream()
                            .map(i -> {
                                AlbumBo album = Optional.ofNullable(i.getAlbum())
                                        .map(albumDto -> {
                                            AlbumBo bo = new AlbumBo();
                                            BeanUtils.copyProperties(albumDto, bo);
                                            return bo;
                                        }).orElse(null);
                                List<ResourcesBo> resourcesList = Optional.ofNullable(i.getResourcesList())
                                        .orElse(Collections.emptyList())
                                        .stream()
                                        .filter(o -> EnumUtil.isEnum(status) && status.getValue().equals(o.getStatus()))
                                        .map(res -> {
                                            ResourcesBo bo = new ResourcesBo();
                                            BeanUtils.copyProperties(res, bo);
                                            return bo;
                                        }).collect(Collectors.toList());
                                List<AlbumLabelBo> labels = Optional.ofNullable(i.getLabels())
                                        .orElse(Collections.emptyList())
                                        .stream()
                                        .map(label -> {
                                            AlbumLabelBo bo = new AlbumLabelBo();
                                            BeanUtils.copyProperties(label, bo);
                                            return bo;
                                        }).collect(Collectors.toList());
                                return new EduAlbumAggregationBo()
                                        .setAlbum(album)
                                        .setLabels(labels)
                                        .setLockSwitch(i.getLockSwitch())
                                        .setResourcesList(resourcesList);
                            }).collect(Collectors.toList());
                    return new EduHubAggregationBo()
                            .setHub(hub)
                            .setAlbumList(aggregationList);
                }).orElse(null);
    }

    private List<EduHubPageBo> convertEduHubPageBo(List<EduHubPageDto> list) {
        return list.stream().filter(Objects::nonNull)
                .map(t -> new EduHubPageBo().setId(t.getId()).setHubName(t.getHubName()).setHubPic(t.getHubPic())
                        .setHubRemark(t.getHubRemark())
                        .setHubType(Objects.nonNull(t.getHubType()) ? t.getHubType().getValue() : null)
                        .setContentType(t.getContentType()).setCourseId(t.getCourseId()).setStatus(t.getStatus())
                        .setSyncType(t.getSyncType()).setUnlockCondition(t.getUnlockCondition()).setPackId(t.getPackId())
                        .setExpiredTime(t.getExpiredTime()).setCreateTime(t.getCreateTime())
                        .setUpdateTime(t.getUpdateTime()).setCreateUser(t.getCreateUser())
                        .setDescription(t.getDescription()).setFreeResCount(t.getFreeResCount()).setCtr(t.getCtr())
                        .setRetention(t.getRetention()).setViewingTime(t.getViewingTime())
                        .setReadingVolume(t.getReadingVolume()).setAlbumIds(t.getAlbumIds())
                )
                .collect(Collectors.toList());
    }

}
