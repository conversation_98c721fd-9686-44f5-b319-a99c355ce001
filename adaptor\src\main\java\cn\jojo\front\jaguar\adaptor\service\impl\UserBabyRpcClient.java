package cn.jojo.front.jaguar.adaptor.service.impl;

import cn.jojo.crm.response.user.UserBaby;
import cn.jojo.front.jaguar.adaptor.service.IUserBabyRpcClient;
import cn.jojo.front.jaguar.common.pojo.bo.UserBabyBo;
import cn.jojo.front.jaguar.common.utils.ApolloUtil;
import cn.jojo.front.jaguar.common.utils.OssUrlUtils;
import cn.jojo.pagani.common.enums.BooleanStatus;
import cn.jojo.uc.api.domain.dto.UserBabyDto;
import cn.jojo.uc.api.domain.request.UserBabyInfoListReq;
import cn.jojo.uc.api.enums.UserServiceErrorStatus;
import cn.jojo.uc.api.service.UserBabyService;
import cn.jojo.uc.common.domain.dto.CommonRespDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Date;
import java.util.Objects;

/**
 * @description:
 * @author: luohuanrong
 * @create: 2023/6/6
 **/
@Slf4j
@Component
public class UserBabyRpcClient implements IUserBabyRpcClient {

    // 男宝宝默认头像
    public static final String BABY_DEFAULT_BOY_AVATAR_LINK = "tinman-oss://server-static-resource/avatar_default_boy_v1.png";
    // 女宝宝默认头像
    public static final String BABY_DEFAULT_GIRL_AVATAR_LINK = "tinman-oss://server-static-resource/avatar_default_girl_v1.png";
    //男宝宝性别标识
    private static final Integer BABY_SEX_BOY = 1;

    @DubboReference
    private UserBabyService userBabyService;

    @Override
    public UserBabyBo getUserBabyInfo(Long userId) {
        UserBabyDto userBabyDto = null;
        try {
            CommonRespDto<UserBabyDto, UserServiceErrorStatus> result = userBabyService.getUserBabyInfo(userId);
            if (result.isSuccess()) {
                userBabyDto = result.getData();
            } else {
                log.warn("调用uc查询宝贝信息失败, userId:{}, message:{}", userId, result.getMessage());
            }
        } catch (Exception e) {
            log.error("调用uc查询宝贝信息失败，req:{}, message:{}", userId, e.getMessage(), e);
        }
        if (Objects.isNull(userBabyDto)) {
            userBabyDto = getDefaultBabyDto(userId);
        }
        if (StringUtils.isEmpty(userBabyDto.getAvatar())) {
            userBabyDto.setAvatar(BABY_DEFAULT_BOY_AVATAR_LINK);
        }
        if (StringUtils.isEmpty(userBabyDto.getAvatarUrl())) {
            userBabyDto.setAvatarUrl(BABY_DEFAULT_BOY_AVATAR_LINK);
        }
        UserBabyBo userBabyBo = new UserBabyBo();
        BeanUtils.copyProperties(userBabyDto, userBabyBo);
        userBabyBo.setAvatarUrl(OssUrlUtils.genFileUrl(userBabyBo.getAvatarUrl()));
        return userBabyBo;
    }

    @Override
    public UserBabyBo getUserBabyInfoWithSensitive(Long userId, boolean needDesensitize) {
        UserBabyDto userBabyDto = null;
        try {
            UserBabyInfoListReq userBabyInfoListReq = new UserBabyInfoListReq();
            userBabyInfoListReq.setUserIds(Collections.singletonList(userId));
            userBabyInfoListReq.setNeedDesensitize(needDesensitize);

            CommonRespDto<UserBabyDto, UserServiceErrorStatus> respDto = userBabyService.getUserBabyInfoWithSensitive(userBabyInfoListReq);
            if (respDto.withoutData()) {
                log.info("宝宝信息查询失败：userId={}, message={}", userId, respDto.getMessage());
            } else {
                userBabyDto = respDto.getData();
            }
        } catch (Exception e) {
            log.error("宝宝信息查询失败：userId={}, message={}", userId, e.getMessage());
        }
        if (Objects.isNull(userBabyDto)) {
            userBabyDto = getDefaultBabyDto(userId);
        }
        if (StringUtils.isEmpty(userBabyDto.getAvatar())) {
            userBabyDto.setAvatar(BABY_DEFAULT_BOY_AVATAR_LINK);
        }
        if (StringUtils.isEmpty(userBabyDto.getAvatarUrl())) {
            userBabyDto.setAvatarUrl(BABY_DEFAULT_BOY_AVATAR_LINK);
        }
        UserBabyBo userBabyBo = new UserBabyBo();
        BeanUtils.copyProperties(userBabyDto, userBabyBo);
        userBabyBo.setAvatarUrl(OssUrlUtils.genFileUrl(userBabyBo.getAvatarUrl()));
        return userBabyBo;
    }

    private UserBabyDto getDefaultBabyDto(Long userId) {
        UserBabyDto userBabyDto = new UserBabyDto();
        userBabyDto.setSex(BABY_SEX_BOY);
        userBabyDto.setNickname(ApolloUtil.getStringMessage("biz.baby.default.name"));
        userBabyDto.setAvatar(BABY_DEFAULT_BOY_AVATAR_LINK);
        userBabyDto.setAvatarUrl(BABY_DEFAULT_BOY_AVATAR_LINK);
        userBabyDto.setBirthday(new Date());
        userBabyDto.setUserId(userId);
        userBabyDto.setGrade(-1);
        return userBabyDto;
    }
}
