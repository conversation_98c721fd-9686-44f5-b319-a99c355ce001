package cn.jojo.front.jaguar.admin.model.req.activity;

import cn.jojo.front.jaguar.common.enums.task.ActivityThemeSceneTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.List;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 活动主题操作对象
 *
 * <AUTHOR>
 * @since 2025/4/23 14:03
 */
@Data
@Builder
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "新增活动主题")
public class ActivityThemeSaveReq implements Serializable {


    /**
     * 主题名称
     */
    @Schema(description = "主题名称")
    @NotBlank(message = "themeName can not null")
    private String name;

    /**
     * 品类
     */
    @Schema(description = "品类")
    @NotNull(message = "subjectType can not null")
    private Integer subjectType;

    /**
     * 活动主题场景
     *
     * @see ActivityThemeSceneTypeEnum
     */
    @Schema(description = "活动主题场景", implementation = ActivityThemeSceneTypeEnum.class)
    @NotBlank(message = "scene can not null")
    private String scene;

    /**
     * 页面信息
     */
    @Schema(description = "页面信息")
    private List<ActivityThemePageSaveReq> pages;
}
