package cn.jojo.front.jaguar.admin.controller;

import cn.jojo.front.jaguar.common.pojo.req.PlateQueryReq;
import cn.jojo.front.jaguar.common.pojo.vo.PlatesVo;
import cn.jojo.front.jaguar.core.service.plate.PlateService;
import cn.jojo.infra.sdk.api.metadata.DefaultPageResp;
import cn.jojo.infra.sdk.api.metadata.IPageResp;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2025/4/10 14:47
 */
@RestController
@RequestMapping("/admin/plates")
@Tag(name = "Jaguar管理后台")
@Validated
public class PlateControllerV2 extends BaseController {


    @Autowired
    private PlateService plateService;

    /**
     * @Description: 查询板块列表
     * @author: xr
     * @date: 2021/4/9
     */
    @GetMapping
    @Operation(summary = "查询板块列表", description = "查询板块列表")
    public IPageResp<PlatesVo> getPlateList(PlateQueryReq<Void> req) {
        IPage<PlatesVo> page = plateService.getPlateList(req);
        return DefaultPageResp.buildPageResp(req, page.getTotal(), page.getRecords());
    }
}
