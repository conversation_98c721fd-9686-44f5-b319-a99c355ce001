package cn.jojo.front.jaguar.admin.model.convert;

import cn.jojo.edu.common.utils.convert.BaseModelConvert;
import cn.jojo.front.jaguar.admin.model.req.QueryConfigurationRequest;
import cn.jojo.front.jaguar.biz.service.pojo.bo.common.ActivityModuleTypeBo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 公共配置查询req 转换bo
 *
 * <AUTHOR>
 * @date 2022/05/16
 */
@Mapper
public interface ActivityModuleTypeReq2BoConvert extends BaseModelConvert<QueryConfigurationRequest, ActivityModuleTypeBo> {

    ActivityModuleTypeReq2BoConvert INSTANCE = Mappers.getMapper(ActivityModuleTypeReq2BoConvert.class);
}
