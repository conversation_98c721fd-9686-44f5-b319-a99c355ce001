package cn.jojo.front.jaguar.adaptor.service;


import cn.jojo.front.jaguar.common.bo.UserHub;
import cn.jojo.front.jaguar.common.pojo.bo.EduUserHubObtainBo;
import cn.jojo.front.jaguar.common.pojo.req.UserHubObtainListReq;

import java.util.List;
import java.util.Map;

public interface IEduUserHubRpcClient {


    /**
     * 查询用户专辑库关联关系
     *
     * @param userIds 用户ids
     * @param hubIds  专辑库id
     * @param status  状态
     * @return 关联关系列表
     */
    List<UserHub> queryUserHubList(List<Long> userIds, List<Long> hubIds, String status);

    /**
     * 查询用户专辑库获得来源信息
     * @param req 请求参数
     * @return  专辑库来源列表
     */
    List<EduUserHubObtainBo> queryUserHubObtainList(UserHubObtainListReq req);
}
