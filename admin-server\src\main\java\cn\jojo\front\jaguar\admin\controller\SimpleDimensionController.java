package cn.jojo.front.jaguar.admin.controller;

import cn.jojo.common.api.ex.DefaultHttpActionPageResult;
import cn.jojo.common.api.ex.IHttpActionResult;
import cn.jojo.front.jaguar.common.pojo.bo.PageBo;
import cn.jojo.front.jaguar.common.pojo.req.SimpleCommonDimensionBatchSaveReq;
import cn.jojo.front.jaguar.common.pojo.req.SimpleCommonDimensionListReq;
import cn.jojo.front.jaguar.common.pojo.vo.SimpleCommonDimensionVo;
import cn.jojo.front.jaguar.core.service.question.SimpleCommonDimensionService;
import cn.jojo.infra.sdk.api.metadata.IPageResp;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping(value = "admin")
@Tag(name = "Jaguar管理后台")
public class SimpleDimensionController extends BaseController {

    @Resource
    private SimpleCommonDimensionService simpleCommonDimensionService;


    @GetMapping("/dimensions")
    @Operation(summary = "获取维度列表", description = "获取维度列表")
    public IHttpActionResult<IPageResp<SimpleCommonDimensionVo>> getDimensionList(@Validated SimpleCommonDimensionListReq req) {
        PageBo<SimpleCommonDimensionVo> pageResult = simpleCommonDimensionService.getDimensionPage(req);
        return DefaultHttpActionPageResult.successWithPageData(pageResult.getPageNum(), pageResult.getPageSize(),
            pageResult.getTotal(), pageResult.getData());
    }

    @PostMapping("/dimensions/batch")
    @Operation(summary = "批量新增维度数据", description = "批量新增维度数据")
    public IHttpActionResult<Void> batchSave(@Validated @RequestBody SimpleCommonDimensionBatchSaveReq req) {
        simpleCommonDimensionService.batchInsertOrUpdate(req, getEmployeeId(), getEmployeeName());
        return DefaultHttpActionPageResult.successWithData(null);
    }

}
