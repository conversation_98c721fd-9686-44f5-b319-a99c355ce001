package cn.jojo.front.jaguar.adaptor.service.impl;


import cn.jojo.edu.malacca.rpc.api.dto.EduClassGroupDto;
import cn.jojo.edu.malacca.rpc.api.req.EduClassGroupIdsQueryReq;
import cn.jojo.edu.malacca.rpc.api.req.EduClassGroupQueryReq;
import cn.jojo.edu.malacca.rpc.api.service.EduClassGroupRpcService;
import cn.jojo.front.jaguar.adaptor.model.req.ClassGroupListReq;
import cn.jojo.front.jaguar.adaptor.service.IEduClassGroupRpcClient;
import cn.jojo.front.jaguar.common.pojo.vo.eduUserClass.EduClassGroupVo;
import cn.jojo.front.jaguar.common.utils.CustomBeanUtils;
import cn.jojo.infra.sdk.api.metadata.IRpcResult;
import cn.jojo.uc.common.exception.BusinessServiceException;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

@Component
@Slf4j
public class EduClassGroupRpcClient implements IEduClassGroupRpcClient {

    @Reference
    private EduClassGroupRpcService eduClassGroupRpcService;

    @Override
    public List<EduClassGroupVo> listGroups(ClassGroupListReq classGroupListReq) {

        EduClassGroupQueryReq req = CustomBeanUtils.copyProperties(classGroupListReq, EduClassGroupQueryReq::new);
        try {
            IRpcResult<List<EduClassGroupDto>> rpcResult = eduClassGroupRpcService.queryClassGroup(req);
            if (!rpcResult.checkSuccess()) {
                log.error("EduClassGroupRpcClient.listGroups failed, req = {}", req);
                throw new BusinessServiceException("EduClassGroupRpcClient.listGroups query rpc failed");
            }

            return rpcResult.getData().stream()
                .map(data -> CustomBeanUtils.copyProperties(data, EduClassGroupVo::new))
                .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("EduClassGroupRpcClient.listGroups failed", e);
            throw new BusinessServiceException("EduClassGroupRpcClient.listGroups failed" );
        }
    }


    @Override
    public List<EduClassGroupVo> queryClassGroupByIds(ClassGroupListReq classGroupListReq) {

        EduClassGroupIdsQueryReq req = CustomBeanUtils.copyProperties(classGroupListReq, EduClassGroupIdsQueryReq::new);
        try {
            IRpcResult<List<EduClassGroupDto>> rpcResult = eduClassGroupRpcService.queryClassGroupByIds(req);
            if (!rpcResult.checkSuccess()) {
                log.error("EduClassGroupRpcClient.queryClassGroupByIds failed, req = {}", req);
                throw new BusinessServiceException("EduClassGroupRpcClient.queryClassGroupByIds query rpc failed");
            }

            return rpcResult.getData().stream()
                .map(data -> CustomBeanUtils.copyProperties(data, EduClassGroupVo::new))
                .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("EduClassGroupRpcClient.queryClassGroupByIds failed", e);
            throw new BusinessServiceException("EduClassGroupRpcClient.queryClassGroupByIds failed" );
        }
    }
}
