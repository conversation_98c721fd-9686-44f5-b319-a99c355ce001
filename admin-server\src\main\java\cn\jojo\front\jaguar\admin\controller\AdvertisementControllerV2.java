package cn.jojo.front.jaguar.admin.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.jojo.common.api.ex.DefaultHttpActionResult;
import cn.jojo.common.api.ex.IHttpActionResult;
import cn.jojo.front.jaguar.common.annotation.Av1Transform;
import cn.jojo.front.jaguar.common.enums.MaterialConfigTypeEnum;
import cn.jojo.front.jaguar.common.pojo.bo.EmployeeBo;
import cn.jojo.front.jaguar.common.pojo.entity.advertisement.Advertisement;
import cn.jojo.front.jaguar.common.pojo.req.AdvertisementListReq;
import cn.jojo.front.jaguar.common.pojo.req.AdvertisementSaveReqV2;
import cn.jojo.front.jaguar.common.pojo.req.CommonUpdateReq;
import cn.jojo.front.jaguar.common.pojo.req.advertisement.AdvertisementSaveReqV3;
import cn.jojo.front.jaguar.common.pojo.vo.AdvertisementListVo;
import cn.jojo.front.jaguar.common.pojo.vo.AdvertisementVoV2;
import cn.jojo.front.jaguar.core.service.advertisement.AdvertisementService;
import cn.jojo.infra.sdk.api.metadata.DefaultHttpResult;
import cn.jojo.infra.sdk.api.metadata.DefaultPageResp;
import cn.jojo.infra.sdk.api.metadata.IHttpResult;
import cn.jojo.infra.sdk.api.metadata.IPageResp;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2024/1/25 20:56
 * @desc
 */
@RestController
@RequestMapping("/admin/advertisements")
@Tag(name = "Jaguar管理后台")
@Validated
public class AdvertisementControllerV2 extends BaseController {

    @Autowired
    private AdvertisementService advertisementService;

    /**
     * 保存广告
     *
     * @param req 保存参数
     * @return 广告ID
     */
    @PostMapping
    @Operation(summary = "创建新广告", description = "创建新广告")
    @Av1Transform
    public IHttpResult<Integer> saveAdvertisement(@RequestBody @Validated AdvertisementSaveReqV3 req) {
        AdvertisementSaveReqV2 saveReqV2 = BeanUtil.copyProperties(req, AdvertisementSaveReqV2.class);
        saveReqV2.setMaterialConfigType(MaterialConfigTypeEnum.REFERENCE);
        Advertisement advertisement = advertisementService.saveOrUpdateAdvertisementV2(saveReqV2,
            new EmployeeBo().setEmployeeId(getEmployeeId()).setEmployeeName(getEmployeeName()));
        return DefaultHttpResult.successWithData(advertisement.getId());
    }

    /**
     * 更新广告
     */
    @PutMapping("/{id}")
    @Operation(summary = "更新广告", description = "更新广告")
    @Av1Transform
    public IHttpResult<Integer> updateAdvertisement(@PathVariable(value = "id")
                                                    @NotNull(message = "Advertising ID is required and cannot be blank")
                                                    @Parameter(description = "广告id") Integer id,
                                                    @RequestBody @Validated AdvertisementSaveReqV3 req) {
        AdvertisementSaveReqV2 saveReqV2 = BeanUtil.copyProperties(req, AdvertisementSaveReqV2.class);
        saveReqV2.setMaterialConfigType(MaterialConfigTypeEnum.REFERENCE);
        saveReqV2.setId(id);
        Advertisement advertisement = advertisementService.saveOrUpdateAdvertisementV2(saveReqV2,
            new EmployeeBo().setEmployeeId(getEmployeeId()).setEmployeeName(getEmployeeName()));
        return DefaultHttpResult.successWithData(advertisement.getId());
    }


    /**
     * 广告详情
     */
    @GetMapping("/{id}")
    @Operation(summary = "广告详情", description = "广告详情")
    public IHttpResult<AdvertisementVoV2> detail(
        @PathVariable(value = "id") @NotNull @Parameter(description = "广告id") Integer id) {
        return DefaultHttpResult.successWithData(advertisementService.getAdvertisementInfoV2(id));
    }


    /**
     * 获取广告列表
     *
     * @param req 查询参数
     * @return 广告列表
     */
    @GetMapping
    @Operation(summary = "分页获取广告配置列表", description = "分页获取广告配置列表")
    public IPageResp<AdvertisementListVo> page(@Validated AdvertisementListReq<Void> req) {
        IPage<AdvertisementListVo> page = advertisementService.getAdvertisementList(req);
        return DefaultPageResp.buildPageResp(req, page.getTotal(), page.getRecords());
    }

    @PatchMapping("/{id}")
    @Operation(summary ="更新广告配置",   description = "更新广告规则配置部分信息")
    public IHttpActionResult<Boolean> update(@Validated @RequestBody CommonUpdateReq req, @PathVariable("id") Integer id) {
        EmployeeBo employeeBo = new EmployeeBo().setEmployeeId(getEmployeeId()).setEmployeeName(getEmployeeName());
        advertisementService.update(id, req, employeeBo);
        return DefaultHttpActionResult.successWithData(true);
    }
}
