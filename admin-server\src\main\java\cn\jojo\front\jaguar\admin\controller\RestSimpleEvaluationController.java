package cn.jojo.front.jaguar.admin.controller;

import cn.jojo.common.api.ex.DefaultHttpActionResult;
import cn.jojo.common.api.ex.IHttpActionResult;
import cn.jojo.front.jaguar.common.pojo.vo.SimpleQuestionDetailVo;
import cn.jojo.front.jaguar.core.service.impl.rating.RatingLinkRuleService;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/admin/simple-evaluations")
@Validated
public class RestSimpleEvaluationController {
    @Resource
    private RatingLinkRuleService ratingLinkRuleService;

    @GetMapping("/questions")
    @Operation(summary = "题目列表获取接口", description = "获取指定试卷下的所有题目")
    public IHttpActionResult<List<SimpleQuestionDetailVo>> questions(@NotNull Long evaluationId) {
        List<SimpleQuestionDetailVo> result = ratingLinkRuleService.ratingQuestionList(evaluationId);
        return DefaultHttpActionResult.successWithData(result);
    }
}
