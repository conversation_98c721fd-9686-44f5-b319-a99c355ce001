package cn.jojo.front.jaguar.admin.controller;

import cn.jojo.front.jaguar.common.enums.MaterialFormatEnum;
import cn.jojo.front.jaguar.common.enums.PopupType;
import cn.jojo.front.jaguar.common.pojo.bo.EmployeeBo;
import cn.jojo.front.jaguar.common.pojo.entity.popup.Popup;
import cn.jojo.front.jaguar.common.pojo.req.PopupListReq;
import cn.jojo.front.jaguar.common.pojo.req.PopupSaveReq;
import cn.jojo.front.jaguar.common.pojo.vo.PopupDetailVo;
import cn.jojo.front.jaguar.common.pojo.vo.PopupListVo;
import cn.jojo.front.jaguar.core.service.popup.PopupService;
import cn.jojo.infra.sdk.api.metadata.DefaultHttpResult;
import cn.jojo.infra.sdk.api.metadata.DefaultPageResp;
import cn.jojo.infra.sdk.api.metadata.IHttpResult;
import cn.jojo.infra.sdk.api.metadata.IPageResp;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * 2024/6/17 13:53
 */

@RestController
@RequestMapping("/admin/popups")
@Tag(name = "Jaguar管理后台")
@Validated
public class PopupControllerV2 extends BaseController {
    @Resource
    private PopupService popupService;

    @GetMapping
    @Operation(summary ="弹窗列表",   description = "获取弹窗列表")
    public IPageResp<PopupListVo> getPopupList(PopupListReq<Void> req) {
        IPage<PopupListVo> page = popupService.listPopup(req);
        return DefaultPageResp.buildPageResp(req, page.getTotal(), page.getRecords());
    }

    @GetMapping("/{id}")
    @Operation(summary ="弹窗详情",   description = "获取弹窗详情")
    public IHttpResult<PopupDetailVo> getPopupInfo(@PathVariable("id") @NotNull Integer popupId) {
        List<PopupDetailVo> list = popupService.listPopupDetailByIds(Collections.singleton(popupId));
        return DefaultHttpResult.successWithData(CollectionUtils.isEmpty(list) ? null : list.get(0));
    }

    @PostMapping
    @Operation(summary ="弹窗保存",   description = "保存弹窗")
    public IHttpResult<Integer> savePopup(@RequestBody @Validated PopupSaveReq req) {
        //兼容前端未发布的场景，后面可以删掉
        if (req.getMaterialFormat() == null) {
            req.setMaterialFormat(MaterialFormatEnum.PICTURE.getCode());
        }
        
        req.validateSelf();
        Popup popup = popupService.saveOrUpdatePopup(req, new EmployeeBo()
            .setEmployeeId(getEmployeeId())
            .setEmployeeName(getEmployeeName()));
        popupService.deleteCache(Collections.singleton(popup.getId()), PopupType.of(popup.getPopupKey()));
        popupService.removeUserPopup(popup);
        return DefaultHttpResult.successWithData(popup.getId());
    }

}
