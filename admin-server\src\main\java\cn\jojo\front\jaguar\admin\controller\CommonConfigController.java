package cn.jojo.front.jaguar.admin.controller;

import cn.jojo.common.api.ex.DefaultHttpActionResult;
import cn.jojo.common.api.ex.IHttpActionResult;
import cn.jojo.front.jaguar.biz.service.option.OptionBizService;
import cn.jojo.front.jaguar.biz.service.option.OptionContext;
import cn.jojo.front.jaguar.common.enums.CommonConfigScene;
import cn.jojo.front.jaguar.common.exception.BusinessException;
import cn.jojo.front.jaguar.common.pojo.vo.CommonConfigVo;
import cn.jojo.infra.sdk.api.constants.ApiResultPlatformCodeConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotNull;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping(value = "admin/common-configs")
@Tag(name = "Jaguar管理后台-通用配置(restFull接口)")
public class CommonConfigController {

    @Autowired
    private List<OptionBizService<CommonConfigVo>> optionBizServices;

    @GetMapping("/{scene}")
    @Operation(summary = "通过场景值查询配置列表", description = "通过场景值查询配置列表")
    public IHttpActionResult<List<CommonConfigVo>> getConfigList(@Parameter(description = "场景值") @PathVariable @NotNull String scene,
                                                                 @Parameter(description = "场景对应必须参数") @RequestParam(value = "param", required = false) String param) {
        CommonConfigScene.of(scene).orElseThrow(() -> new BusinessException(ApiResultPlatformCodeConstants.BIZ_ERROR, "Unknown scene Value"));
        OptionContext context = new OptionContext();
        context.setParam(param);
        List<CommonConfigVo> optionList = getOptionBizService(scene).getOptionList(context);
        return DefaultHttpActionResult.successWithData(optionList);
    }

    /**
     * 获取下拉选项列表
     *
     * @param scene 下拉选项键值
     * @return 下拉选项列表
     */
    private <T extends CommonConfigVo> OptionBizService<T> getOptionBizService(String scene) {
        return Optional.ofNullable(optionBizServices)
            .orElse(Collections.emptyList())
            .stream()
            .filter(service -> service.getScene() == CommonConfigScene.of(scene).get())
            .map(service -> (OptionBizService<T>) service)
            .findFirst()
            .orElseThrow(() -> new BusinessException(ApiResultPlatformCodeConstants.BIZ_ERROR, "未知的场景值"));
    }

}
