package cn.jojo.front.jaguar.admin.model.dto.task.activity;

import cn.jojo.front.jaguar.common.enums.task.ActivityThemeSceneTypeEnum;
import cn.jojo.infra.sdk.openapi.annotation.Privacy;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 活动主题
 *
 * <AUTHOR>
 * @since 2025/4/22 15:22
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "主题分页列表对象")
public class ActivityThemePageDto implements Serializable {


    /**
     * 主键id
     */
    @Schema(description = "主题Id")
    private Long id;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private Long updateTime;

    /**
     * 主题名称
     */
    @Schema(description = "主题名称")
    private String name;

    /**
     * 品类
     */
    @Schema(description = "品类")
    private Integer subjectType;

    /**
     * 品类名称
     */
    @Schema(description = "品类名称")
    private String subjectTypeName;

    /**
     * 活动主题场景
     *
     * @see ActivityThemeSceneTypeEnum
     */
    @Schema(description = "活动主题场景", implementation = ActivityThemeSceneTypeEnum.class)
    private String scene;


    /**
     * 场景名称
     */
    @Schema(description = "场景名称")
    private String sceneName;

    /**
     * 更新者
     */
    @Schema(description = "更新者Id")
    private Long updator;

    /**
     * 更新者名称
     */
    @Schema(description = "更新者名称")
    @Privacy
    private String updatorName;
}
