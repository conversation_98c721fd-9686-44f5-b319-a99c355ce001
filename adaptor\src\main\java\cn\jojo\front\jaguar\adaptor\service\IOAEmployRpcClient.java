package cn.jojo.front.jaguar.adaptor.service;

import cn.jojo.front.jaguar.common.pojo.vo.OAEmployeeVo;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/02/06
 **/
public interface IOAEmployRpcClient {

    List<OAEmployeeVo> searchEmployees(List<Long> employeeIds);

    OAEmployeeVo searchEmployee(Long employeeId);

    IPage<OAEmployeeVo> searchEmployeesByPage(IPage<OAEmployeeVo> req, String username);

    Long currentEmployeeId();

    /**
     * 查询员工下属
     *
     * @param employeeId
     * @return
     */
    List<OAEmployeeVo> searchSubordinates(Long employeeId);
}
