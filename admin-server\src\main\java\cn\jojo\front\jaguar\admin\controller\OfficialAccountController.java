package cn.jojo.front.jaguar.admin.controller;

import cn.jojo.front.jaguar.adaptor.service.IOfficialAccountRpcClient;
import cn.jojo.front.jaguar.common.pojo.bo.PageBo;
import cn.jojo.front.jaguar.common.pojo.req.OfficialAccountReq;
import cn.jojo.front.jaguar.common.pojo.vo.OfficialAccountVo;
import cn.jojo.infra.sdk.api.metadata.DefaultHttpPageResult;
import cn.jojo.infra.sdk.api.metadata.IHttpResult;
import cn.jojo.infra.sdk.api.metadata.IPageResp;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping(value = "/admin/wechat-official-accounts")
@Tag(name = "Jaguar管理后台-公众号(restFull接口)")
@Validated
public class OfficialAccountController {
    @Resource
    private IOfficialAccountRpcClient officialAccountRpcClient;
    @Operation(summary = "获取操作", description = "分页获取CRM公众号列表")
    @GetMapping
    public IHttpResult<IPageResp<OfficialAccountVo>> list(OfficialAccountReq req) {
        if (req.getPageNum() == null) {
            req.setPageNum(1L);
        }
        if (req.getPageSize() == null) {
            req.setPageSize(20L);
        }
        PageBo<OfficialAccountVo> result =
            officialAccountRpcClient.listOfficialAccount(req.getKeyword(), req.getPageNum(), req.getPageSize());
        return DefaultHttpPageResult
            .successWithPageData(result.getPageNum(), result.getPageSize(), result.getTotal(), result.getData());
    }
}
