package cn.jojo.front.jaguar.admin.model.dto.task;

import cn.jojo.front.jaguar.common.enums.task.SpecialHolidayEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2024/03/20
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema(description="条件范围")
public class EventScopeDto {

    @Schema(description="课时id")
    private String lessonId;
    @Schema(description="开始时间 HH:mm:ss")
    private String startDateTimeOfDay;
    @Schema(description="结束时间 HH:mm:ss")
    private String endDateTimeOfDay;
    @Schema(description="目标值")
    private String targetValue;
    @Schema(description="特殊节日",implementation = SpecialHolidayEnum.class)
    private String specialHoliday;
    @Schema(description="课时id 列表")
    private List<String> lessonIds;
    @Schema(description="主题月id 列表")
    private List<Long> themeMonthIds;
    @Schema(description="日常任务-课时id列表")
    private List<String> dailyLessonIds;
}
