package cn.jojo.front.jaguar.adaptor.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.jojo.cc.api.domain.request.CourseInfoReq;
import cn.jojo.cc.api.domain.request.CourseSearchReq;
import cn.jojo.cc.api.domain.request.SegmentListByCourseIdReq;
import cn.jojo.cc.api.service.BasicService;
import cn.jojo.cc.api.service.CourseSegmentService;
import cn.jojo.cc.api.service.CourseService;
import cn.jojo.cc.api.service.SubjectService;
import cn.jojo.cc.common.dto.CourseInfoDto;
import cn.jojo.cc.common.dto.CourseSearchDto;
import cn.jojo.cc.common.dto.SegmentDto;
import cn.jojo.cc.common.dto.SubjectTypeDto;
import cn.jojo.edu.malacca.rpc.api.dto.EduClassAggregationDto;
import cn.jojo.edu.malacca.rpc.api.dto.EduClassDto;
import cn.jojo.edu.malacca.rpc.api.dto.EduClassTeacherDto;
import cn.jojo.edu.malacca.rpc.api.dto.EduTeacherDto;
import cn.jojo.edu.malacca.rpc.api.req.EduClassAggregationQueryReq;
import cn.jojo.edu.malacca.rpc.api.req.EduClassQueryReq;
import cn.jojo.edu.malacca.rpc.api.req.EduClassTeacherQueryReq;
import cn.jojo.edu.malacca.rpc.api.req.EduTeacherQueryReq;
import cn.jojo.edu.malacca.rpc.api.service.EduClassRpcService;
import cn.jojo.edu.malacca.rpc.api.service.EduClassTeacherRpcService;
import cn.jojo.edu.malacca.rpc.api.service.EduTeacherRpcService;
import cn.jojo.front.jaguar.adaptor.service.ConditionParamService;
import cn.jojo.front.jaguar.common.exception.SystemException;
import cn.jojo.front.jaguar.common.pojo.vo.CourseSearchVo;
import cn.jojo.front.jaguar.common.pojo.vo.CourseSegmentListVo;
import cn.jojo.front.jaguar.common.pojo.vo.PageJumpVo;
import cn.jojo.front.jaguar.common.utils.ApolloUtil;
import cn.jojo.infra.sdk.api.metadata.IPageResp;
import cn.jojo.infra.sdk.api.metadata.IRpcResult;
import cn.jojo.uc.api.domain.dto.DictDto;
import cn.jojo.uc.api.enums.CommonServiceErrorStatus;
import cn.jojo.uc.api.service.CommonService;
import cn.jojo.uc.common.domain.dto.CommonRespDto;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class ConditionParamServiceImpl implements ConditionParamService {

    @Reference
    private CourseService courseService;
    @Reference
    private BasicService basicService;
    @Reference(tag = "oa-admin", timeout = 5000)
    private EduClassRpcService eduClassRpcService;
    @Reference
    private EduTeacherRpcService eduTeacherRpcService;
    @Reference
    private EduClassTeacherRpcService eduClassTeacherRpcService;
    @Reference
    private SubjectService subjectService;
    @Reference
    private CourseSegmentService courseSegmentService;
    @Reference
    private CommonService commonService;

    /**
     * 年级字典类型
     */
    private static final int DICT_TYPE_GRADE = 1;

    @Value("${batch.query.class.switch:true}")
    private Boolean batchQueryClassSwitch;

    @Override
    public List<SubjectTypeDto> getAllSubjectType() {
        IRpcResult<List<SubjectTypeDto>> result = basicService.getAllSubjectType();
        if (!result.checkSuccess()) {
            throw new SystemException(ApolloUtil.getStringMessage("error.invoke.remote.fail"));
        }
        return result.getData();
    }

    @Override
    public List<CourseSearchVo> listCourse(Integer courseType, Integer subCourseType, Integer subjectType,
                                           List<Integer> courseSegmentCodes, String keyword) {
        CourseSearchReq req = new CourseSearchReq();
        req.setCourseType(courseType);
        req.setCourseL2Type(subCourseType);
        req.setSubjectType(subjectType);

        List<CourseSearchVo> result;
        if (courseType == null && subjectType == null) {
            CourseInfoReq courseInfoReq = new CourseInfoReq();
            IRpcResult<List<CourseInfoDto>> rpcResult = courseService.listCourseInfo(courseInfoReq);
            result = parseCourseResult(rpcResult);
        } else {
            IRpcResult<List<CourseSearchDto>> rpcResult = courseService.searchCourseForSimple(req);
            result = parseCourseResult(rpcResult);
        }

        if (!StringUtils.isEmpty(keyword)) {
            return result.stream()
                .filter(item -> item.getCourseKey().contains(keyword) || item.getCourseName().contains(keyword))
                .collect(Collectors.toList());
        }
        if (CollUtil.isNotEmpty(courseSegmentCodes)) {
            return result.stream()
                .filter(item -> courseSegmentCodes.contains(item.getCourseSegmentCode()))
                .collect(Collectors.toList());
        }
        return result;
    }

    private static <T> List<CourseSearchVo> parseCourseResult(IRpcResult<List<T>> rpcResult) {
        if (!rpcResult.checkSuccess()) {
            throw new SystemException(ApolloUtil.getStringMessage("error.search.lesson.fail"));
        }
        if (CollectionUtils.isEmpty(rpcResult.getData())) {
            return Collections.emptyList();
        }
        return rpcResult.getData().stream().map(data -> {
            CourseSearchVo vo = new CourseSearchVo();
            BeanUtils.copyProperties(data, vo);
            return vo;
        }).collect(Collectors.toList());
    }

    @Override
    public List<EduClassDto> listClass(List<Long> courseIds, List<Long> classIds) {
        if (batchQueryClassSwitch) {
            return batchQueryClass(courseIds, classIds);
        }
        EduClassQueryReq req = new EduClassQueryReq();
        req.setCourseIds(courseIds);
        req.setClassIds(classIds);
        IRpcResult<List<EduClassDto>> result = eduClassRpcService.queryClasses(req);
        if (!result.checkSuccess()) {
            throw new SystemException(ApolloUtil.getStringMessage("error.search.class.fail"));
        }
        return result.getData();
    }

    private List<EduClassDto> batchQueryClass(List<Long> courseIds, List<Long> classIds) {
        EduClassQueryReq req = new EduClassQueryReq();
        req.setCourseIds(courseIds);
        req.setClassIds(classIds);

        long pageNum = 1;
        long pageSize = 500;
        EduClassAggregationQueryReq query = EduClassAggregationQueryReq.builder()
                .eduClassQueryReq(req)
                .pageNum(pageNum)
                .pageSize(pageSize)
                .build();
        List<EduClassDto> resultList = new ArrayList<>();
        while (true) {
            IRpcResult<IPageResp<EduClassAggregationDto>> rpcResult = eduClassRpcService
                    .queryClassAggregationByPage(query);
            if (!rpcResult.checkSuccess()) {
                log.error("查询班级列表失败,message={}", rpcResult.getMessage());
                break;
            }

            List<EduClassAggregationDto> dataList = Optional.ofNullable(rpcResult.getData())
                    .map(IPageResp::getPageRecords).orElse(Collections.emptyList());
            if (CollectionUtils.isEmpty(dataList)) {
                break;
            }
            List<EduClassDto> classList = dataList.stream().map(EduClassAggregationDto::getEduClassDto)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            resultList.addAll(classList);
            pageNum++;
            query.setPageNum(pageNum);
        }
        log.info("totalSize:{}", resultList.size());
        return resultList;
    }

    @Override
    public List<SegmentDto> listSubject(Integer courseId) {
        SegmentListByCourseIdReq req = new SegmentListByCourseIdReq();
        req.setCourseId(Long.valueOf(courseId));
        IRpcResult<List<SegmentDto>> result = courseSegmentService.listByCourseId(req);
        if (!result.checkSuccess()) {
            throw new SystemException(ApolloUtil.getStringMessage("error.search.topic.fail"));
        }
        return result.getData();
    }

    @Override
    public List<PageJumpVo> listPageUrl() {
        return ApolloUtil.getMessage("page.jump.info", new TypeReference<List<PageJumpVo>>() {
        })
            .orElse(Collections.emptyList());
    }

    @Override
    public List<EduTeacherDto> listTeacher(List<Integer> classIds) {
        if (CollectionUtils.isEmpty(classIds)) {
            return Lists.newArrayList();
        }
        List<Long> longClassIds = classIds.stream().map(Integer::longValue).collect(Collectors.toList());
        IRpcResult<List<EduClassTeacherDto>> iRpcResult = eduClassTeacherRpcService
            .queryClassTeachers(EduClassTeacherQueryReq.builder().classIds(longClassIds).build());
        if (!iRpcResult.checkSuccess()) {
            throw new SystemException(ApolloUtil.getStringMessage("error.search.teacher.fail"));
        }
        if (CollectionUtils.isEmpty(iRpcResult.getData())) {
            return Lists.newArrayList();
        }
        List<Long> teacherIds = iRpcResult.getData().stream().map(EduClassTeacherDto::getTeacherId).distinct()
            .collect(Collectors.toList());
        return Optional.ofNullable(eduTeacherRpcService.queryTeachers(EduTeacherQueryReq.builder()
            .teacherId(teacherIds).build()).getData()).orElse(Collections.emptyList())
            .stream().peek(teacher -> teacher.setWechatId(null)).collect(Collectors.toList());
    }

    @Override
    public List<DictDto> listGrade() {
        CommonRespDto<List<DictDto>, CommonServiceErrorStatus> resp = null;
        try {
            resp = commonService.listByType(DICT_TYPE_GRADE);
            if (!resp.isSuccess()) {
                log.warn("调用用户中心获取年级字典信息失败:{}", resp.getMessage());
                return Collections.emptyList();
            }
        } catch (Exception e) {
            log.error("调用用户中心获取年级字典信息失败", e);
            return Collections.emptyList();
        }
        return resp.getData();
    }
}
