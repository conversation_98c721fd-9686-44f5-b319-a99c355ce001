package cn.jojo.front.jaguar.adaptor.service.impl;

import cn.jojo.dayu.rpc.api.dto.UserCustomSettingsDto;
import cn.jojo.dayu.rpc.api.req.UserCustomSettingReq;
import cn.jojo.dayu.rpc.api.req.UserPlateRecommendOpenSaveReq;
import cn.jojo.dayu.rpc.api.service.UserCustomSettingsRpcService;
import cn.jojo.front.jaguar.adaptor.service.IFbUserCustomSettingsRpcClient;
import cn.jojo.infra.sdk.api.metadata.IRpcResult;
import cn.jojo.infra.sdk.logging.JoJoLogging;
import com.alibaba.fastjson2.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * @description:
 * @author: luohuanrong
 * @create: 2023/5/26
 **/

@Slf4j
@Component
public class FbUserCustomSettingsRpcClient implements IFbUserCustomSettingsRpcClient {

    @DubboReference
    private UserCustomSettingsRpcService userCustomSettingsRpcService;

    @Override
    public Boolean hasUserPlateRecommendOpen(Long userId) {
        try {
            UserCustomSettingReq req = new UserCustomSettingReq().setUserId(userId);
            IRpcResult<UserCustomSettingsDto> rpcResult = userCustomSettingsRpcService.getUserCustomSettings(req);
            if (log.isDebugEnabled()) {
                JoJoLogging.logger(log)
                    .unIndex("req", JSON.toJSONString(req))
                    .unIndex("rpcResult", JSON.toJSONString(rpcResult))
                    .debug("plate recommend hasUserPlateRecommendOpen");
            }
            if (!rpcResult.checkSuccess() || Objects.isNull(rpcResult.getData())) {
                return Boolean.TRUE;
            }
            UserCustomSettingsDto settingsDto = rpcResult.getData();
            if (Objects.nonNull(settingsDto) && Objects.nonNull(settingsDto.getSettings())
                && Objects.nonNull(settingsDto.getSettings().getPlateRecommendOpen())) {
                return settingsDto.getSettings().getPlateRecommendOpen();
            }
        } catch (Exception e) {
            log.error("plate recommend hasUserPlateRecommendOpen error", e);
        }
        return Boolean.TRUE;
    }

    @Override
    public void saveUserPlateRecommendOpen(Long userId, Boolean plateRecommendOpen) {
        try {
            UserPlateRecommendOpenSaveReq req = new UserPlateRecommendOpenSaveReq()
                    .setUserId(userId).setPlateRecommendOpen(plateRecommendOpen);
            IRpcResult<Void> rpcResult = userCustomSettingsRpcService.saveUserPlateRecommendOpen(req);
            JoJoLogging.logger(log)
                    .unIndex("req", JSON.toJSONString(req))
                    .unIndex("rpcResult", JSON.toJSONString(rpcResult))
                    .info("plate recommend saveUserPlateRecommendOpen");
        } catch (Exception e) {
            log.error("plate recommend saveUserPlateRecommendOpen error", e);
        }
    }
}
