package cn.jojo.front.jaguar.admin.model.dto.task;

import cn.jojo.front.jaguar.common.enums.YesOrNoEnum;
import cn.jojo.front.jaguar.common.enums.task.RewardTypeEnum;
import cn.jojo.front.jaguar.common.enums.task.TaskRewardTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description
 * @date 2024/03/11
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema(description="关联外部奖励查询结果详细信息")
public class ThirdRewardQueryDto {


    @Schema(description="奖励类型", implementation = RewardTypeEnum.class)
    private Integer rewardType;

    @Schema(description="奖励id：勋章id,心愿礼物id")
    private Long rewardId;

    @Schema(description="奖励id对应的KEY，比如：卡牌ID对应的卡牌Key， 或其他id 对应的key，方便前端老师看的描述转换")
    private String rewardKey;

    @Schema(description="奖励名称，比如：卡牌对应的卡牌名称")
    private String rewardName;

}
