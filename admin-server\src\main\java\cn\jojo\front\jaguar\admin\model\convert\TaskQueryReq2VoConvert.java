package cn.jojo.front.jaguar.admin.model.convert;

import cn.jojo.edu.common.utils.convert.BaseModelConvert;
import cn.jojo.front.jaguar.admin.model.convert.decorator.TaskSaveReq2VoConvertDecorator;
import cn.jojo.front.jaguar.admin.model.req.task.TaskPageReq;
import cn.jojo.front.jaguar.admin.model.req.task.TaskSaveReq;
import cn.jojo.front.jaguar.biz.service.pojo.bo.task.TaskQueryVo;
import cn.jojo.front.jaguar.biz.service.pojo.bo.task.TaskVo;
import org.mapstruct.DecoratedWith;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * vo转换
 *
 * <AUTHOR>
 * @date 2022/05/16
 */
@Mapper
public interface TaskQueryReq2VoConvert extends BaseModelConvert<TaskPageReq, TaskQueryVo> {

    TaskQueryReq2VoConvert INSTANCE = Mappers.getMapper(TaskQueryReq2VoConvert.class);

}
