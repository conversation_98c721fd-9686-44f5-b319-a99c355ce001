package cn.jojo.front.jaguar.admin.controller.activity;


import cn.jojo.front.jaguar.admin.controller.BaseController;
import cn.jojo.front.jaguar.admin.model.convert.ClassScopesQueryReq2BoConvert;
import cn.jojo.front.jaguar.admin.model.req.activity.ClassScopesQueryReq;
import cn.jojo.front.jaguar.biz.service.activitity.IActivitiesBizService;
import cn.jojo.front.jaguar.biz.service.pojo.bo.activity.ClassScopesQueryBo;
import cn.jojo.front.jaguar.common.pojo.vo.activity.ClassScopesListAdminVo;
import cn.jojo.infra.sdk.api.metadata.DefaultHttpResult;
import cn.jojo.infra.sdk.api.metadata.IHttpResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Validated
@RestController
@RequestMapping("admin/activity-scope-classes")
@Tag(name = "活动投放班级")
public class ScopeClassesController extends BaseController {

    @Resource
    private IActivitiesBizService activitiesBizService;

    @Operation(summary = "活动投放班级查询 ", description = "活动投放班级查询")
    @GetMapping()
    public IHttpResult<ClassScopesListAdminVo> listScopesCandidates(@ModelAttribute
                                                                    ClassScopesQueryReq classScopesQueryReq) {

        ClassScopesQueryBo bo = ClassScopesQueryReq2BoConvert.INSTANCE.model1ToModel2(classScopesQueryReq);

        return DefaultHttpResult.successWithData(activitiesBizService.listClassScopes(bo));
    }
}
