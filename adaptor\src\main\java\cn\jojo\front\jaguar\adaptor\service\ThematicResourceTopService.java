package cn.jojo.front.jaguar.adaptor.service;

import cn.jojo.edu.fantasy.rpc.api.dto.EduTopicCardDto;
import cn.jojo.front.jaguar.common.pojo.bo.ThematicBo;

import java.util.List;

/**
 * @Description: 专题卡片专辑信息数据
 * @Copyright: Copyright (c) 2022  ALL RIGHTS RESERVED.
 * @Company: 书声科技有限公司
 * @Author: chenHao
 * @CreateDate: 2022/9/27 14:12
 * @UpdateDate: 2022/9/27 14:12
 * @UpdateRemark: init
 * @Version: 1.0
 */
public interface ThematicResourceTopService {

    /**
     * 检查是否有效专题
     *
     * @param thematicList 专题信息
     * @return 专题列表
     */
    List<EduTopicCardDto> checkValidThematicResourceTopInfo(List<ThematicBo> thematicList);
}
