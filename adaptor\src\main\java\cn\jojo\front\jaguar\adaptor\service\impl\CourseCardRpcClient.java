package cn.jojo.front.jaguar.adaptor.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.jojo.cc.api.domain.request.CardPropQueryReq;
import cn.jojo.cc.common.dto.CardPropDto;
import cn.jojo.cc.common.dto.StickerPageDto;
import cn.jojo.front.jaguar.adaptor.service.ICourseCardRpcClient;
import cn.jojo.front.jaguar.common.pojo.req.course.CardReq;
import cn.jojo.front.jaguar.common.pojo.vo.activity.CardVo;

import cn.jojo.infra.sdk.api.metadata.IRpcResult;
import cn.jojo.uc.common.exception.BusinessServiceException;
import com.google.api.client.util.Lists;
import com.google.common.collect.Sets;

import java.util.List;

import java.util.Map;

import java.util.stream.Collectors;

import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import cn.jojo.cc.api.service.CardPropAppService;

/**
 * CC 的活动卡牌，用于激励系统
 *
 * <AUTHOR>
 * @date 2024/12/09
 **/
@Slf4j
@Component
public class CourseCardRpcClient implements ICourseCardRpcClient {
    @DubboReference
    private CardPropAppService cardPropAppService;
    @Override
    public CardVo getCardByKey(CardReq req) {
        if (ObjectUtil.isEmpty(req.getCardKey())) {
            return null;
        }
        IRpcResult<Map<String, CardPropDto>> result = cardPropAppService
            .queryCardPropByKey(CardPropQueryReq.builder().cardKeys(Sets.newHashSet(req.getCardKey())).prod(req.isProd()).build());
        if (ObjectUtil.isEmpty(result) || !result.checkSuccess()) {
            log.error("invoke cardPropAppService.queryCardPropByKey failed, message={}",
                result.getMessage());
            throw new BusinessServiceException("invoke cardPropAppService.queryCardPropByKey failed");
        }
        CardPropDto cardPropDto = result.getData().getOrDefault(req.getCardKey(), null);
        if (ObjectUtil.isEmpty(cardPropDto)) {
            log.error("invoke ICourseCardRpcClient.getCardByKey failed, message={}",
                result.getMessage());
            return null;
        }
        return CardVo.builder()
            .cardKey(cardPropDto.getCardKey())
            .name(cardPropDto.getName())
            .id(cardPropDto.getId()).build();
    }

    @Override
    public List<CardVo> getCardByIds(CardReq req) {

        if (ObjectUtil.isEmpty(req.getCardIds())) {
            return Lists.newArrayList();
        }
        //将入参的cardIds转成set
        CardPropQueryReq queryReq = CardPropQueryReq.builder().cardIds(Sets.newHashSet(req.getCardIds())).prod(req.isProd()).build();
        IRpcResult<Map<Long, CardPropDto>> result = cardPropAppService
            .queryCardPropById(queryReq);
        if (ObjectUtil.isEmpty(result) || !result.checkSuccess()) {
            log.error("invoke cardPropAppService.queryCardPropById failed, message={}",
                result.getMessage());
            throw new BusinessServiceException("invoke cardPropAppService.queryCardPropById failed");
        }

        Map<Long, CardPropDto> data = result.getData();
        if (ObjectUtil.isEmpty(data)) {
            log.error("invoke ICourseCardRpcClient.getCardByIds failed, message={}", result.getMessage());
            return Lists.newArrayList();
        }
        return data.values().stream().map(cardPropDto->CardVo.builder()
            .cardKey(cardPropDto.getCardKey())
            .name(cardPropDto.getName())
            .id(cardPropDto.getId()).build()).collect(Collectors.toList());
    }
}
