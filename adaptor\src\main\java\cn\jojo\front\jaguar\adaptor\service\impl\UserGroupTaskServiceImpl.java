package cn.jojo.front.jaguar.adaptor.service.impl;

import cn.jojo.fb.tag.rpc.api.domain.dto.UserGroupSyncResultDto;
import cn.jojo.fb.tag.rpc.api.domain.dto.UserGroupTaskListDto;
import cn.jojo.fb.tag.rpc.api.domain.req.FbTagUserGroupTaskRecordListReq;
import cn.jojo.fb.tag.rpc.api.service.FbTagUserGroupTaskService;
import cn.jojo.front.jaguar.adaptor.service.UserGroupTaskService;
import cn.jojo.front.jaguar.common.exception.BusinessException;
import cn.jojo.front.jaguar.common.exception.SystemException;
import cn.jojo.front.jaguar.common.pojo.bo.PageResultBo;
import cn.jojo.front.jaguar.common.pojo.req.UserGroupTaskRecordListReq;
import cn.jojo.front.jaguar.common.pojo.vo.UserGroupSyncResultVo;
import cn.jojo.front.jaguar.common.pojo.vo.UserGroupTaskListVo;
import cn.jojo.infra.sdk.api.constants.ApiResultPlatformCodeConstants;
import cn.jojo.infra.sdk.api.metadata.IPageResp;
import cn.jojo.infra.sdk.api.metadata.IRpcResult;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class UserGroupTaskServiceImpl implements UserGroupTaskService {

    @DubboReference
    private FbTagUserGroupTaskService fbTagUserGroupTaskService;

    @Override
    public UserGroupSyncResultVo syncNow(Long id) {
        IRpcResult<UserGroupSyncResultDto> rpcResult = fbTagUserGroupTaskService.syncNow(id);
        if (!rpcResult.checkSuccess()) {
            log.error("invoke fbTagUserGroupTaskService.syncNow failed, message={}", rpcResult.getMessage());
            throw new SystemException("system error");
        }
        return Optional.ofNullable(rpcResult.getData())
                .map(item -> {
                    UserGroupSyncResultVo vo = new UserGroupSyncResultVo();
                    BeanUtils.copyProperties(item, vo);
                    return vo;
                }).orElseThrow(() -> new BusinessException(ApiResultPlatformCodeConstants.BIZ_ERROR, "data is null"));
    }

    @Override
    public PageResultBo<UserGroupTaskListVo> listUserGroupTaskPage(UserGroupTaskRecordListReq req) {
        FbTagUserGroupTaskRecordListReq query = new FbTagUserGroupTaskRecordListReq();
        BeanUtils.copyProperties(req, query);
        IRpcResult<IPageResp<UserGroupTaskListDto>> rpcResult = fbTagUserGroupTaskService.listUserGroupTaskPage(query);
        if (!rpcResult.checkSuccess()) {
            log.error("invoke fbTagUserGroupTaskService.listUserGroupTaskPage failed, message={}",
                    rpcResult.getMessage());
            return new PageResultBo<>();
        }
        return Optional.ofNullable(rpcResult.getData())
                .map(item -> {
                    List<UserGroupTaskListVo> listData = Optional.ofNullable(item.getPageRecords())
                            .orElse(Collections.emptyList())
                            .stream()
                            .map(i -> {
                                UserGroupTaskListVo vo = new UserGroupTaskListVo();
                                BeanUtils.copyProperties(i, vo);
                                return vo;
                            }).collect(Collectors.toList());
                    return new PageResultBo<UserGroupTaskListVo>()
                            .setPageNum(item.getPageNum())
                            .setPageSize(item.getPageSize())
                            .setTotal(item.getTotalCount())
                            .setRecords(listData);
                }).orElseGet(PageResultBo::new);
    }

    @Override
    public boolean stopTask(Long id) {
       try {
           IRpcResult<Boolean> rpcResult = fbTagUserGroupTaskService.stopTask(id);
           if (!rpcResult.checkSuccess()) {
               log.error("invoke fbTagUserGroupTaskService.stopTask failed, message={}", rpcResult.getMessage());
               return false;
           }

           return Optional.ofNullable(rpcResult.getData()).orElse(false);
       }catch (Exception e) {
           log.error("stopTask.fail",e);

           return false;
       }

    }
}
