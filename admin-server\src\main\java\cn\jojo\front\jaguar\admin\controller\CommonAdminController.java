package cn.jojo.front.jaguar.admin.controller;

import cn.jojo.common.api.ex.DefaultHttpActionResult;
import cn.jojo.common.api.ex.IHttpActionResult;
import cn.jojo.front.jaguar.biz.service.EnumService;
import cn.jojo.front.jaguar.biz.service.NvwaBizService;
import cn.jojo.front.jaguar.common.pojo.vo.BusinessSceneVo;
import cn.jojo.front.jaguar.common.pojo.vo.CourseDictVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * 2025/4/11 14:57
 */
@RestController
@RequestMapping("/admin")
@Tag(name = "Jaguar管理后台")
public class CommonAdminController {
    @Resource
    private EnumService enumService;
    @Resource
    private NvwaBizService nvwaBizService;

    @GetMapping("/course-dictionaries/{dictType}")
    @Operation(summary = "获取课程中心字典表数据", description = "通过字典类型获取课程中心字典数据")
    public IHttpActionResult<List<CourseDictVo>> getCourseDictData(@PathVariable("dictType")
                                                                   @NotNull(message = "dictType is null")
                                                                   String dictType) {
        List<CourseDictVo> resultList = enumService.getCourseDictList(dictType);
        return DefaultHttpActionResult.successWithData(resultList);
    }

    @GetMapping("/business-scenes")
    @Operation(summary = "获取业务场景列表", description = "获取业务场景列表")
    public IHttpActionResult<BusinessSceneVo> getBusinessSceneList() {
        BusinessSceneVo vo = nvwaBizService.getBusinessSceneInfo();
        return DefaultHttpActionResult.successWithData(vo);
    }
}
