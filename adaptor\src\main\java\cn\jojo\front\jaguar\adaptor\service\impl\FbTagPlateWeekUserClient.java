package cn.jojo.front.jaguar.adaptor.service.impl;

import cn.jojo.fb.tag.rpc.api.domain.req.PlateWeekUserReq;
import cn.jojo.fb.tag.rpc.api.service.PlateWeekUserRpcService;
import cn.jojo.front.jaguar.adaptor.service.IFbTagPlateWeekUserClient;
import cn.jojo.infra.sdk.api.metadata.IRpcResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

/**
 * @description:
 * @author: luohuanrong
 * @create: 2023/6/8
 **/
@Slf4j
@Component
public class FbTagPlateWeekUserClient implements IFbTagPlateWeekUserClient {

    @DubboReference
    private PlateWeekUserRpcService plateWeekUserRpcService;

    @Override
    public void recordWeekUser(Long userId) {
        try {
            PlateWeekUserReq req = new PlateWeekUserReq().setUserId(userId);
            IRpcResult<Void> rpcResult = plateWeekUserRpcService.recordUser(req);
            if (!rpcResult.checkSuccess()) {
                log.error("FbTagPlateWeekUserClient.recordWeekUser failed, userId:{}", userId);
            }
        } catch (Exception e) {
            log.error("FbTagPlateWeekUserClient.recordWeekUser error, userId:{}", userId, e);
        }
    }

}
