package cn.jojo.front.jaguar.admin.controller;

import cn.jojo.common.api.ex.DefaultHttpActionPageResult;
import cn.jojo.common.api.ex.IHttpActionResult;
import cn.jojo.edu.wings.rpc.api.dto.activity.ActivityBasicInfoDto;
import cn.jojo.edu.wings.rpc.api.dto.activity.RepositoryDto;
import cn.jojo.edu.wings.rpc.api.dto.activity.ThemeDto;
import cn.jojo.edu.wings.rpc.api.dto.level.LevelDetailDto;
import cn.jojo.front.jaguar.biz.service.impl.LevelBizService;
import cn.jojo.front.jaguar.common.pojo.bo.PageBo;
import cn.jojo.infra.sdk.api.metadata.IPageResp;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("admin/activity")
@Tag(name = "Jaguar管理后台")
@Validated
public class ActivityController {
    @Resource
    private LevelBizService levelBizService;

    @GetMapping("/level-page")
    @Operation(summary ="闯关分页列表")
    public IHttpActionResult<IPageResp<LevelDetailDto>> levelPage(@NotNull Integer pageNum, @NotNull Integer pageSize,
                                                                  Long levelTaskId, String levelName) {
        PageBo<LevelDetailDto> page = levelBizService.getLevelDetailPage(levelTaskId, levelName, pageNum, pageSize);
        return DefaultHttpActionPageResult.successWithPageData(page.getPageNum(),
                page.getPageSize(), page.getTotal(), page.getData());
    }

    @GetMapping("/repository-page")
    @Operation(summary ="活动库分页列表")
    public IHttpActionResult<IPageResp<RepositoryDto>> repositoryPage(@NotNull Integer pageNum,
                                                                      @NotNull Integer pageSize) {
        PageBo<RepositoryDto> page = levelBizService.getRepositoriesPage(pageNum, pageSize);
        return DefaultHttpActionPageResult.successWithPageData(page.getPageNum(),
                page.getPageSize(), page.getTotal(), page.getData());
    }

    @GetMapping("/theme-page")
    @Operation(summary ="主题分页列表")
    public IHttpActionResult<IPageResp<ThemeDto>> themePage(@NotNull Integer pageNum,
                                                            @NotNull Integer pageSize,
                                                            @NotNull Long repositoryId) {
        PageBo<ThemeDto> page = levelBizService.getThemesPage(repositoryId, pageNum, pageSize);
        return DefaultHttpActionPageResult.successWithPageData(page.getPageNum(),
                page.getPageSize(), page.getTotal(), page.getData());
    }

    @GetMapping("/activity-page")
    @Operation(summary ="活动分页列表")
    public IHttpActionResult<IPageResp<ActivityBasicInfoDto>> activityPage(@NotNull Integer pageNum,
                                                                           @NotNull Integer pageSize,
                                                                           @NotNull Long themeId,
                                                                           Long activityId,
                                                                           String activityName) {
        PageBo<ActivityBasicInfoDto> page = levelBizService.getActivityPage(
                activityId, activityName, themeId, pageNum, pageSize);
        return DefaultHttpActionPageResult.successWithPageData(page.getPageNum(),
                page.getPageSize(), page.getTotal(), page.getData());
    }
}
