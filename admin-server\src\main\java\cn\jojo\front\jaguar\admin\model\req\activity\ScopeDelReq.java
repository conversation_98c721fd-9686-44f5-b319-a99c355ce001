package cn.jojo.front.jaguar.admin.model.req.activity;

import cn.jojo.front.jaguar.common.enums.task.ActivityScopeTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 激励活动范围
 * @date 2024/3/11 21:05
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Tag(name = "活动范围")
public class ScopeDelReq {

    @Schema(description = "班级id集合 ")
    private List<Long> classIds;

    @Schema(description = "投放范围类型, 当前只有班级类型，后续新增多类型时使用 ", implementation = ActivityScopeTypeEnum.class)
    private Integer scopeType;
}
