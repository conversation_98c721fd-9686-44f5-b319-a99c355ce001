package cn.jojo.front.jaguar.adaptor.service.impl;

import cn.jojo.follower.api.OfficialAccountRpcApi;
import cn.jojo.follower.api.request.QueryOfficialAccountRequest;
import cn.jojo.follower.api.response.QueryOfficialAccountResponse;
import cn.jojo.front.jaguar.adaptor.service.IOfficialAccountRpcClient;
import cn.jojo.front.jaguar.common.pojo.bo.PageBo;
import cn.jojo.front.jaguar.common.pojo.vo.OfficialAccountVo;
import cn.jojo.infra.sdk.api.metadata.IPageResp;
import cn.jojo.infra.sdk.api.metadata.IRpcResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class OfficialAccountRpcClient implements IOfficialAccountRpcClient {
    @DubboReference
    private OfficialAccountRpcApi officialAccountRpcApi;

    @Override
    public PageBo<OfficialAccountVo> listOfficialAccount(String keyword, Long pageNum, Long pageSize) {
        if (pageNum == null || pageSize == null) {
            return PageBo.emptyPage();
        }
        QueryOfficialAccountRequest req =
            QueryOfficialAccountRequest.builder().pageNum(pageNum).pageSize(pageSize).name(keyword).build();
        try {
            IRpcResult<IPageResp<QueryOfficialAccountResponse>> rpcResult =
                officialAccountRpcApi.queryOfficialAccount(req);
            if (!rpcResult.checkSuccess()) {
                log.error("invoke officialAccountRpcApi.queryOfficialAccount failed, message = {}"
                    , rpcResult.getMessage());
                return PageBo.emptyPage();
            }
            if (rpcResult.getData() == null) {
                return PageBo.emptyPage();
            }
            List<OfficialAccountVo> list = Optional.ofNullable(rpcResult.getData())
                .map(IPageResp::getPageRecords)
                .orElse(Collections.emptyList())
                .stream()
                .map(item -> new OfficialAccountVo().setName(item.getName()).setAppId(item.getAppId()))
                .collect(Collectors.toList());
            return new PageBo<OfficialAccountVo>()
                .setData(list)
                .setPageNum(rpcResult.getData().getPageNum())
                .setPageSize(rpcResult.getData().getPageSize())
                .setTotal(rpcResult.getData().getTotalCount());
        } catch (Exception e) {
            log.error("invoke officialAccountRpcApi.queryOfficialAccount failed", e);
            return PageBo.emptyPage();
        }
    }
}
