package cn.jojo.front.jaguar.admin.model.dto.task;

import cn.jojo.front.jaguar.common.enums.YesOrNoEnum;
import cn.jojo.front.jaguar.common.enums.task.TaskRewardTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description
 * @date 2024/03/11
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema(description="奖励")
public class RewardDto {


    @Schema(description="奖励类型", implementation = TaskRewardTypeEnum.class)
    private Integer type;

    @Schema(description="奖励id：勋章id,心愿礼物id")
    private Long typeId;

    @Schema(description="奖励id映射描述，比如：贴纸id 对应的贴纸key， 或其他id 对应的key，方便前端老师看的描述转换")
    private String typeIdDesc;

    @Schema(description="数量")
    private String value;

    @Schema(description="奖励id")
    private Long id;

    @Schema(description="是否自动发放",implementation = YesOrNoEnum.class)
    private Integer autoDistribution;

    @Schema(description="贴纸页id")
    private Long stickerPageId;

    @Schema(description="贴纸页key")
    private String stickerPageKey;

}
