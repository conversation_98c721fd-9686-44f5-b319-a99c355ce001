package cn.jojo.front.jaguar.adaptor.service;

import cn.jojo.edu.malacca.rpc.api.dto.EduClassScheduleItemDto;
import cn.jojo.edu.malacca.rpc.api.req.EduClassScheduleItemQueryReq;
import cn.jojo.front.jaguar.common.pojo.req.EduUserClassScheduleItemQueryRpcReq;

import java.util.List;
import java.util.Map;

public interface IClassScheduleItemRpcClient {

    Map<Long, Integer> batchQueryClassScheduleItemCount(List<Long> classIds);

    List<EduClassScheduleItemDto> queryUserClassScheduleItems(EduUserClassScheduleItemQueryRpcReq rpcReq);

    List<EduClassScheduleItemDto> queryClassScheduleItems(EduClassScheduleItemQueryReq rpcReq);

}
