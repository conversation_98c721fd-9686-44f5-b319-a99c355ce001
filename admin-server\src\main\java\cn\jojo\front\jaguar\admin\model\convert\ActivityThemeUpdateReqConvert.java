package cn.jojo.front.jaguar.admin.model.convert;

import cn.jojo.edu.common.utils.convert.BaseModelConvert;
import cn.jojo.front.jaguar.admin.model.req.activity.ActivityThemeUpdateReq;
import cn.jojo.front.jaguar.common.pojo.vo.activity.ActivityThemeOperateVo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface ActivityThemeUpdateReqConvert extends
    BaseModelConvert<ActivityThemeUpdateReq, ActivityThemeOperateVo> {

    ActivityThemeUpdateReqConvert INSTANCE = Mappers.getMapper(ActivityThemeUpdateReqConvert.class);
}
