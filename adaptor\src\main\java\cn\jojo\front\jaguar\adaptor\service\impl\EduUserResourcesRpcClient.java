package cn.jojo.front.jaguar.adaptor.service.impl;


import cn.jojo.edu.fantasy.rpc.api.dto.EduUserResourcesExtensionDto;
import cn.jojo.edu.fantasy.rpc.api.dto.EduUserResourcesSimpleDto;
import cn.jojo.edu.fantasy.rpc.api.req.EduUserResourcesExtensionReq;
import cn.jojo.edu.fantasy.rpc.api.req.EduUserResourcesQueryReq;
import cn.jojo.edu.fantasy.rpc.api.req.enums.EduUserResAggregationType;
import cn.jojo.edu.fantasy.rpc.api.service.IEduUserResourcesRpcService;
import cn.jojo.front.jaguar.adaptor.service.IEduUserResourcesRpcClient;
import cn.jojo.front.jaguar.common.pojo.req.EduUserResourcesQueryRpcReq;
import cn.jojo.front.jaguar.common.pojo.vo.EduResourcesVo;
import cn.jojo.front.jaguar.common.pojo.vo.EduUserResourcesExtensionVo;
import cn.jojo.front.jaguar.common.pojo.vo.EduUserResourcesVo;
import cn.jojo.infra.sdk.api.metadata.IRpcResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Component
@Slf4j
public class EduUserResourcesRpcClient implements IEduUserResourcesRpcClient {

    @DubboReference
    private IEduUserResourcesRpcService eduUserResourcesRpcService;

    @Override
    public List<EduUserResourcesExtensionVo> queryAggregation(EduUserResourcesQueryRpcReq req) {
        if (CollectionUtils.isEmpty(req.getUserIds()) || (CollectionUtils.isEmpty(req.getUserHubIds()) &&
            CollectionUtils.isEmpty(req.getHubIds()))) {
            return Collections.emptyList();
        }
        EduUserResourcesQueryReq queryRpcReq = new EduUserResourcesQueryReq();
        BeanUtils.copyProperties(req, queryRpcReq);
        try {
            IRpcResult<List<EduUserResourcesExtensionDto>> rpcResult =
                eduUserResourcesRpcService.queryAggregation(EduUserResourcesExtensionReq.builder()
                    .queryReq(queryRpcReq)
                    .extensionTypes(Collections.singletonList(EduUserResAggregationType.RESOURCES))
                    .build());
            if (!rpcResult.checkSuccess()) {
                log.error("EduUserResourcesRpcClient#queryAggregation failed");
                return Collections.emptyList();
            }
            return rpcResult.getData().stream().map(data -> {
                EduUserResourcesVo userResourcesVo = new EduUserResourcesVo();
                EduResourcesVo resourcesVo = new EduResourcesVo();
                BeanUtils.copyProperties(data.getUserResources(), userResourcesVo);
                BeanUtils.copyProperties(data.getResources(), resourcesVo);
                return EduUserResourcesExtensionVo.builder()
                    .userResources(userResourcesVo)
                    .resources(resourcesVo)
                    .build();
            }).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("EduUserResourcesRpcClient#queryAggregation failed", e);
            return Collections.emptyList();
        }
    }
}
