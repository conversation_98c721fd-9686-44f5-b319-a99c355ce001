package cn.jojo.front.jaguar.adaptor.service.impl;

import cn.jojo.front.jaguar.common.pojo.bo.PageBo;
import cn.jojo.infra.sdk.api.metadata.IPageResp;
import cn.jojo.infra.sdk.api.metadata.IRpcResult;
import cn.tinman.clouds.hermes.dubbo.model.ChannelAdInfoDto;
import cn.tinman.clouds.hermes.dubbo.req.AdInfoQueryReq;
import cn.tinman.clouds.hermes.dubbo.service.AdInfoService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * 2024/11/8 13:54
 */
@Service
@Slf4j
public class AdInfoRpcClient {
    @DubboReference
    private AdInfoService adInfoService;

    @NotNull
    public PageBo<ChannelAdInfoDto> queryByCondition(AdInfoQueryReq req) {
        try {
            IRpcResult<IPageResp<ChannelAdInfoDto>> rpcResult = adInfoService.queryByCondition(req);
            if (!rpcResult.checkSuccess()) {
                log.error("AdInfoRpcClient.queryByCondition error, message={}", rpcResult.getMessage());
                return PageBo.emptyPage();
            }
            return Optional.ofNullable(rpcResult.getData())
                .map(item -> new PageBo<ChannelAdInfoDto>()
                    .setPageNum(item.getPageNum())
                    .setPageSize(item.getPageSize())
                    .setTotal(item.getTotalCount())
                    .setData(item.getPageRecords()))
                .orElse(new PageBo<>());
        } catch (Exception e) {
            log.error("AdInfoRpcClient.queryByCondition error", e);
        }
        return PageBo.emptyPage();
    }

    public List<ChannelAdInfoDto> queryByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        try {
            IRpcResult<List<ChannelAdInfoDto>> rpcResult = adInfoService.queryByIds(ids);
            if (!rpcResult.checkSuccess()) {
                log.error("AdInfoRpcClient.queryByIds error, message={}", rpcResult.getMessage());
                return Collections.emptyList();
            }
            return Optional.ofNullable(rpcResult.getData())
             .orElse(new ArrayList<>());
        } catch (Exception e) {
            log.error("AdInfoRpcClient.queryByIds error", e);
            return Collections.emptyList();
        }
    }
}
