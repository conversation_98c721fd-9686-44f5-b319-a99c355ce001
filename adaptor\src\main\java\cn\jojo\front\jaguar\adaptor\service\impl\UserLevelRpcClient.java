package cn.jojo.front.jaguar.adaptor.service.impl;

import cn.jojo.edu.wings.rpc.api.dto.level.LevelParticipantDto;
import cn.jojo.edu.wings.rpc.api.req.level.UserLevelQueryReq;
import cn.jojo.edu.wings.rpc.api.service.IUserLevelRpcService;
import cn.jojo.front.jaguar.adaptor.service.IUserLevelRpcClient;
import cn.jojo.infra.sdk.api.metadata.IRpcResult;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class UserLevelRpcClient implements IUserLevelRpcClient {

    @DubboReference
    private IUserLevelRpcService userLevelRpcService;

    @Override
    public int queryLevelParticipant(Long taskId) {
        if (taskId == null) {
            return 0;
        }
        UserLevelQueryReq req = UserLevelQueryReq.builder().levelBindId(taskId).build();
        try {
            IRpcResult<LevelParticipantDto> rpcResult = userLevelRpcService.queryLevelParticipant(req);
            if (!rpcResult.checkSuccess()) {
                log.error("invoke userLevelRpcService.queryLevelParticipant failed, message={}",
                        rpcResult.getMessage());
                return 0;
            }
            return Optional.ofNullable(rpcResult.getData()).map(LevelParticipantDto::getParticipantCount).orElse(0);
        } catch (Exception e) {
            log.error("invoke userLevelRpcService.queryLevelParticipant failed", e);
            return 0;
        }
    }
}
