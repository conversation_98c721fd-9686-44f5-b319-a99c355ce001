package cn.jojo.front.jaguar.admin.config;

import cn.jojo.edu.common.redis.client.EduRedissonClient;
import cn.jojo.edu.common.utils.validator.CustomLocalExecutableValidator;
import cn.jojo.front.jaguar.admin.constants.AdminConstants;
import cn.jojo.front.jaguar.common.config.CustomEnumJsonSerializer;
import cn.jojo.front.jaguar.core.common.RedisHelper;
import cn.jojo.front.jaguar.core.common.RedissonHelper;
import com.baomidou.mybatisplus.extension.plugins.PaginationInterceptor;
import com.baomidou.mybatisplus.extension.plugins.pagination.optimize.JsqlParserCountOptimize;
import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalTimeSerializer;
import org.mybatis.spring.annotation.MapperScan;
import org.redisson.api.RedissonClient;
import org.springdoc.webmvc.api.MultipleOpenApiResource;
import org.springdoc.webmvc.api.OpenApiResource;
import org.springdoc.webmvc.ui.SwaggerConfigResource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.context.annotation.FilterType;
import org.springframework.http.converter.ByteArrayHttpMessageConverter;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.config.annotation.PathMatchConfigurer;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;
import java.util.TimeZone;

@Configuration
@EnableAspectJAutoProxy(exposeProxy = true)
@ComponentScan(basePackages = {"cn.jojo.front.jaguar", "cn.jojo.edu.common.utils"},
    excludeFilters = @ComponentScan.Filter(
    type =  FilterType.ASSIGNABLE_TYPE,
    classes = {CustomLocalExecutableValidator.class, cn.jojo.edu.common.utils.i18n.config.InternationValidConfig.class}))
@MapperScan("cn.jojo.front.jaguar.core.dao.mapper")
@EnableApolloConfig
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class})
@EnableAsync
public class ConfigAll implements WebMvcConfigurer {

    private static final String TIME_PATTERN = "yyyy-MM-dd HH:mm:ss";

    /**
     * mybatis-plus 分页插件
     */
    @Bean
    public PaginationInterceptor paginationInterceptor() {
        PaginationInterceptor paginationInterceptor = new PaginationInterceptor();
        // 开启 count 的 join 优化,只针对部分 left join
        paginationInterceptor.setCountSqlParser(new JsqlParserCountOptimize(true));
        return paginationInterceptor;
    }

    @Bean("redisHelper")
    public RedisHelper redisHelper(@Autowired EduRedissonClient eduRedissonClient,
                                   @Autowired RedissonClient redissonClient,
                                   @Value("${redis.compress.switch:false}") boolean compressSwitch) {
        return new RedissonHelper(eduRedissonClient, redissonClient, compressSwitch);
    }

    /**
     * 消息转换配置
     **/
    @Override
    public void configureMessageConverters(List<HttpMessageConverter<?>> converters) {

        MappingJackson2HttpMessageConverter converter = new MappingJackson2HttpMessageConverter();
        ObjectMapper objectMapper = new ObjectMapper();
        SimpleModule simpleModule = new SimpleModule();
        simpleModule.addSerializer(Enum.class, new CustomEnumJsonSerializer());
        objectMapper.registerModule(simpleModule);
        // 设置枚举可以传null或者"",解析为 null
        objectMapper.configure(DeserializationFeature.READ_UNKNOWN_ENUM_VALUES_AS_NULL, true);
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        // 忽略无法转换的对象
        objectMapper.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
        // PrettyPrinter 格式化输出
        objectMapper.configure(SerializationFeature.INDENT_OUTPUT, true);
        // 指定时区
        objectMapper.setTimeZone(TimeZone.getTimeZone("GMT+8:00"));
        // 日期类型字符串处理
        objectMapper.setDateFormat(new SimpleDateFormat(TIME_PATTERN));

        // java8日期日期处理
        JavaTimeModule javaTimeModule = new JavaTimeModule();
        javaTimeModule.addSerializer(LocalDateTime.class,
            new LocalDateTimeSerializer(DateTimeFormatter.ofPattern(TIME_PATTERN)));
        javaTimeModule
            .addSerializer(LocalDate.class, new LocalDateSerializer(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        javaTimeModule.addSerializer(LocalTime.class, new LocalTimeSerializer(DateTimeFormatter.ofPattern("HH:mm:ss")));
        javaTimeModule.addDeserializer(LocalDateTime.class,
            new LocalDateTimeDeserializer(DateTimeFormatter.ofPattern(TIME_PATTERN)));
        javaTimeModule
            .addDeserializer(LocalDate.class, new LocalDateDeserializer(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        javaTimeModule
            .addDeserializer(LocalTime.class, new LocalTimeDeserializer(DateTimeFormatter.ofPattern("HH:mm:ss")));
        objectMapper.registerModule(javaTimeModule);
        converter.setObjectMapper(objectMapper);
        converters.add(0, converter);
        //让第0个转换器为字节数组转换器
        Optional<HttpMessageConverter<?>> bytesConverter = converters
            .stream()
            .filter(item -> item instanceof ByteArrayHttpMessageConverter)
            .findFirst();
        if (bytesConverter.isPresent()) {
            converters.remove(bytesConverter.get());
            converters.add(0, bytesConverter.get());
        } else {
            converters.add(new ByteArrayHttpMessageConverter());
        }
    }

    @Override
    public void configurePathMatch(PathMatchConfigurer configurer) {
        configurer.addPathPrefix(AdminConstants.API_PATH_PREFIX, c -> c.isAnnotationPresent(RestController.class) &&
            !(MultipleOpenApiResource.class.isAssignableFrom(c) || OpenApiResource.class.isAssignableFrom(c)
                || SwaggerConfigResource.class.isAssignableFrom(c)));
    }

}
