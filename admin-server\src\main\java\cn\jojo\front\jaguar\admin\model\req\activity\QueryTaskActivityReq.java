package cn.jojo.front.jaguar.admin.model.req.activity;

import cn.jojo.front.jaguar.common.enums.task.ActivityTypeEnum;
import cn.jojo.front.jaguar.common.enums.task.CourseTypeEnum;
import cn.jojo.front.jaguar.common.enums.task.InnerSubjectType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class QueryTaskActivityReq {

    @Schema(description = "活动主键id")
    private Long id;

    /**
     * 活动名称
     */
    @Schema(description = "活动名称")
    private String name;

    /**
     * 活动类型(1.今日活动、2.首月激励、3.全年激励)在apollo可配置。
     */
    @Schema(description = "活动类型", implementation = ActivityTypeEnum.class)
    private Integer type;

    @Schema(description = "页码", defaultValue = "1")
    private Long pageNum = 1L;

    @Schema(description = "页宽", defaultValue = "10")
    private Long pageSize = 10L;

    @Schema(description = "品类", implementation = InnerSubjectType.class)
    private Integer subjectType;

    @Schema(description = "课程id")
    private Long courseId;

    @Schema(description = "班级key")
    private String classKey;

    @Schema(description = "班级id")
    private Long classId;

    @Schema(description = "产品类型", implementation = CourseTypeEnum.class)
    private Integer courseType;
}
