package cn.jojo.front.jaguar.admin.controller;

import cn.jojo.front.jaguar.common.exception.BusinessException;
import cn.jojo.front.jaguar.common.pojo.req.exposurestrategy.ExposureStrategyListReq;
import cn.jojo.front.jaguar.common.pojo.req.exposurestrategy.ExposureStrategySaveReq;
import cn.jojo.front.jaguar.common.pojo.vo.exposurestrategy.ExposureStrategyVo;
import cn.jojo.front.jaguar.core.service.resource.IExposureStrategyConfigService;
import cn.jojo.infra.sdk.api.constants.ApiResultPlatformCodeConstants;
import cn.jojo.infra.sdk.api.metadata.DefaultHttpResult;
import cn.jojo.infra.sdk.api.metadata.DefaultPageResp;
import cn.jojo.infra.sdk.api.metadata.IHttpResult;
import cn.jojo.infra.sdk.api.metadata.IPageResp;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.validation.BeanPropertyBindingResult;
import org.springframework.validation.ObjectError;
import org.springframework.validation.Validator;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.ValidationException;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 */
@Tag(name = "Jaguar管理后台")
@RestController
@RequestMapping("/admin/material-exposure-strategies")
public class ExposureStrategyController extends BaseController {

    @Qualifier("mvcValidator")
    @Resource
    private Validator validator;

    @Resource
    private IExposureStrategyConfigService exposureStrategyConfigService;

    /**
     * 新增策略
     */
    @Operation(summary = "新增素材曝光策略配置", description = "新增素材曝光策略配置")
    @PostMapping
    public IHttpResult<Long> create(@RequestBody @Validated ExposureStrategySaveReq req) {
        req.setOperatorId(getEmployeeId());
        req.setOperatorName(getEmployeeName());
        return DefaultHttpResult.successWithData(exposureStrategyConfigService.saveOrUpdate(req));
    }

    /**
     * 更新策略
     */
    @Operation(summary = "更新素材曝光策略配置", description = "更新素材曝光策略配置")
    @PutMapping("/{strategyId}")
    public IHttpResult<Long> update(@PathVariable @NotNull @Parameter(description = "策略id") Long strategyId,
                                    @RequestBody ExposureStrategySaveReq req) {
        if (req.getUpdateType() == null) {
            throw new BusinessException(ApiResultPlatformCodeConstants.PARAM_ERROR, "updateType can not be null");
        }
        req.setId(strategyId);
        req.setOperatorId(getEmployeeId());
        req.setOperatorName(getEmployeeName());
        if (req.getUpdateType() == ExposureStrategySaveReq.UpdateTypeEnum.DETAIL) {
            BeanPropertyBindingResult errors = new BeanPropertyBindingResult(req, "req");
            validator.validate(req, errors);
            List<ObjectError> allErrors = errors.getAllErrors();
            if (CollectionUtils.isNotEmpty(allErrors)) {
                throw new ValidationException(allErrors.get(0).getDefaultMessage());
            }
            return DefaultHttpResult.successWithData(exposureStrategyConfigService.saveOrUpdate(req));
        }

        if (req.getUpdateType() == ExposureStrategySaveReq.UpdateTypeEnum.PRIORITY) {
            if (req.getPriority() == null) {
                throw new BusinessException(ApiResultPlatformCodeConstants.PARAM_ERROR, "Priority cannot be empty");
            }
            return DefaultHttpResult.successWithData(exposureStrategyConfigService.updatePriority(req));
        }

        if (req.getStatus() == null) {
            throw new BusinessException(ApiResultPlatformCodeConstants.PARAM_ERROR, "Status cannot be empty");
        }
        return DefaultHttpResult.successWithData(exposureStrategyConfigService.updateStatus(req));
    }


    /**
     * 根据ID查询详情
     */
    @Operation(summary = "根据id查询素材曝光策略配置", description = "根据id查询素材曝光策略配置")
    @GetMapping("/{strategyId}")
    public IHttpResult<ExposureStrategyVo> query(
        @PathVariable @NotNull @Parameter(description = "策略id") Long strategyId) {
        return DefaultHttpResult.successWithData(exposureStrategyConfigService.detail(strategyId));
    }

    /**
     * 查询所有策略
     */
    @Operation(summary = "分页查询素材曝光策略配置", description = "分页查询素材曝光策略配置")
    @GetMapping
    public IPageResp<ExposureStrategyVo> page(ExposureStrategyListReq req) {
        IPage<ExposureStrategyVo> page = exposureStrategyConfigService.page(req);
        return DefaultPageResp.buildPageResp(req, page.getTotal(), page.getRecords());
    }
}
