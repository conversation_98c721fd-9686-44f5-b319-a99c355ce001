package cn.jojo.front.jaguar.adaptor.service.impl;

import cn.jojo.front.jaguar.adaptor.service.IMallChannelRpcClient;
import cn.jojo.front.jaguar.common.pojo.req.ChannelNoDimensionListPageReq;
import cn.jojo.front.jaguar.common.pojo.req.ChannelNoEnumListPageReq;
import cn.jojo.front.jaguar.common.pojo.req.OperateChannelDimensionEnumRpcReq;
import cn.jojo.front.jaguar.common.pojo.resp.CreateResPositionEnumResp;
import cn.tinman.sharedservices.mall.channel.api.request.BatchCreateChannelNoReq;
import cn.tinman.sharedservices.mall.channel.api.request.DeleteSystemDimensionEnumsReq;
import cn.tinman.sharedservices.mall.channel.api.request.DimensionListPageReq;
import cn.tinman.sharedservices.mall.channel.api.request.EnumListPageReq;
import cn.tinman.sharedservices.mall.channel.api.request.SaveSystemDimensionEnumsReq;
import cn.tinman.sharedservices.mall.channel.api.response.BatchCreateChannelNoResp;
import cn.tinman.sharedservices.mall.channel.api.response.DimensionListPageResp;
import cn.tinman.sharedservices.mall.channel.api.response.EnumListPageResp;
import cn.tinman.sharedservices.mall.channel.api.service.IChannelNoApiService;
import cn.tinman.sharedservices.mall.channel.api.service.IDimensionEnumsApiService;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/7/3 13:38
 */
@Component
public class MallChannelRpcClientImpl extends AbstractRpcClient implements IMallChannelRpcClient {

    @DubboReference
    private IChannelNoApiService channelNoApiService;

    @DubboReference
    private IDimensionEnumsApiService dimensionEnumsApiService;


    @Override
    public Optional<CreateResPositionEnumResp> createChannelDimensionEnum(OperateChannelDimensionEnumRpcReq req) {
        return saveChannelDimensionEnum(req);

    }

    @Override
    public Optional<CreateResPositionEnumResp> updateChannelDimensionEnum(OperateChannelDimensionEnumRpcReq req) {
        return saveChannelDimensionEnum(req);

    }

    @Override
    public Optional<CreateResPositionEnumResp> deleteChannelDimensionEnum(OperateChannelDimensionEnumRpcReq req) {
        DeleteSystemDimensionEnumsReq rpcReq = DeleteSystemDimensionEnumsReq.builder()
            .deleteEnumList(Collections.singletonList(DeleteSystemDimensionEnumsReq.DeleteEnumItem.builder()
                .enumKey(req.getKey())
                .dimensionKey(req.getDimensionKey())
                .build()))
            .build();
        return doRpc(rpcReq, dimensionEnumsApiService::deleteSystemDimensionEnums, CreateResPositionEnumResp.class);
    }

    @Override
    public Optional<DimensionListPageResp> fetchDimensionListPage(ChannelNoDimensionListPageReq req) {
        DimensionListPageReq rpcReq = DimensionListPageReq
            .builder()
            .dimensionName(req.getDimensionName())
            .pageNum(req.getPageNum())
            .pageSize(req.getPageSize())
            .ids(req.getIds())
            .build();
        return doRpc(rpcReq, dimensionEnumsApiService::fetchDimensionListPage, DimensionListPageResp.class);
    }

    @Override
    public Optional<EnumListPageResp> fetchEnumListPage(ChannelNoEnumListPageReq req) {
        EnumListPageReq rpcReq = EnumListPageReq
            .builder()
            .enumName(req.getEnumName())
            .pageNum(req.getPageNum())
            .pageSize(req.getPageSize())
            .ids(req.getIds())
            .dimensionIds(req.getDimensionIds())
            .build();
        return doRpc(rpcReq, dimensionEnumsApiService::fetchEnumListPage, EnumListPageResp.class);
    }

    private Optional<CreateResPositionEnumResp> saveChannelDimensionEnum(OperateChannelDimensionEnumRpcReq req) {
        SaveSystemDimensionEnumsReq rpcReq = SaveSystemDimensionEnumsReq.builder()
            .saveEnumList(Collections.singletonList(SaveSystemDimensionEnumsReq.SaveEnumItem.builder()
                .enumName(req.getName())
                .enumKey(req.getKey())
                .dimensionKey(req.getDimensionKey())
                .build()))
            .build();
        return doRpc(rpcReq, dimensionEnumsApiService::saveSystemDimensionEnums, CreateResPositionEnumResp.class);
    }

    @Override
    public Optional<BatchCreateChannelNoResp> batchCreateChannelNumber(BatchCreateChannelNoReq req) {
        return doRpc(req, channelNoApiService::batchCreateChannelNo, BatchCreateChannelNoResp.class);
    }
}
