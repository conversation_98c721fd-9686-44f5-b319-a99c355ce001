package cn.jojo.front.jaguar.admin.controller;

import cn.jojo.front.jaguar.common.enums.MaterialConfigTypeEnum;
import cn.jojo.front.jaguar.common.pojo.bo.EmployeeBo;
import cn.jojo.front.jaguar.common.pojo.entity.advertisement.Advertisement;
import cn.jojo.front.jaguar.common.pojo.req.AdLabelReq;
import cn.jojo.front.jaguar.common.pojo.req.AdvertisementListReq;
import cn.jojo.front.jaguar.common.pojo.req.AdvertisementSaveReqV2;
import cn.jojo.front.jaguar.common.pojo.req.PriorityUpdateReq;
import cn.jojo.front.jaguar.common.pojo.vo.AdLabelVo;
import cn.jojo.front.jaguar.common.pojo.vo.AdvertisementListVo;
import cn.jojo.front.jaguar.common.pojo.vo.AdvertisementVoV2;
import cn.jojo.front.jaguar.core.service.advertisement.AdvertisementService;
import cn.jojo.infra.sdk.api.metadata.DefaultHttpResult;
import cn.jojo.infra.sdk.api.metadata.DefaultPageResp;
import cn.jojo.infra.sdk.api.metadata.IHttpResult;
import cn.jojo.infra.sdk.api.metadata.IPageResp;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.Positive;
import java.util.Collections;

/**
 * @Description 广告
 * <AUTHOR>
 * @Date 2021/5/27 10:32
 */
@RestController
@RequestMapping("/admin/advertisement")
@Tag(name = "Jaguar管理后台")
@Validated
public class AdvertisementController extends BaseController {

    @Autowired
    private AdvertisementService advertisementService;

    /**
     * 获取广告列表
     *
     * @param req 查询参数
     * @return 广告列表
     */
    @GetMapping("/getAdvertisementList")
    @Operation(summary = "广告配置列表", description = "广告配置列表")
    public IPageResp<AdvertisementListVo> getContentConfigurationList(AdvertisementListReq<Void> req) {
        IPage<AdvertisementListVo> page = advertisementService.getAdvertisementList(req);
        return DefaultPageResp.buildPageResp(req, page.getTotal(), page.getRecords());
    }

    /**
     * @Description: 更改优先级
     * @author: xr
     * @date: 2021/5/27
     */
    @PostMapping("/updateAdvertisementPriority")
    @Operation(summary ="Advertisement优先级", description = "更新Advertisement优先级")
    public IHttpResult<Boolean> updateContentConfigurationPriority(
        @RequestBody @Validated PriorityUpdateReq req) {
        Advertisement advertisement = advertisementService.updatePriority(req);

        if (advertisement != null) {
            advertisementService.deleteCache(Collections.singleton(advertisement));
        }

        return DefaultHttpResult.successWithData(true);
    }


    /**
     * 保存广告
     *
     * @param req 保存参数
     * @return 广告ID
     */
    @PostMapping("/v2/saveOrUpdateAdvertisement")
    @Operation(summary = "保存Advertisement", description = "保存Advertisement")
    public IHttpResult<String> saveOrUpdateAdvertisementV2(@RequestBody @Validated AdvertisementSaveReqV2 req) {
        req.setMaterialConfigType(MaterialConfigTypeEnum.CUSTOM);
        return DefaultHttpResult.successWithData(String.valueOf(advertisementService.saveOrUpdateAdvertisementV2(req,
            new EmployeeBo().setEmployeeId(getEmployeeId()).setEmployeeName(getEmployeeName())).getId()));
    }


    /**
     * 获取广告详情
     *
     * @param advertisementId 广告ID
     * @return 广告详情
     */
    @GetMapping("/v2/getAdvertisementDetailInfo")
    @Operation(summary = "获取广告详情", description = "获取广告详情")
    public IHttpResult<AdvertisementVoV2> getAdvertisementInfoV2(@RequestParam("advertisementId") @Positive Integer advertisementId) {
        return DefaultHttpResult.successWithData(advertisementService.getAdvertisementInfoV2(advertisementId));
    }

    /**
     * 删除广告
     *
     * @param advertisementId 广告详情
     * @return 是否成功
     */
    @PostMapping("/deleteAdvertisement")
    @Operation(summary ="删除Advertisement详情", description = "删除Advertisement详情")
    public IHttpResult<Boolean> deleteAdvertisement(@RequestParam("advertisementId") @Positive Integer advertisementId) {
        Advertisement advertisement = advertisementService.deleteAdvertisement(advertisementId, new EmployeeBo()
            .setEmployeeId(getEmployeeId())
            .setEmployeeName(getEmployeeName()));
        advertisementService.deleteCache(Collections.singleton(advertisement));
        return DefaultHttpResult.successWithData(true);
    }

    @GetMapping("/getAllLabels")
    @Operation(summary ="分页获取标签列表")
    public IPageResp<AdLabelVo> getAllLabels(@Validated AdLabelReq req)
    {
        IPage<AdLabelVo> resultPage = advertisementService.getAllLabels(req.getPageNum(), req.getPageSize(),req.getLabelIds(),req.getLabelName());
        return DefaultPageResp.buildPageResp(req.getPageNum(), req.getPageSize(), resultPage.getTotal(), resultPage.getRecords());

    }

}
