package cn.jojo.front.jaguar.admin.model.dto.task;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description
 * @date 2024/03/19
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MedalResourceDto {
    @Schema(description="勋章id")
    private Long id;

    @Schema(description="已解锁图片")
    private String unlockedImage;
    @Schema(description="未解锁图片")
    private String lockedImage;
    @Schema(description = "资源包")
    private MedalResourcePackageDto resourcePackages;
    @Schema(description="称号")
    private String title;
}
