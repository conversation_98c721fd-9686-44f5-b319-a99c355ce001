package cn.jojo.front.jaguar.admin.controller;

import cn.jojo.front.jaguar.common.exception.BusinessException;
import cn.jojo.front.jaguar.common.pojo.req.resource.ResourceListReq;
import cn.jojo.front.jaguar.common.pojo.req.resource.ResourceSaveReq;
import cn.jojo.front.jaguar.common.pojo.vo.resource.ResourceLibDetailVo;
import cn.jojo.front.jaguar.common.pojo.vo.resource.ResourceVo;
import cn.jojo.front.jaguar.common.pojo.vo.resource.SubjectListVo;
import cn.jojo.front.jaguar.common.pojo.vo.resource.SubjectVo;
import cn.jojo.front.jaguar.core.service.resource.IResourceService;
import cn.jojo.infra.sdk.api.constants.ApiResultPlatformCodeConstants;
import cn.jojo.infra.sdk.api.metadata.DefaultHttpResult;
import cn.jojo.infra.sdk.api.metadata.DefaultPageResp;
import cn.jojo.infra.sdk.api.metadata.IHttpResult;
import cn.jojo.infra.sdk.api.metadata.IPageResp;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/1/8 17:35
 * @desc
 */
@Validated
@RequestMapping("/admin")
@RestController
@Tag(name = "Jaguar管理后台")
public class ResourceController extends BaseController {

    @Resource
    private IResourceService resourceService;

    @GetMapping("/resources")
    @Operation(summary = "查询资源列表", description = "查询资源列表")
    public IPageResp<ResourceVo> page(ResourceListReq<Void> req) {
        IPage<ResourceVo> page = resourceService.page(req);
        return DefaultPageResp.buildPageResp(req, page.getTotal(), page.getRecords());
    }

    @PostMapping("/resources")
    @Operation(summary = "保存资源", description = "保存资源")
    public IHttpResult<Long> save(@Validated @RequestBody ResourceSaveReq req) {
        req.setOperatorId(getEmployeeId());
        req.setOperatorName(getEmployeeName());
        Long resId = resourceService.saveOrUpdate(req);
        return DefaultHttpResult.successWithData(resId);
    }

    @PutMapping("/resources")
    @Operation(summary = "更新资源", description = "更新资源")
    public IHttpResult<Long> update(@Validated @RequestBody ResourceSaveReq req) {
        if (Objects.isNull(req.getId())) {
            throw new BusinessException(ApiResultPlatformCodeConstants.PARAM_ERROR, "id can not be null");
        }
        Long resId = resourceService.saveOrUpdate(req);
        return DefaultHttpResult.successWithData(resId);
    }

    @GetMapping("/resources/{id}")
    @Operation(summary = "查询资源详情", description = "查询资源详情")
    public IHttpResult<ResourceVo> detail(
        @PathVariable(value = "id") @NotNull @Parameter(description = "资源id") Long id) {
        ResourceVo result = resourceService.detail(id);
        return DefaultHttpResult.successWithData(result);
    }

    @DeleteMapping("/resources")
    @Operation(summary = "删除资源", description = "删除资源")
    public IHttpResult<Boolean> delete(@RequestParam @NotNull @Parameter(description = "资源id") Long id) {
        Boolean result = resourceService.delete(id, getEmployeeId(), getEmployeeName());
        return DefaultHttpResult.successWithData(result);
    }

    @GetMapping("/subjects")
    @Operation(summary = "查询科目列表", description = "查询科目和阶段配置列表")
    public IHttpResult<SubjectListVo> getAllSubjects() {
        List<SubjectVo> result = resourceService.getAllSubjects();
        return DefaultHttpResult.successWithData(SubjectListVo.builder()
            .subjects(result)
            .build());
    }
}
