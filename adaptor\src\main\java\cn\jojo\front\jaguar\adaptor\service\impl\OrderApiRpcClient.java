package cn.jojo.front.jaguar.adaptor.service.impl;

import cn.jojo.front.jaguar.common.pojo.bo.OrderSimpleInfoBo;
import cn.jojo.infra.sdk.api.metadata.IRpcResult;
import cn.tinman.sharedservices.mall.order.api.dto.order.OrderSimpleInfoDTO;
import cn.tinman.sharedservices.mall.order.api.request.order.OrderSimpleInfoReq;
import cn.tinman.sharedservices.mall.order.api.service.IOrderApiService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 2025/3/18 14:11
 */
@Service
@Slf4j
public class OrderApiRpcClient {
    @DubboReference
    private IOrderApiService orderApiService;

    public List<OrderSimpleInfoBo> queryOrderSimpleInfo(List<String> orderIds,
                                                        boolean needOrderAddress,
                                                        boolean needOrderExtend,
                                                        boolean needOrderChannelInfo) {
        if (CollectionUtils.isEmpty(orderIds)) {
            return Collections.emptyList();
        }
        return Lists.partition(orderIds, 100)
                .stream()
                .map(i -> queryOrderSimpleInfoList(i, needOrderAddress, needOrderExtend, needOrderChannelInfo))
                .flatMap(List::stream)
                .collect(Collectors.toList());
    }

    public List<OrderSimpleInfoBo> queryOrderSimpleInfoList(List<String> orderIds,
                                                        boolean needOrderAddress,
                                                        boolean needOrderExtend,
                                                        boolean needOrderChannelInfo) {
        if (CollectionUtils.isEmpty(orderIds)) {
            return Collections.emptyList();
        }

        OrderSimpleInfoReq req = new OrderSimpleInfoReq();
        req.setOrderIds(orderIds);
        req.setNeedOrderAddress(needOrderAddress);
        req.setNeedOrderChannelInfo(needOrderChannelInfo);
        req.setNeedOrderExtend(needOrderExtend);
        try {
            IRpcResult<List<OrderSimpleInfoDTO>> rpcResult = orderApiService.listOrderByOrderIds(req);
            if (!rpcResult.checkSuccess()) {
                log.error("invoke orderApiService.listOrderByOrderIds failed, message={}", rpcResult.getMessage());
                return Collections.emptyList();
            }
            return Optional.ofNullable(rpcResult.getData()).orElse(Collections.emptyList())
                .stream()
                .map(i -> {
                    OrderSimpleInfoBo.OrderChannelInfoBo channelInfo =
                        Optional.ofNullable(i.getOrderChannelInfoDTO())
                            .map(j -> new OrderSimpleInfoBo.OrderChannelInfoBo()
                                .setChannelArguments(j.getChannelArguments())
                                .setChannelNo(j.getChannelNo())
                                .setChannelNoVersion(j.getChannelNoVersion())
                                .setSellerId(j.getSellerId()))
                            .orElse(null);
                    return new OrderSimpleInfoBo()
                        .setOrderId(i.getOrderId())
                        .setOrderChannelInfo(channelInfo)
                        .setOrderStatus(i.getOrderStatus())
                        .setProductAmount(i.getProductAmount())
                        .setSsTenantId(i.getSsTenantId())
                        .setUserId(i.getUserId())
                        .setCreateTime(i.getCreateTime());
                }).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("invoke orderApiService.listOrderByOrderIds failed", e);
            return Collections.emptyList();
        }
    }


}
