package cn.jojo.front.jaguar.admin.controller;

import cn.jojo.front.jaguar.common.pojo.entity.grayscale.Grayscale;
import cn.jojo.front.jaguar.common.pojo.req.BaseListReq;
import cn.jojo.front.jaguar.common.pojo.req.GrayscaleSaveReq;
import cn.jojo.front.jaguar.common.pojo.vo.GrayscaleDetailVo;
import cn.jojo.front.jaguar.common.pojo.vo.GrayscaleListVo;
import cn.jojo.front.jaguar.core.service.grayscale.GrayscaleService;
import cn.jojo.infra.sdk.api.metadata.DefaultHttpResult;
import cn.jojo.infra.sdk.api.metadata.DefaultPageResp;
import cn.jojo.infra.sdk.api.metadata.IHttpResult;
import cn.jojo.infra.sdk.api.metadata.IPageResp;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import java.util.Collections;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.constraints.NotNull;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/admin/grayscale")
@Tag(name = "Jaguar管理后台")
@Validated
public class GrayscaleController {

    @Resource
    private GrayscaleService grayscaleService;

    @GetMapping("/getGrayscaleList")
    @Operation(summary ="灰度列表",   description = "获取灰度列表")
    public IPageResp<GrayscaleListVo> getGrayscaleList(BaseListReq<Void> req) {
        IPage<GrayscaleListVo> pageList = grayscaleService.listGrayscale(req);
        return DefaultPageResp.buildPageResp(req, pageList.getTotal(), pageList.getRecords());
    }

    @GetMapping("/getGrayscaleInfo")
    @Operation(summary ="灰度详情",   description = "获取灰度详情")
    public IHttpResult<GrayscaleDetailVo> getGrayscaleInfo(@RequestParam("id") @NotNull Integer id) {
        List<GrayscaleDetailVo> resultData = grayscaleService.listGrayscaleDetailInfoByIds(Collections.singleton(id));
        return DefaultHttpResult.successWithData(resultData.stream().findAny().orElse(null));
    }

    @PostMapping("/saveGrayscale")
    @Operation(summary ="保存灰度",   description = "保存灰度详情")
    public IHttpResult<Integer> saveGrayscale(@RequestBody @Validated GrayscaleSaveReq req) {
        Grayscale grayscale = grayscaleService.saveOrUpdate(req);
        return DefaultHttpResult.successWithData(grayscale == null ? null : grayscale.getId());
    }

    @GetMapping(value = "deleteGrayscale")
    @Operation(summary ="删除灰度",   description = "删除灰度")
    public IHttpResult<Boolean> deleteGrayscale(@RequestParam("id") @NotNull Integer id) {
        grayscaleService.deleteGrayscaleById(id);
        return DefaultHttpResult.successWithData(true);
    }
}
