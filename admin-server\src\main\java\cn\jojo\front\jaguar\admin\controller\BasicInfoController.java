package cn.jojo.front.jaguar.admin.controller;

import cn.jojo.front.jaguar.common.pojo.req.BasicInfoListReq;
import cn.jojo.front.jaguar.common.pojo.req.BasicInfoUpdateReq;
import cn.jojo.front.jaguar.common.pojo.vo.BasicInfoDetailVo;
import cn.jojo.front.jaguar.common.pojo.vo.BasicInfoListVo;
import cn.jojo.front.jaguar.core.service.common.BasicInfoService;
import cn.jojo.infra.sdk.api.metadata.DefaultHttpResult;
import cn.jojo.infra.sdk.api.metadata.DefaultPageResp;
import cn.jojo.infra.sdk.api.metadata.IHttpResult;
import cn.jojo.infra.sdk.api.metadata.IPageResp;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Positive;

import io.swagger.v3.oas.annotations.Operation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/admin/basic")
@Tag(name = "Jaguar管理后台")
@Validated
public class BasicInfoController {

    @Resource
    private BasicInfoService basicInfoService;

    @GetMapping("/getBasicInfoList")
    @Operation(summary ="列表页接口",   description = "获取基础配置列表")
    public IPageResp<BasicInfoListVo> listBasicInfo(BasicInfoListReq<Void> req) {
        IPage<BasicInfoListVo> result = basicInfoService.listBasicInfo(req);
        return DefaultPageResp
            .buildPageResp(req.getPageNum(), req.getPageSize(), result.getTotal(), result.getRecords());
    }

    @GetMapping("getBasicInfo")
    @Operation(summary = "详情页接口", method = "GET", description = "获取基础配置详情")
    public IHttpResult<BasicInfoDetailVo> getBasicInfo(@RequestParam("id") @Positive Integer id) {
        Optional<BasicInfoDetailVo> opt = basicInfoService.listBasicDetailInfoByIds(Collections.singleton(id))
            .stream().findFirst();
        return DefaultHttpResult.successWithData(opt.orElse(null));
    }

    @PostMapping("/updateBasicInfo")
    @Operation(summary = "更新接口", method = "POST", description = "更新基础信息")
    public IHttpResult<Boolean> updateBasicInfo(@RequestBody @Validated BasicInfoUpdateReq req) {
        return DefaultHttpResult.successWithData(basicInfoService.updateBasicInfo(req));
    }

    @GetMapping("/getBasicInfoByKey")
    @Operation(summary ="获取配置列表",   description = "获取配置列表")
    public IHttpResult<List<BasicInfoDetailVo>> getBasicInfoByKey(@RequestParam("key") @NotBlank String key) {
        return DefaultHttpResult.successWithData(basicInfoService.getBasicInfoByKey(key));
    }

    @GetMapping("/deleteCheck")
    @Operation(summary ="广告位删除校验",   description = "true->可以删除，false->不能删除")
    public IHttpResult<Boolean> deleteCheck(@RequestParam("basicKey") @NotBlank String basicKey) {
        return DefaultHttpResult.successWithData(basicInfoService.deleteCheck(basicKey));
    }
}
