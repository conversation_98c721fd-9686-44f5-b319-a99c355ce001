package cn.jojo.front.jaguar.admin.model.req.activity;

import cn.jojo.front.jaguar.common.enums.task.ActivityThemeSceneTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 活动主题列表查询
 *
 * <AUTHOR>
 * @since 2025/4/24 16:39
 */

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ActivityThemePageQueryReq {


    @Schema(description = "主题id")
    private Long id;

    @Schema(description = "主题名称")
    private String name;

    @Schema(description = "主题场景", implementation = ActivityThemeSceneTypeEnum.class)
    private String scene;

    /**
     * 品类
     *
     * @see cn.jojo.cc.common.enums.SubjectTypeEnum
     */
    @Schema(description = "品类")
    private Integer subjectType;

    /**
     * 页码
     */
    @Schema(description = "页码")
    private Long pageNum;

    /**
     * 页大小
     */
    @Schema(description = "页大小")
    private Long pageSize;
}
