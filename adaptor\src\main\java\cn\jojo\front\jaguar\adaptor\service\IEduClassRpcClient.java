package cn.jojo.front.jaguar.adaptor.service;


import cn.jojo.front.jaguar.adaptor.model.req.EduClassReq;
import cn.jojo.front.jaguar.common.pojo.bo.UserCourseExtensionQueryBo;
import cn.jojo.front.jaguar.common.pojo.bo.UserCourseQueryBo;
import cn.jojo.front.jaguar.common.pojo.vo.UserCourseExtensionVo;
import cn.jojo.front.jaguar.common.pojo.vo.eduUserClass.EduClassVo;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;

public interface IEduClassRpcClient {


    /**
     * 分页查询
     *
     * @param queryVo
     * @return
     */
    IPage<UserCourseExtensionVo> queryExtension(UserCourseExtensionQueryBo<UserCourseQueryBo> queryVo);

    List<EduClassVo> queryClassesByClassIds(EduClassReq req);
}
