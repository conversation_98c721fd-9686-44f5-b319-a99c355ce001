package cn.jojo.front.jaguar.admin.controller.activity;

import cn.jojo.front.jaguar.admin.model.dto.task.ThirdRewardQueryDto;
import cn.jojo.front.jaguar.admin.model.req.QueryRewardReq;
import cn.jojo.front.jaguar.biz.service.activitity.ICardBizService;
import cn.jojo.front.jaguar.biz.service.activitity.IStickerBizService;
import cn.jojo.front.jaguar.biz.service.activitity.IStickerPagesBizService;
import cn.jojo.front.jaguar.common.enums.task.RewardTypeEnum;
import cn.jojo.front.jaguar.common.pojo.req.ListLinkSkusReq;
import cn.jojo.front.jaguar.common.pojo.vo.activity.CardVo;
import cn.jojo.front.jaguar.common.pojo.vo.activity.StickerPageVo;
import cn.jojo.front.jaguar.common.pojo.vo.activity.StickerVo;
import cn.jojo.infra.sdk.api.metadata.DefaultHttpResult;
import cn.jojo.infra.sdk.api.metadata.IHttpResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;

import java.util.Objects;

import org.hibernate.validator.constraints.Length;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 贴纸controller
 * @date 2024/9/23 19:36
 **/

@Validated
@RestController

@RequestMapping("admin/rewards")
@Tag(name = "激励系统关联的外部奖励")
public class RewardController {

    @Resource
    private ICardBizService cardBizService;

    @Operation(summary = "奖励信息查询", description = "奖励信息查询,根据key+类型查询活动卡等")
    @GetMapping("/{rewardKey}")
    public IHttpResult<ThirdRewardQueryDto> getReward(@Valid
                                                      @Schema(description = "对应的奖励key，比如活动卡key。用该key去反查其它系统获取相关信息，必填")
                                                      @Length(max = 20)
                                                      @NotBlank(message = "rewardKey must not blank")
                                                      @PathVariable String rewardKey,

                                                      @Validated   QueryRewardReq queryRewardReq,

                                                      @Schema(description = "奖励类型，和rewardKey搭配使用，用于区分不同的奖励信息查询策略，为空时默认活动卡类型", implementation = RewardTypeEnum.class)
                                                      @Positive @RequestParam(value = "rewardType", required = false) Integer rewardType
                                               ) {
        if (Objects.isNull(rewardType)||RewardTypeEnum.CARD.getValue().equals(rewardType)){
           CardVo cardInfo =   cardBizService.getCardInfo(rewardKey, queryRewardReq.getCourseId());
           if (Objects.isNull(cardInfo)){
               return DefaultHttpResult.successWithoutData();
           }
           ThirdRewardQueryDto thirdRewardQueryDto = ThirdRewardQueryDto.builder()
                .rewardType(rewardType).rewardId(cardInfo.getId())
                .rewardName(cardInfo.getName())
                .rewardKey(cardInfo.getCardKey()).build();
            return DefaultHttpResult.successWithData(thirdRewardQueryDto);
        }else {
            //其它类型需要自行扩充
            return DefaultHttpResult.successWithoutData();
        }
    }

}
