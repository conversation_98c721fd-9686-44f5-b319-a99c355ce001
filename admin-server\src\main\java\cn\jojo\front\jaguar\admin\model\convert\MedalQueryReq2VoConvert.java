package cn.jojo.front.jaguar.admin.model.convert;

import cn.jojo.edu.common.utils.convert.BaseModelConvert;
import cn.jojo.front.jaguar.common.pojo.req.task.MedalQueryReq;
import cn.jojo.front.jaguar.biz.service.pojo.bo.task.MedalQueryVo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * vo转换
 *
 * <AUTHOR>
 * @date 2022/05/16
 */
@Mapper
public interface MedalQueryReq2VoConvert extends BaseModelConvert< MedalQueryReq, MedalQueryVo> {

    MedalQueryReq2VoConvert INSTANCE = Mappers.getMapper(MedalQueryReq2VoConvert.class);

}
