package cn.jojo.front.jaguar.adaptor.service;

import cn.jojo.front.jaguar.common.pojo.bo.UserBabyBo;

/**
 * @description:
 * @author: luohuanrong
 * @create: 2023/6/6
 **/
public interface IUserBabyRpcClient {

    /**
     * 获取用户宝贝信息
     * @param userId
     * @return
     */
    UserBabyBo getUserBabyInfo(Long userId);

    /**
     * 查询用户信息（支持脱敏）
     *
     * @param userId              用户ID
     * @param needDesensitize     是否需要脱敏
     * @return UserBaby
     */
    UserBabyBo getUserBabyInfoWithSensitive(Long userId, boolean needDesensitize);

}
