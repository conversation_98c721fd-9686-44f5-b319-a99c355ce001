package cn.jojo.front.jaguar.admin.model.dto.task;

import cn.jojo.front.jaguar.common.enums.task.CourseTypeEnum;
import cn.jojo.front.jaguar.common.enums.task.TaskCategoryType;
import cn.jojo.front.jaguar.common.enums.task.TaskModelType;
import cn.jojo.front.jaguar.common.enums.task.TaskType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2024/03/11
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema(description="任务详情")
public class TaskDto {

    private Long id;

    @Schema(description="任务名称")
    private String name;

    @Schema(description="说明")
    private String description;

    @Schema(description="备注")
    private String remark;

    @Schema(description="品类类型")
    private String subjectType;

    @Schema(description="品类名称")
    private String subjectTypeName;

    @Schema(description="计划id")
    private String courseId;

    @Schema(description="计划名称")
    private String courseName;

    @Schema(description="排序")
    private Integer sort;

    @Schema(description="模式",implementation = TaskModelType.class)
    private String modelType;

    @Schema(description="任务类型", implementation = TaskType.class)
    private String taskType;

    @Schema(description="任务类型")
    private String taskTypeName;

    @Schema(description="类别", implementation = TaskCategoryType.class)
    private String category;

    @Schema(description="任务语音")
    private List<TaskVoiceDto> taskVoices;

    private Long updateTime;

    @Schema(description="子任务/条件")
    private List<SubTaskDto> subTaskList;

    @Schema(description = "产品类型", implementation = CourseTypeEnum.class)
    private Integer courseType;

    @Schema(description = "产品类型描述", implementation = CourseTypeEnum.class)
    private String courseTypeName;
}
