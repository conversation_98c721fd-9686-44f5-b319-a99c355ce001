package cn.jojo.front.jaguar.adaptor.service;

import cn.jojo.cc.common.dto.SegmentDto;
import cn.jojo.cc.common.dto.SubjectTypeDto;
import cn.jojo.edu.malacca.rpc.api.dto.EduClassDto;
import cn.jojo.edu.malacca.rpc.api.dto.EduTeacherDto;
import cn.jojo.front.jaguar.common.pojo.vo.CourseSearchVo;
import cn.jojo.front.jaguar.common.pojo.vo.CourseSegmentListVo;
import cn.jojo.front.jaguar.common.pojo.vo.PageJumpVo;
import cn.jojo.uc.api.domain.dto.DictDto;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface ConditionParamService {

    /**
     * 获取所有科目
     *
     * @return 科目列表
     */
    List<SubjectTypeDto> getAllSubjectType();

    /**
     * 筛选课程信息
     *
     * @param courseType        课程类型
     * @param subjectType       科目类型
     * @param courseSegmentCodes 阶段
     * @param keyword           关键字筛选，全匹配
     * @return 课程列表
     */
    List<CourseSearchVo> listCourse(Integer courseType, Integer subCourseType, Integer subjectType,
                                    List<Integer> courseSegmentCodes, String keyword);

    /**
     * 筛选班级信息
     *
     * @param courseIds 所属课程
     * @param classIds  班级id
     * @return 班级信息列表
     */
    List<EduClassDto> listClass(List<Long> courseIds, List<Long> classIds);

    /**
     * 获取所有主题
     *
     * @param courseId 课程id
     * @return 主题类别列表
     */
    List<SegmentDto> listSubject(Integer courseId);

    /**
     * 获取所有的页面url
     *
     * @return 页面URL列表
     */
    List<PageJumpVo> listPageUrl();

    /**
     * 获取指定班级老师信息
     *
     * @return 老师信息
     */
    List<EduTeacherDto> listTeacher(List<Integer> classIds);

    /**
     * 获取年级列表
     *
     * @return 年级字典信息
     */
    List<DictDto> listGrade();
}
