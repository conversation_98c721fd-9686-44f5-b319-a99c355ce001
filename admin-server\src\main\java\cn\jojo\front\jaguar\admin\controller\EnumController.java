package cn.jojo.front.jaguar.admin.controller;

import cn.jojo.front.jaguar.adaptor.service.UserGroupService;
import cn.jojo.front.jaguar.biz.service.EnumService;
import cn.jojo.front.jaguar.common.enums.SystemActivityEnum;
import cn.jojo.front.jaguar.common.pojo.req.HubListReq;
import cn.jojo.front.jaguar.common.pojo.req.TopicListReq;
import cn.jojo.front.jaguar.common.pojo.vo.CommonEnumVo;
import cn.jojo.front.jaguar.common.pojo.vo.CourseSegmentListVo;
import cn.jojo.front.jaguar.common.pojo.vo.CourseTypeListVo;
import cn.jojo.front.jaguar.common.pojo.vo.HubItemVo;
import cn.jojo.front.jaguar.common.pojo.vo.SectionVo;
import cn.jojo.front.jaguar.common.pojo.vo.TopicItemVo;
import cn.jojo.front.jaguar.common.pojo.vo.UserGroupListVo;
import cn.jojo.front.jaguar.common.pojo.vo.VersionIntervalVo;
import cn.jojo.infra.sdk.api.metadata.DefaultHttpResult;
import cn.jojo.infra.sdk.api.metadata.DefaultPageResp;
import cn.jojo.infra.sdk.api.metadata.IHttpResult;
import cn.jojo.infra.sdk.api.metadata.IPageResp;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.common.collect.Lists;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 **/
@Tag(name = "枚举类接口")
@RestController
@RequestMapping("admin/enum")
@Validated
public class EnumController extends BaseController {

    @Resource
    private EnumService enumService;
    @Resource
    private UserGroupService userGroupService;

    @Operation(tags = "枚举类获取", method = "GET", hidden = true, description = "根据枚举类路径获取枚举类实现" +
        "cn.jojo.front.jaguar.common.enums.CourseStatus - 课程状态,\"\n" +
        "\t\t\t+ \"cn.jojo.front.jaguar.common.enums.CustomizeRuleType - 自定义规则类型\"\n" +
        "\t\t\t+ \"cn.jojo.front.jaguar.common.enums.ButtonNumber - 按钮数量,\"\n" +
        "\t\t\t+ \"cn.jojo.front.jaguar.common.enums.ConfigType - 配置类型,\"\n" +
        "\t\t\t+ \"cn.jojo.front.jaguar.common.enums.CourseAction - 课程动作,\"\n" +
        "\t\t\t+ \"cn.jojo.front.jaguar.common.enums.LinkType - 链接类型,\"\n" +
        "\t\t\t+ \"cn.jojo.front.jaguar.common.enums.PlatformType - 平台类型,\"\n" +
        "\t\t\t+ \"cn.jojo.front.jaguar.common.enums.PromptPeriod - 提醒周期,\"\n" +
        "\t\t\t+ \"cn.jojo.front.jaguar.common.enums.PublishPlatform - 发布平台,\"\n" +
        "\t\t\t+ \"cn.jojo.front.jaguar.common.enums.RuleStatus - 规则状态,\"\n" +
        "\t\t\t+ \"cn.jojo.front.jaguar.common.enums.RuleType - 规则类型,\"\n" +
        "\t\t\t+ \"cn.jojo.front.jaguar.common.enums.ShareConfigType - 分享配置类型,\"\n" +
        "\t\t\t+ \"cn.jojo.front.jaguar.common.enums.StyleType - 样式类型,\"\n" +
        "\t\t\t+ \"cn.jojo.front.jaguar.common.enums.SystemInvokeEnum - 系统调用类型,\"\n" +
        "\t\t\t+ \"cn.jojo.front.jaguar.common.enums.TakeEffectStatus - 生效状态")
    @Parameter(name = "enumPath", description = "枚举路径", schema = @Schema(type = "string"), example = "cn.jojo.front.jaguar.common.enums.CustomizeRuleType")
    @ApiResponses(
        @ApiResponse(code = 200, message = "枚举全部值response:{\"code\":\"SUCCESS\",\"message\":\"ok\",\"subCode\":null,\"subMessage\":null,\"data\":[{\"name\":\"N_2\",\"value\":2,\"desc\":\"2\"},{\"name\":\"N_3\",\"value\":3,\"desc\":\"3\"},{\"name\":\"N_4\",\"value\":4,\"desc\":\"4\"}]}")
    )
    @GetMapping("/get")
    public List<Enum> getEnumInfo(@RequestParam("enumPath") @NotBlank String enumPath) {
        return enumService.getEnumInfo(enumPath);
    }


    @Operation(summary ="获取系统调用活动类型",   description = "根据父系统code获取对应系统下的活动类型枚举")
    @Parameter(name = "parentCode", description = "父系统枚举value", schema = @Schema(type = "integer"), required = true)
    @GetMapping("/getActivity")
    public List<SystemActivityEnum> getActivity(Integer parentCode) {
        if (parentCode == null) {
            return Lists.newArrayList();
        }
        return SystemActivityEnum.getSystemActivityEnumByParentCode(parentCode);
    }

    @Operation(summary ="课程类型列表",   description = "获取课程类型列表")
    @GetMapping("/listCourseType")
    public IHttpResult<List<CourseTypeListVo>> listCourseType() {
        List<CourseTypeListVo> resultList = enumService.getCourseTypeList();
        return DefaultHttpResult.successWithData(resultList);
    }

    @Operation(summary ="课程阶段列表",   description = "获取阶段列表")
    @GetMapping("/listCourseStep")
    public IHttpResult<List<CourseSegmentListVo>> listCourseSegment() {
        List<CourseSegmentListVo> resultList = enumService.getCourseSegmentList();
        return DefaultHttpResult.successWithData(resultList);
    }

    @Operation(summary ="板块类型列表",   description = "获取板块类型列表")
    @GetMapping("/listSections")
    public IHttpResult<List<SectionVo>> listSections() {
        return DefaultHttpResult.successWithData(enumService.getSectionList());
    }

    @Operation(summary ="版本范围列表",   description = "获取版本范围列表")
    @GetMapping("/listVersionInterval")
    public IHttpResult<List<VersionIntervalVo>> listVersionInterval() {
        return DefaultHttpResult.successWithData(enumService.listVersionIntervals());
    }

    @Operation(summary ="获取用户分群列表",   description = "获取用户分群列表")
    @GetMapping("/listUserGroups")
    public IHttpResult<List<UserGroupListVo>> listUserGroups() {
        return DefaultHttpResult.successWithData(userGroupService.listUserGroups());
    }

    @Operation(summary ="作品筛选类型列表",   description = "获取作品筛选类型列表")
    @GetMapping("/listOpusType")
    public IHttpResult<List<CommonEnumVo>> listOpusType() {
        return DefaultHttpResult.successWithData(enumService.listOpusType());
    }

    @Operation(summary ="获取灰度维度",   description = "获取灰度维度")
    @GetMapping("/listGrayDimension")
    public IHttpResult<List<CommonEnumVo>> listGrayDimension() {
        return DefaultHttpResult.successWithData(enumService.listGrayDimension());
    }

    @Operation(summary ="获取所有tab页",   description = "获取所有tab")
    @GetMapping("/listAllTab")
    public IHttpResult<List<CommonEnumVo>> listAllTab() {
        return DefaultHttpResult.successWithData(enumService.listAllTab());
    }

    @Operation(summary ="获取所有功能类型",   description = "获取所有功能类型")
    @GetMapping("/listAllFunctionType")
    public IHttpResult<List<CommonEnumVo>> listAllFunctionType() {
        return DefaultHttpResult.successWithData(enumService.listAllFunctionType());
    }

    @Operation(summary ="获取所有系统配置类型",   description = "获取所有系统配置类型")
    @GetMapping("/listAllSystemCallType")
    public IHttpResult<List<CommonEnumVo>> listAllSystemCallType() {
        return DefaultHttpResult.successWithData(enumService.listAllSystemCallType());
    }

    @GetMapping("/listSquareMaterialType")
    @Operation(summary ="获取所有广场素材类型",   description = "获取所有广场素材类型")
    public IHttpResult<List<CommonEnumVo>> listSquareMaterialType() {
        return DefaultHttpResult.successWithData(enumService.listSquareMaterialType());
    }

    @Operation(summary ="获取所有跳转类型",   description = "获取所有跳转类型")
    @GetMapping("/listAllJumpType")
    public IHttpResult<List<CommonEnumVo>> listAllJumpType() {
        return DefaultHttpResult.successWithData(enumService.listAllJumpType());
    }

    @Operation(summary ="分页获取专辑列表")
    @GetMapping("/hub/list")
    public IPageResp<HubItemVo> listHub(@Validated HubListReq req) {
        req.setPageNum(Optional.ofNullable(req.getPageNum()).orElse(1));
        req.setPageSize(Optional.ofNullable(req.getPageSize()).orElse(50L));
        IPage<HubItemVo> page = enumService
            .listHubInfo(req.getHubName(), req.getHubIds(), req.getPageNum(), req.getPageSize());
        return DefaultPageResp.buildPageResp(req.getPageNum(), req.getPageSize(), page.getTotal(), page.getRecords());
    }

    @Operation(summary ="分页获取专题列表")
    @GetMapping("/topic/list")
    public IPageResp<TopicItemVo> listTopic(TopicListReq req) {
        req.setPageNum(Optional.ofNullable(req.getPageNum()).orElse(1));
        req.setPageSize(Optional.ofNullable(req.getPageSize()).orElse(50));
        IPage<TopicItemVo> page = enumService.listTopicInfo(req);
        return DefaultPageResp.buildPageResp(req.getPageNum(), req.getPageSize(), page.getTotal(), page.getRecords());
    }
}
