package cn.jojo.front.jaguar.adaptor.service.impl;

import cn.jojo.edu.wings.rpc.api.dto.notify.EduNotifyDto;
import cn.jojo.edu.wings.rpc.api.req.notify.EduNotifyReq;
import cn.jojo.edu.wings.rpc.api.service.INotifyRpcService;
import cn.jojo.front.jaguar.common.pojo.bo.notifyaudit.EduNotifyBo;
import cn.jojo.front.jaguar.common.utils.ModelConvertUtil;
import cn.jojo.infra.sdk.api.metadata.IRpcResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

@Service
@Slf4j
public class EduNotifyRpcClient {


    @DubboReference
    private INotifyRpcService notifyRpcService;

    /**
     * 根据notifyId查询教务中台活动通知详情内容
     *
     * @param notifyId
     * @return
     */
    public EduNotifyBo queryByEduNotifyId(Long notifyId) {
        if (notifyId == null) {
            return null;
        }
        EduNotifyReq eduNotifyReq = new EduNotifyReq();
        eduNotifyReq.setIds(Collections.singletonList(notifyId));
        try {
            IRpcResult<List<EduNotifyDto>> rpcResult = notifyRpcService.getNotifys(eduNotifyReq);
            if (!rpcResult.checkSuccess()) {
                log.error("查询教务通知内容失败, message:{}", rpcResult.getMessage());
            }
            if (CollectionUtils.isNotEmpty(rpcResult.getData())) {
                return ModelConvertUtil.build(rpcResult.getData().get(0), EduNotifyBo.class);
            }
        } catch (Exception e) {
            log.error("查询教务通知内容失败", e);
        }
        return null;
    }
}
