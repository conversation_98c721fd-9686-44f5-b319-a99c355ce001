package cn.jojo.front.jaguar.adaptor.service.impl;

import cn.jojo.front.jaguar.adaptor.service.IUserGrayRpcClient;
import cn.jojo.pagani.rpc.api.domain.dto.gray.GrayValueDTO;
import cn.jojo.pagani.rpc.api.domain.req.gray.LearnPageGrayReq;
import cn.jojo.pagani.rpc.api.service.UserGrayRpcService;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025/5/13 17:57
 */
@Component
public class UserGrayRpcClient extends AbstractRpcClient implements IUserGrayRpcClient {

    @DubboReference
    private UserGrayRpcService userGrayRpcService;

    @Override
    public Optional<GrayValueDTO> getLearnPageGrayValue(LearnPageGrayReq req) {
        return doRpc(req, userGrayRpcService::getLearnPageGrayValue);
    }
}
