package cn.jojo.front.jaguar.admin.model.convert;

import cn.jojo.edu.common.utils.convert.BaseModelConvert;
import cn.jojo.front.jaguar.biz.service.pojo.bo.task.StockAdminVo;
import cn.jojo.front.jaguar.common.pojo.entity.season.Stock;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @date 2025/01/07
 **/
@Mapper
public interface StockPageVoConvert  extends BaseModelConvert<Stock, StockAdminVo> {
    StockPageVoConvert INSTANCE  = Mappers.getMapper(StockPageVoConvert.class);

}
