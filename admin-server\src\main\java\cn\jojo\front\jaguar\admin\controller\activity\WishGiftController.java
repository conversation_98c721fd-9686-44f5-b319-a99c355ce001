package cn.jojo.front.jaguar.admin.controller.activity;

import cn.jojo.front.jaguar.admin.model.convert.WishGiftReq2BoConvert;
import cn.jojo.front.jaguar.admin.model.req.activity.WishGiftReq;
import cn.jojo.front.jaguar.biz.service.activitity.IWishGiftBizService;
import cn.jojo.front.jaguar.biz.service.pojo.bo.activity.WishGiftBo;
import cn.jojo.front.jaguar.common.pojo.vo.activity.WishGiftVo;
import cn.jojo.infra.sdk.api.metadata.DefaultHttpResult;
import cn.jojo.infra.sdk.api.metadata.IHttpResult;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 心愿礼物controller
 * @date 2024/3/14 16:17
 **/

@Validated
@RestController
@RequestMapping("admin/wish-gifts")
@Tag(name = "心愿礼物")
public class WishGiftController {

    @Resource
    private IWishGiftBizService wishGiftBizService;

    @Operation(summary = "心愿礼物分页查询", description = "心愿礼物分页查询, 分页查询和回显接口")
    @GetMapping
    public IHttpResult<IPage<WishGiftVo>> pageInfo(@ModelAttribute @Valid WishGiftReq wishGiftReq) {

        WishGiftBo wishGiftBo = WishGiftReq2BoConvert.INSTANCE.model1ToModel2(wishGiftReq);
        return DefaultHttpResult.successWithData(wishGiftBizService.pageInfo(wishGiftBo));
    }
}
