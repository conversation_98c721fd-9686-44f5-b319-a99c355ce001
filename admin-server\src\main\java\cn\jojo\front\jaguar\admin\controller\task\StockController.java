package cn.jojo.front.jaguar.admin.controller.task;

import cn.jojo.edu.common.utils.exceptions.ParamValidException;
import cn.jojo.front.jaguar.adaptor.service.IOAEmployRpcClient;
import cn.jojo.front.jaguar.admin.model.convert.StockSaveReq2OperateVoConvert;
import cn.jojo.front.jaguar.admin.model.convert.StockUpdateReq2OperateVoConvert;
import cn.jojo.front.jaguar.admin.model.req.task.StockPageReq;
import cn.jojo.front.jaguar.admin.model.req.task.StockSaveReq;
import cn.jojo.front.jaguar.admin.model.req.task.StockUpdateReq;
import cn.jojo.front.jaguar.biz.service.pojo.bo.task.StockAdminVo;
import cn.jojo.front.jaguar.biz.service.season.IStockAdminBizService;
import cn.jojo.front.jaguar.biz.service.season.IStockBizService;
import cn.jojo.front.jaguar.common.enums.TableSortField;
import cn.jojo.front.jaguar.common.enums.TableSortType;
import cn.jojo.front.jaguar.common.enums.season.StockCategoryType;
import cn.jojo.front.jaguar.common.enums.season.StockType;
import cn.jojo.front.jaguar.common.pojo.vo.season.StockDetailAdminVo;
import cn.jojo.front.jaguar.common.pojo.vo.season.StockOperateVo;
import cn.jojo.front.jaguar.common.pojo.vo.season.StockQuerySortVo;
import cn.jojo.front.jaguar.common.pojo.vo.season.StockQueryVo;
import cn.jojo.infra.sdk.api.metadata.DefaultHttpResult;
import cn.jojo.infra.sdk.api.metadata.DefaultPageResp;
import cn.jojo.infra.sdk.api.metadata.IHttpResult;
import cn.jojo.infra.sdk.api.metadata.IPageResp;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.NoSuchElementException;
import java.util.Objects;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import org.apache.curator.shaded.com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 装扮资源管理
 *
 * <AUTHOR>
 * @date 2025/01/07
 **/
@Validated
@RestController
@RequestMapping("/admin/stocks")
@Tag(name = "装扮管理")
public class StockController {
    @Autowired
    private IStockBizService stockBizService;
    @Autowired
    private IOAEmployRpcClient oaEmployRpcClient;
    @Autowired
    private IStockAdminBizService stockAdminBizService;

    @GetMapping
    @Operation(summary = "装扮列表", description = "装扮列表")
    public IPageResp<StockAdminVo> page(StockPageReq req) {
        checkTypeParams(req.getCategoryType(), req.getStockType());
        StockQueryVo stockQueryVo = buildQueryVo(req);
        IPage<StockAdminVo> stockPage = stockAdminBizService.queryStockPage(stockQueryVo);
        return DefaultPageResp.buildPageResp(stockPage.getCurrent(), stockPage.getSize(), stockPage.getTotal(),
            stockPage.getRecords());
    }


    @PostMapping
    @Operation(summary = "新增", description = "装扮新增")
    public IHttpResult<Void> save(@Validated @RequestBody StockSaveReq req) {
        checkTypeParams(req.getCategoryType(), req.getStockType());
        StockOperateVo stockOperateVo = StockSaveReq2OperateVoConvert.INSTANCE.model1ToModel2(req);
        stockBizService.save(stockOperateVo);
        return DefaultHttpResult.successWithoutData();
    }

    @PatchMapping("/{id}")
    @Operation(summary = "编辑修改",description = "修改装扮信息")
    public IHttpResult<Void> update(@NotNull @PathVariable(value = "id") Long id,
                                    @Validated @RequestBody StockUpdateReq req) {
        checkTypeParams(req.getCategoryType(), req.getStockType());
        StockOperateVo stockOperateVo = StockUpdateReq2OperateVoConvert.INSTANCE.model1ToModel2(req);
        stockBizService.updateById(stockOperateVo);
        return DefaultHttpResult.successWithoutData();
    }

    @GetMapping("/{id}")
    @Operation(summary = "查看明细", description = "查看装扮信息")
    public IHttpResult<StockDetailAdminVo> findById(
        @Schema(description="装扮")
        @NotNull @Positive @PathVariable(value = "id") Long id) {
        StockDetailAdminVo stockDetailAdminVo = stockAdminBizService.queryStockDetail(id);
        return DefaultHttpResult.successWithData(stockDetailAdminVo);
    }



    private StockQueryVo buildQueryVo(StockPageReq req) {
        if (Objects.isNull(req)){
            return StockQueryVo.builder().pageSize(20L).pageNum(1L).build();
        }
        StockQueryVo stockQueryVo = StockQueryVo.builder()
            .stockName(req.getStockName())
         .build();
        if (Objects.nonNull(req.getId())){
            stockQueryVo.setStockIds(Lists.newArrayList(req.getId()));
        }
        if (Objects.nonNull(req.getStockType())){
            stockQueryVo.setStockTypes(StockType.convertStockTypes(Lists.newArrayList(req.getStockType())));
        }
        if (Objects.nonNull(req.getCategoryType())){
            stockQueryVo.setCategoryType(StockCategoryType.convertCategoryType(req.getCategoryType()));
        }
        if (Objects.nonNull(req.getPageNum())){
            stockQueryVo.setPageNum(req.getPageNum());
        } else {
            stockQueryVo.setPageNum(1L);
        }

        if (Objects.nonNull(req.getPageSize())) {
            stockQueryVo.setPageSize(req.getPageSize());
        } else {
            stockQueryVo.setPageSize(10L);
        }
        if (Objects.nonNull(req.getSupportBuy())) {
            stockQueryVo.setSupportBuy(req.getSupportBuy());
        }
        //默认ID降序排序
        stockQueryVo.setSortFields(Lists.newArrayList(StockQuerySortVo.builder().sortField(TableSortField.ID).sortType(
            TableSortType.DESC).build()));
        return stockQueryVo;
    }


    private void checkTypeParams(Integer categoryType, Integer stockType) {

        try {
            if (Objects.nonNull(categoryType)) {
                StockCategoryType.convertCategoryType(categoryType);
            }
            if (Objects.nonNull(stockType)) {
                StockType.convertStockType(stockType);
            }
        } catch (NoSuchElementException e) {
            throw new ParamValidException(e.getMessage());
        }
    }

}
