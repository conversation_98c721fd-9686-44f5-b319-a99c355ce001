package cn.jojo.front.jaguar.admin.controller.tag;

import cn.jojo.common.api.ex.DefaultHttpActionPageResult;
import cn.jojo.common.api.ex.DefaultHttpActionResult;
import cn.jojo.common.api.ex.IHttpActionResult;
import cn.jojo.front.jaguar.adaptor.service.UserGroupTaskService;
import cn.jojo.front.jaguar.common.pojo.bo.PageResultBo;
import cn.jojo.front.jaguar.common.pojo.req.UserGroupTaskRecordListReq;
import cn.jojo.front.jaguar.common.pojo.vo.UserGroupSyncResultVo;
import cn.jojo.front.jaguar.common.pojo.vo.UserGroupTaskListVo;
import cn.jojo.infra.sdk.api.metadata.IPageResp;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("admin/user/group/task")
@Tag(name = "fb-jaguar管理后台")
@Validated
public class UserGroupTaskController {

    @Resource
    private UserGroupTaskService userGroupTaskService;

    @GetMapping("/start")
    @Operation(summary ="立即同步接口")
    public IHttpActionResult<UserGroupSyncResultVo> start(@RequestParam @NotNull @Positive Long id) {
        UserGroupSyncResultVo result = userGroupTaskService.syncNow(id);
        return DefaultHttpActionResult.successWithData(result);
    }

    @GetMapping("/list")
    @Operation(summary ="获取分群同步历史")
    public IHttpActionResult<IPageResp<UserGroupTaskListVo>> list(UserGroupTaskRecordListReq req) {
        req.setDefault();
        PageResultBo<UserGroupTaskListVo> result = userGroupTaskService.listUserGroupTaskPage(req);
        return DefaultHttpActionPageResult.successWithPageData(
                result.getPageNum(), result.getPageSize(), result.getTotal(), result.getRecords());
    }

    @GetMapping("/stop")
    @Operation(summary ="停止同步")
    public IHttpActionResult<Boolean> stopTask(@RequestParam @NotNull @Positive Long id) {
        boolean result = userGroupTaskService.stopTask(id);
        return DefaultHttpActionResult.successWithData(result);
    }
}
