package cn.jojo.front.jaguar.adaptor.service;

import cn.jojo.front.jaguar.common.pojo.bo.PageResultBo;
import cn.jojo.front.jaguar.common.pojo.req.FbUserGroupListQueryReq;
import cn.jojo.front.jaguar.common.pojo.req.FbUserGroupSaveReq;
import cn.jojo.front.jaguar.common.pojo.vo.FbTagUserGroupListVo;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface FbTagUserGroupRpcService {

    PageResultBo<FbTagUserGroupListVo> listUserGroupPages(FbUserGroupListQueryReq req, Long employeeId);

    boolean updateUserGroupStatus(Long id, String status);

    boolean addUserGroup(FbUserGroupSaveReq req);

    /**
     * 使用uid匹配分群id
     *
     * @param groupIds 目标分群id
     * @param userId   用户id
     * @return 匹配结果
     */
    Map<Long, Boolean> matchUserGroup(List<Long> groupIds, Long userId);

    /**
     * 获取分群名称
     *
     * @param groupIds 分群id
     * @return 分群名称信息
     */
    Map<Long, String> getGroupNames(List<Long> groupIds);
}
