package cn.jojo.front.jaguar.adaptor.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.jojo.cc.api.service.BasicService;
import cn.jojo.cc.common.dto.DictDto;
import cn.jojo.front.jaguar.adaptor.service.BasicServiceClient;
import cn.jojo.infra.sdk.api.metadata.IRpcResult;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.mockito.Mockito.*;
import static org.junit.jupiter.api.Assertions.*;

/**
 * 单元测试：BasicServiceClientImpl.getDictDtoList()
 */
@ExtendWith(MockitoExtension.class)
public class BasicServiceClientImplTest {

    @InjectMocks
    private BasicServiceClient basicServiceClient = new BasicServiceClientImpl();

    @Mock
    private BasicService basicService;

    @Mock
    private IRpcResult<List<DictDto>> rpcResult;

    // 用于模拟 DictDto 数据
    private List<DictDto> mockData;

    @BeforeEach
    void setUp() {
        mockData = new ArrayList<>();
        mockData.add(new DictDto());
    }

    /**
     * 测试正常情况：RPC 成功且有数据
     */
    @Test
    public void testGetDictDtoList_SuccessWithData() {
        when(basicService.getDictInfoByTypeByTenant("testType")).thenReturn(rpcResult);
        when(rpcResult.checkSuccess()).thenReturn(true);
        when(rpcResult.getData()).thenReturn(mockData);

        List<DictDto> result = basicServiceClient.getDictDtoList("testType");

        assertNotNull(result);
        assertEquals(1, result.size());
    }

    /**
     * 测试异常情况：rpcResult 为 null
     */
    @Test
    public void testGetDictDtoList_ResultIsNull() {
        when(basicService.getDictInfoByTypeByTenant("nullResult")).thenReturn(null);

        List<DictDto> result = basicServiceClient.getDictDtoList("nullResult");

        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * 测试异常情况：checkSuccess 返回 false
     */
    @Test
    public void testGetDictDtoList_CheckSuccessFalse() {
        when(basicService.getDictInfoByTypeByTenant("fail")).thenReturn(rpcResult);
        when(rpcResult.checkSuccess()).thenReturn(false);

        List<DictDto> result = basicServiceClient.getDictDtoList("fail");

        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * 测试异常情况：getData 返回 null
     */
    @Test
    public void testGetDictDtoList_DataIsNull() {
        when(basicService.getDictInfoByTypeByTenant("noData")).thenReturn(rpcResult);
        when(rpcResult.checkSuccess()).thenReturn(true);
        when(rpcResult.getData()).thenReturn(null);

        List<DictDto> result = basicServiceClient.getDictDtoList("noData");

        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * 测试异常情况：getData 返回空集合
     */
    @Test
    public void testGetDictDtoList_DataIsEmpty() {
        when(basicService.getDictInfoByTypeByTenant("emptyData")).thenReturn(rpcResult);
        when(rpcResult.checkSuccess()).thenReturn(true);
        when(rpcResult.getData()).thenReturn(Collections.emptyList());

        List<DictDto> result = basicServiceClient.getDictDtoList("emptyData");

        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * 测试异常情况：调用时抛出异常
     */
    @Test
    public void testGetDictDtoList_ThrowsException() {
        when(basicService.getDictInfoByTypeByTenant("error")).thenThrow(new RuntimeException("Network error"));

        List<DictDto> result = basicServiceClient.getDictDtoList("error");

        assertNotNull(result);
        assertTrue(result.isEmpty());
    }
}
