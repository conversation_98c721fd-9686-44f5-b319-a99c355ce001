package cn.jojo.front.jaguar.adaptor.service.impl;

import cn.jojo.front.jaguar.adaptor.service.IPlaceOrderProcessClient;
import cn.jojo.front.jaguar.common.pojo.bo.PlaceOrderParam;
import cn.jojo.front.jaguar.common.pojo.bo.PlaceOrderResultBo;
import cn.jojo.infra.sdk.api.metadata.IRpcResult;
import cn.tinman.sharedservices.mall.cashback.api.campaign.request.CampaignInfoReq;
import cn.tinman.sharedservices.mall.cashback.api.campaign.request.PlaceOrderReq;
import cn.tinman.sharedservices.mall.cashback.api.campaign.response.CampaignInfoResp;
import cn.tinman.sharedservices.mall.cashback.api.campaign.response.CampaignLinkResp;
import cn.tinman.sharedservices.mall.cashback.api.campaign.response.MaterielResp;
import cn.tinman.sharedservices.mall.cashback.api.campaign.response.PlaceOrderResp;
import cn.tinman.sharedservices.mall.cashback.api.campaign.service.ILinkApiService;
import cn.tinman.sharedservices.mall.cashback.api.campaign.service.IMaterielApiService;
import cn.tinman.sharedservices.mall.cashback.api.campaign.service.IPlaceOrderProcessService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class PlaceOrderProcessClient extends AbstractRpcClient implements IPlaceOrderProcessClient {

    @DubboReference
    private IPlaceOrderProcessService placeOrderProcessService;

    @DubboReference
    private ILinkApiService linkApiService;

    @DubboReference
    private IMaterielApiService materielApiService;

    @Override
    public Optional<CampaignInfoResp> getCampaignInfo(Long linkId, String ip) {
        if (linkId == null) {
            return Optional.empty();
        }
        CampaignInfoReq req = CampaignInfoReq.builder().linkId(linkId).ip(ip).build();
        IRpcResult<CampaignInfoResp> rpcResult;
        try {
            rpcResult = placeOrderProcessService.getCampaignInfo(req);
        } catch (Exception e) {
            log.error("invoke placeOrderProcessService.getCampaignInfo failed", e);
            return Optional.empty();
        }
        if (!rpcResult.checkSuccess()) {
            log.error("invoke placeOrderProcessService.getCampaignInfo failed, message={}", rpcResult.getMessage());
            return Optional.empty();
        }
        return Optional.ofNullable(rpcResult.getData());
    }

    @Override
    public Optional<CampaignLinkResp> getCampaignInfo(Long linkId) {
        if (Objects.isNull(linkId)) {
            return Optional.empty();
        }
        IRpcResult<CampaignLinkResp> rpcResult;
        try {
            rpcResult = linkApiService.getByLinkId(linkId);
        } catch (Exception e) {
            log.error("invoke placeOrderProcessService.getByLinkId failed", e);
            return Optional.empty();
        }
        if (!rpcResult.checkSuccess()) {
            log.error("invoke placeOrderProcessService.getByLinkId failed, message={}", rpcResult.getMessage());
            return Optional.empty();
        }
        return Optional.ofNullable(rpcResult.getData());
    }

    @Override
    public Optional<String> placeOrder(PlaceOrderParam param) {
        return placeOrderV2(param).map(PlaceOrderResultBo::getOrderId);
    }

    @Override
    public Optional<PlaceOrderResultBo> placeOrderV2(PlaceOrderParam param) {
        PlaceOrderReq req = PlaceOrderReq.builder().build();
        BeanUtils.copyProperties(param, req);
        try {
            IRpcResult<PlaceOrderResp> rpcResult = placeOrderProcessService.placeOrder(req);
            if (!rpcResult.checkSuccess()) {
                log.info("invoke placeOrderProcessService.placeOrder failed, message={}", rpcResult.getMessage());
                return Optional.empty();
            }
            return Optional.ofNullable(rpcResult.getData()).map(item -> new PlaceOrderResultBo()
                .setPreOrderId(item.getPreOrderId())
                .setOrderId(item.getOrderId())
                .setSoldNoteId(item.getSoldNoteId()));
        } catch (Exception e) {
            log.info("invoke placeOrderProcessService.placeOrder failed", e);
            return Optional.empty();
        }
    }

    @Override
    public List<MaterielResp> getBycampaignId(Long campaignId) {
        return doRpcList(campaignId, materielApiService::getBycampaignId);
    }
}
