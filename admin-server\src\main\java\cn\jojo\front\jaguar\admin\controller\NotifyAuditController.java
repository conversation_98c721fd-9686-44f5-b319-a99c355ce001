package cn.jojo.front.jaguar.admin.controller;


import cn.jojo.common.api.ex.DefaultHttpActionPageResult;
import cn.jojo.common.api.ex.DefaultHttpActionResult;
import cn.jojo.common.api.ex.IHttpActionResult;
import cn.jojo.front.jaguar.biz.service.impl.NotifyAuditBizService;
import cn.jojo.front.jaguar.common.exception.BusinessException;
import cn.jojo.front.jaguar.common.pojo.bo.notifyaudit.NotifyAuditQueryReq;
import cn.jojo.front.jaguar.common.pojo.vo.NotifyAuditVo;
import cn.jojo.infra.sdk.api.metadata.IPageResp;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.collections.CollectionUtils;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.Positive;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Tag(name = "通知内容审核管理")
@RestController
@RequestMapping("admin/notify-audits")
@Validated
public class NotifyAuditController extends BaseController {

    @Autowired
    private NotifyAuditBizService notifyAuditBizService;

    @Operation(summary = "查询通知内容审核列表", description = "查询通知内容审核列表")
    @GetMapping
    public IHttpActionResult<IPageResp<NotifyAuditVo>> queryPage(
            @ParameterObject @Validated NotifyAuditQueryReq queryReq) {
        if (queryReq == null || !queryReq.checkOptionParam()) {
            throw BusinessException.paramException("param error");
        }

        IPageResp<NotifyAuditVo> result = notifyAuditBizService.query(queryReq);
        if (CollectionUtils.isNotEmpty(result.getPageRecords())) {
            List<Long> employeeIds = result.getPageRecords().stream()
                .map(NotifyAuditVo::getOperatorId)
                .filter(Objects::nonNull)
                .distinct().collect(Collectors.toList());
            Map<Long, String> employeeNames = getEmployeeNames(employeeIds);
            result.getPageRecords().stream().filter(item -> item.getOperatorId() != null)
                .forEach(item -> item.setOperatorName(employeeNames.get(item.getOperatorId())));
        }

        return DefaultHttpActionPageResult.successWithData(result);
    }

    @Operation(summary = "更新通知内容审核状态", description = "更新通知内容审核状态")
    @PatchMapping("/{id}")
    public IHttpActionResult<Void> saveOrUpdateGoodsLink(
            @Schema(description = "审核记录id") @Validated @Positive @PathVariable Long id,
            @Schema(description = "当前被审核的内容更新时间戳") @Validated @Positive @RequestParam
            Long updateTime,
            @Schema(description = "审核状态") @RequestParam Integer auditStatus) {
        notifyAuditBizService.updateAuditStatus(id, updateTime, auditStatus, getEmployeeId());
        return DefaultHttpActionResult.successWithoutData();
    }
}
