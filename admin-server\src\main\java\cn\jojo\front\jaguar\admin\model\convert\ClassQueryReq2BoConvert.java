package cn.jojo.front.jaguar.admin.model.convert;

import cn.jojo.edu.common.utils.convert.BaseModelConvert;
import cn.jojo.front.jaguar.admin.model.req.activity.ClassQueryReq;
import cn.jojo.front.jaguar.biz.service.pojo.bo.activity.ClassQueryBo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 下拉选查询req 转bo
 *
 * <AUTHOR>
 * @date 2024/07/03
 */
@Mapper
public interface ClassQueryReq2BoConvert extends BaseModelConvert<ClassQueryReq, ClassQueryBo> {

    ClassQueryReq2BoConvert INSTANCE = Mappers.getMapper(ClassQueryReq2BoConvert.class);
}
