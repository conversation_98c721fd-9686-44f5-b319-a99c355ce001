package cn.jojo.front.jaguar.adaptor.service.impl;


import cn.jojo.front.jaguar.adaptor.service.IOAEmployRpcClient;
import cn.jojo.front.jaguar.common.pojo.vo.OAEmployeeVo;
import cn.jojo.pagani.common.exception.RpcBusinessException;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import oagateway.client.springbootstarter.model.EmployeeInfo;
import oagateway.client.springbootstarter.model.Result;
import oagateway.client.springbootstarter.server.DepartmentClient;
import oagateway.client.springbootstarter.server.EmployeeClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.List;
import java.util.Objects;

/**
 * OA RPC
 *
 * <AUTHOR>
 * @date 2020/04/27
 **/
@Component
@Slf4j
public class OAEmployRpcClient extends AbstractRpcClient implements IOAEmployRpcClient {

    @Autowired
    private EmployeeClient employeeClient;
    @Autowired
    private DepartmentClient departmentClient;

    @Override
    public List<OAEmployeeVo> searchEmployees(List<Long> employeeIds) {
        List<EmployeeInfo> employeeInfos;
        try {
            Result<List<EmployeeInfo>> result = employeeClient.searchEmployees(employeeIds);
            checkResult(result);
            employeeInfos = result.getData();
        } catch (IOException e) {
            log.error("oa employ rpc client search employees IOException!，e:{}", e);
            throw new RpcBusinessException("oa employ rpc client search employees IOException!");
        }
        List<OAEmployeeVo> employeeVos = super.covert(employeeInfos, OAEmployeeVo.class);
        return employeeVos;
    }

    @Override
    public OAEmployeeVo searchEmployee(Long employeeId) {
        EmployeeInfo employeeInfo;
        try {
            Result<EmployeeInfo> result = employeeClient.searchEmployee(employeeId);
            checkResult(result);
            employeeInfo = result.getData();

        } catch (IOException e) {
            log.error("oa employ rpc client search employees IOException!,e:{}", e);
            throw new RpcBusinessException("oa employ rpc client search employees IOException!");
        }
        return super.covert(employeeInfo, OAEmployeeVo.class);
    }

    @Override
    public IPage<OAEmployeeVo> searchEmployeesByPage(IPage<OAEmployeeVo> req, String userName) {
        List<EmployeeInfo> employeeInfos;
        long total = 0L;
        try {
            Result<oagateway.client.springbootstarter.model.Page<EmployeeInfo>> result = employeeClient
                .searchEmployeesByPage(req.getSize(), (int) req.getCurrent(), userName, null,
                    null, null, null);
            checkResult(result);
            employeeInfos = result.getData().getRecords();
            total = result.getData().getTotal();

        } catch (IOException e) {
            log.error("oa employ rpc client search employees IOException! e:{}", e);
            throw new RpcBusinessException("oa employ rpc client search employees IOException!");
        }
        List<OAEmployeeVo> oaEmployeeVos = super.covert(employeeInfos, OAEmployeeVo.class);
        return new Page<OAEmployeeVo>(req.getCurrent(), req.getSize(), total).setRecords(oaEmployeeVos);
    }

    @Override
    public Long currentEmployeeId() {
        return employeeClient.getEmployeeId();
    }

    @Override
    public List<OAEmployeeVo> searchSubordinates(Long employeeId) {
        List<EmployeeInfo> employeeInfos;
        try {
            Result<List<EmployeeInfo>> result = departmentClient.searchSubordinates(employeeId);
            checkResult(result);
            employeeInfos = result.getData();
        } catch (IOException e) {
            log.error("OA employ RPC client search subordinates failed for employeeId: {}, error: {}", employeeId, e);
            throw new RpcBusinessException("oa employ rpc client search employees IOException!");
        }
        List<OAEmployeeVo> oaEmployeeVos = super.covert(employeeInfos, OAEmployeeVo.class);
        return oaEmployeeVos;
    }

    private void checkResult(Result result) {
        if (Objects.isNull(result)) {
            log.error("oa employ rpc client search employees fail!,result:{}", result);
            throw new RpcBusinessException("oa employ rpc client search employees IOException!");
        }
        if (!result.isSuccess()) {
            log.error("oa employ rpc client search employees fail!,result:{}", result);
            throw new RpcBusinessException("oa employ rpc client search employees fail!");
        }

    }
}
