package cn.jojo.front.jaguar.adaptor.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.jojo.front.jaguar.adaptor.service.IUserRatingRpcClient;
import cn.jojo.infra.sdk.api.metadata.IRpcResult;
import cn.jojo.pagani.rpc.api.domain.dto.UserEvaluationResultDto;
import cn.jojo.pagani.rpc.api.domain.dto.UserEvaluationSimpleResultDto;
import cn.jojo.pagani.rpc.api.domain.dto.evalute.EvaluateSaveResultDto;
import cn.jojo.pagani.rpc.api.domain.dto.rating.RatingData;
import cn.jojo.pagani.rpc.api.domain.dto.rating.UserRatingData;
import cn.jojo.pagani.rpc.api.domain.req.evaluate.EvaluateQueryReq;
import cn.jojo.pagani.rpc.api.domain.req.evaluate.EvaluateSimpleQueryReq;
import cn.jojo.pagani.rpc.api.domain.req.evaluate.EvaluationRecordSaveReq;
import cn.jojo.pagani.rpc.api.domain.req.rating.QueryRatingReq;
import cn.jojo.pagani.rpc.api.service.IEvaluateService;
import cn.jojo.pagani.rpc.api.service.UserRatingRpcService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class UserRatingRpcClientImpl implements IUserRatingRpcClient {

    @DubboReference
    private UserRatingRpcService userRatingRpcService;

    @DubboReference
    private IEvaluateService evaluateService;

    @Override
    public boolean userRatingChild(Long userId) {
        if (Objects.isNull(userId)) {
            return false;
        }
        QueryRatingReq req = new QueryRatingReq().setUserIdList(Collections.singletonList(userId))
            .setEvaluateTypes(CollUtil.newArrayList(6, 7, 12, 13));
        try {
            IRpcResult<RatingData> rpcResult = userRatingRpcService.queryRatingData(req);
            if (Objects.nonNull(rpcResult) && rpcResult.checkSuccess()) {
                List<UserRatingData> userRatingData =
                    Optional.ofNullable(rpcResult.getData()).map(RatingData::getRatingDataList)
                        .orElse(Collections.emptyList());
                return CollUtil.isNotEmpty(userRatingData) &&
                    userRatingData.stream().findFirst().map(UserRatingData::getIdentityType).orElse("").equals("CHILD");
            }
        } catch (Exception e) {
            log.error("invoke pagani-userRatingRpcService.queryRatingData fail:{}",e.getMessage());
        }
        return false;
    }

    @Override
    public Long saveRatingRecord(EvaluationRecordSaveReq req) {
        if (Objects.isNull(req)) {
            return null;
        }
        try {
            IRpcResult<EvaluateSaveResultDto> rpcResult = evaluateService.saveEvaluationRecord(req);
            if (!rpcResult.checkSuccess()) {
                log.error("invoke evaluateService.saveEvaluationRecord failed, message={}", rpcResult.getMessage());
                return null;
            }
            return Optional.ofNullable(rpcResult.getData()).map(EvaluateSaveResultDto::getReportId).orElse(null);
        } catch (Exception e) {
            log.error("invoke pagani-saveEvaluationRecord error.", e);
        }
        return null;
    }

    @Override
    public UserEvaluationSimpleResultDto getRatingRecord(Long reportId) {
        if (Objects.isNull(reportId)) {
            return null;
        }
        try {
            EvaluateSimpleQueryReq req = new EvaluateSimpleQueryReq()
                .setId(reportId);
            IRpcResult<UserEvaluationSimpleResultDto> rpcResult = evaluateService.getUserEvaluationResult(req);
            if (!rpcResult.checkSuccess()) {
                log.error("invoke evaluateService.getUserEvaluationResult failed, message={}", rpcResult.getMessage());
                return null;
            }
            return rpcResult.getData();
        } catch (Exception e) {
            log.error("invoke pagani-getUserEvaluationResult error.", e);
        }
        return null;
    }
}
