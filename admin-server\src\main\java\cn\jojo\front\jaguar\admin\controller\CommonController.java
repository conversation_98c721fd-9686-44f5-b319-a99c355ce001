package cn.jojo.front.jaguar.admin.controller;

import cn.jojo.front.jaguar.adaptor.service.ObjectStorageAdaptorService;
import cn.jojo.front.jaguar.admin.model.req.GenUploadTokenReq;
import cn.jojo.front.jaguar.admin.model.req.UploadFileCheckReq;
import cn.jojo.front.jaguar.common.exception.BusinessException;
import cn.jojo.front.jaguar.common.utils.ApolloUtil;
import cn.jojo.front.jaguar.common.utils.JacksonUtil;
import cn.jojo.infra.sdk.api.metadata.DefaultHttpResult;
import cn.jojo.infra.sdk.api.metadata.IHttpResult;
import com.fasterxml.jackson.core.JsonProcessingException;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 */
@Tag(name = "公共服务")
@RestController
@RequestMapping("admin/common")
@Validated
public class CommonController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(CommonController.class);

    @Resource
    private ObjectStorageAdaptorService objectStorageAdaptorService;

    @Operation(summary ="获取文件上传签名token")
    @PostMapping("/genUploadToken")
    public String genUploadToken(
            @Parameter(description = "生成上传token参数，由前端上传组件提供统一参数!参考：http://confluence.console.tinman.cn/pages/viewpage.action?pageId=51975792") @RequestBody @Validated GenUploadTokenReq req) {
        String genTokenReq;
        try {
            genTokenReq = JacksonUtil.getInstance().writeValueAsString(req.buildGenUploadTokenDto());
        } catch (JsonProcessingException e) {
            throw BusinessException.exception(ApolloUtil.getStringMessage("error.param.error"));
        }
        String result = objectStorageAdaptorService.genUploadToken(genTokenReq);
        logger.info("[genUploadToken]req:{}, result:{}", genTokenReq, result);
        return result;
    }

    @Operation(summary ="获取批量文件上传签名token")
    @PostMapping("/genBatchUploadToken")
    public String genBatchUploadToken(
            @Parameter(description = "生成批量文件上传，由前端上传组件提供统一参数!参考：http://confluence.console.tinman.cn/pages/viewpage.action?pageId=51975792") @RequestBody @Validated GenUploadTokenReq req) {
        String genTokenReq;
        try {
            genTokenReq = JacksonUtil.getInstance().writeValueAsString(req.buildGenUploadTokenDto());
        } catch (JsonProcessingException e) {
            throw BusinessException.exception(ApolloUtil.getStringMessage("error.param.error"));
        }
        String result = objectStorageAdaptorService.genBatchUploadToken(genTokenReq);
        logger.info("[genBatchUploadToken]req:{}, result:{}", genTokenReq, result);
        return result;
    }

    @Operation(summary ="单个校验上传文件完整性")
    @PostMapping("/singleCheckConsistency")
    public IHttpResult<Boolean> singleCheckConsistency(
            @Parameter(description = "需校验的存储参数") @RequestParam(value = "objectStorageUrl") @NotBlank String objectStorageUrl) {
        Boolean result = objectStorageAdaptorService.checkConsistency(objectStorageUrl);
        logger.info("[singleCheckConsistency]objectStorageUrl:{}, result:{}", objectStorageUrl, result);
        return DefaultHttpResult.successWithData(result);
    }

    @Operation(summary ="批量校验上传文件完整性")
    @PostMapping("/checkConsistency")
    public String checkConsistency(
            @Parameter(description = "批量校验上传文件完整性参数，由前端上传组件提供统一参数!参考：http://confluence.console.tinman.cn/pages/viewpage.action?pageId=51975792") @RequestBody @Validated
            UploadFileCheckReq req) {
        String result = objectStorageAdaptorService.checkConsistencyOut(req.getObjectStorageUrlPrefix(),
                req.getObjectStorageUrlList());
        try {
            logger.info("[checkConsistency]req:{}, result:{}", JacksonUtil.getInstance().writeValueAsString(req),
                    result);
        } catch (JsonProcessingException e) {
            logger.error("序列化失败");
        }
        return result;
    }
}
