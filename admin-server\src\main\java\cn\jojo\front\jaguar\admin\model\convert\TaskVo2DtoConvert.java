package cn.jojo.front.jaguar.admin.model.convert;

import cn.jojo.edu.common.utils.convert.BaseModelConvert;
import cn.jojo.front.jaguar.admin.model.convert.decorator.TaskVo2DtoConvertDecorator;
import cn.jojo.front.jaguar.admin.model.dto.task.SubTaskDto;
import cn.jojo.front.jaguar.admin.model.dto.task.TaskDto;
import cn.jojo.front.jaguar.admin.model.dto.task.TaskVoiceDto;
import cn.jojo.front.jaguar.biz.service.pojo.bo.task.SubTaskVo;
import cn.jojo.front.jaguar.biz.service.pojo.bo.task.TaskVo;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson2.JSONValidator;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.DecoratedWith;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * vo转换
 *
 * <AUTHOR>
 * @date 2022/05/16
 */
@Mapper
@DecoratedWith(value = TaskVo2DtoConvertDecorator.class)
public interface TaskVo2DtoConvert extends BaseModelConvert<TaskVo, TaskDto> {

    TaskVo2DtoConvert INSTANCE = Mappers.getMapper(TaskVo2DtoConvert.class);
    @Mapping(source = "taskVoice", target = "taskVoices", qualifiedByName = "taskVoiceToObj")
    TaskDto model1ToModel2(TaskVo taskVo);

    @Mapping(source = "taskVoices", target = "taskVoice", qualifiedByName = "taskVoiceToString")
    TaskVo model2ToModel1(TaskDto taskDto);

    @Named("taskVoiceToObj")
    default List<TaskVoiceDto> taskVoiceToObj(String taskVoice){
        if (!JSONValidator.from(taskVoice).validate()) {
            return Collections.emptyList();
        }
        return JSON.parseObject(taskVoice,new TypeReference<List<TaskVoiceDto>>(){});
    }

    @Named("taskVoiceToString")
    default String taskVoiceToString(List<TaskVoiceDto> taskVoices){
        if(Objects.isNull(taskVoices)){
            return StringUtils.EMPTY;
        }
        return JSON.toJSONString(taskVoices);
    }

    @Mapping(source = "taskExtendResource", target = "taskExtendResource")
    SubTaskDto subTaskVoToDto(SubTaskVo subTaskVo);

    @Mapping(source = "taskExtendResource", target = "taskExtendResource")
    SubTaskVo subTaskDtoToVo(SubTaskDto subTaskDto);
}
