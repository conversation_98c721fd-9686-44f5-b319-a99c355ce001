package cn.jojo.front.jaguar.adaptor.service;


import cn.jojo.cc.common.dto.PropGroupDto;
import cn.jojo.front.jaguar.common.pojo.req.StickerAlbumReq;
import cn.jojo.front.jaguar.common.pojo.vo.activity.StickerPageVo;

import java.util.List;
import java.util.Map;

public interface IStickerAlbumRpcClient {

    /**
     * 根据key获取贴纸页
     * @param req
     * @return
     */
    StickerPageVo getStickerPagesByKey(StickerAlbumReq req);


    /**
     * 根据key获取贴纸页
     * @param req
     * @return
     */
    List<StickerPageVo> getStickerPagesByIds(StickerAlbumReq req);

    /**
     * 贴纸页ids 查询道具组
     * @param req
     * @return
     */
    Map<Long, List<PropGroupDto>> queryStickerPagePropByPageIds(StickerAlbumReq req);

}
