package cn.jojo.front.jaguar.admin.model.req.activity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ActivityRuleNaturalTimeReq {

    @Schema(description = "活动开始时间")
    private Long startTime;

    @Schema(description = "活动结束时间")
    private Long endTime;

    @Schema(description = "预告自然开始时间")
    private Long advanceStartTime;

    @Schema(description = "预告自然结束时间")
    private Long advanceEndTime;
}
