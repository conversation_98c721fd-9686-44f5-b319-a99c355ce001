package cn.jojo.front.jaguar.adaptor.service;


import cn.jojo.front.jaguar.common.pojo.req.CourseRpcReq;
import cn.jojo.front.jaguar.common.pojo.vo.SimpleCourseVo;

import java.util.List;

public interface ICourseRpcClient {

    /**
     * 如果返回null表示rpc接口调用出错，非null则表示id查询不到对应的课程
     * @param courseId
     * @return
     */
    SimpleCourseVo getByCourseId(Long courseId);

    List<SimpleCourseVo> listByCourseIds(List<Long> courseIds);

    List<SimpleCourseVo> listByCourseKeys(List<String> courseKeys);


    /**
     * 查询所有课程 PS:仅限于已发布课程,默认查询正式库，默认:0 查询正式库，1：查询教研库，2：查询所有库)
     *
     * @return
     */
    List<SimpleCourseVo> listAllCourse();

    /**
     * 通过搜索条件查询课程 教研和非教研全部数据
     * @param courseRpcReq
     * @return
     */
    List<SimpleCourseVo> searchCourse(CourseRpcReq courseRpcReq);
}
