package cn.jojo.front.jaguar.adaptor.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.jojo.front.jaguar.adaptor.service.ICommonCompoundRpcClient;
import cn.jojo.front.jaguar.common.exception.BusinessException;
import cn.jojo.front.jaguar.common.pojo.vo.BannerClipsMaterialContentVo;
import cn.jojo.infra.sdk.api.constants.ApiResultPlatformCodeConstants;
import cn.jojo.infra.sdk.api.metadata.IRpcResult;
import cn.jojo.pagani.rpc.api.domain.dto.compoundResource.BannerCompoundResultDTO;
import cn.jojo.pagani.rpc.api.domain.req.BaseCommonCompoundReq;
import cn.jojo.pagani.rpc.api.domain.req.compound.WorkResource;
import cn.jojo.pagani.rpc.api.service.CommonCompoundService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Description: 通用剪辑rpc
 * @Copyright: Copyright (c) 2022  ALL RIGHTS RESERVED.
 * @Company: 书声科技有限公司
 * @Author: chenHao
 * @CreateDate: 2023/1/3 15:41
 * @UpdateDate: 2023/1/3 15:41
 * @UpdateRemark: init
 * @Version: 1.0
 */
@Component
@Slf4j
public class CommonCompoundRpcClient implements ICommonCompoundRpcClient {

    @Reference(timeout = 5000)
    private CommonCompoundService commonCompoundService;

    @Override
    public Long createBannerClipsCompoundTask(Integer bizType, WorkResource req) {
        IRpcResult<Long> rpcResult =
            commonCompoundService.createBannerClipsCompoundTask(BaseCommonCompoundReq.builder()
                .bizType(bizType)
                .workResources(req).build());
        if (!rpcResult.checkSuccess()) {
            log.error("构建banner合成视频失败 msg:{}", rpcResult.getMessage());
        }
        return rpcResult.getData();
    }

    @Override
    public BannerClipsMaterialContentVo getClipsBannerMaterialInfo(Long taskId) {
        IRpcResult<BannerCompoundResultDTO> rpcResult = commonCompoundService.getClipsBannerMaterialInfo(taskId);
        if (rpcResult == null || !rpcResult.checkSuccess()) {
            log.error("获取banner合成视频失败 rpcResult:{}", rpcResult);

            return null;
        }
        BannerCompoundResultDTO data = rpcResult.getData();
        if (Objects.isNull(data)) {
            return null;
        }
        return new BannerClipsMaterialContentVo()
            .setBannerCover(data.getImageUrl())
            .setContent(data.getVideoUrl())
            .setClipsTaskStatus(data.getStatus());
    }

    @Override
    public List<BannerClipsMaterialContentVo> getClipsBannerMaterialInfos(List<Long> taskIds) {
        IRpcResult<List<BannerCompoundResultDTO>> rpcResult = null;
        try {
            rpcResult =
                    commonCompoundService.getClipsBannerMaterialInfos(taskIds);
        } catch (Exception e) {
            log.error("commonCompoundService.getClipsBannerMaterialInfos fail:{}",
                    e.getMessage());

        }
        if (Objects.isNull(rpcResult) || !rpcResult.checkSuccess() || Objects.isNull(rpcResult.getData())) {
            log.error("commonCompoundService.getClipsBannerMaterialInfos fail");
            return Collections.emptyList();
        }

        return rpcResult.getData().stream().map(result -> {
                BannerClipsMaterialContentVo vo = new BannerClipsMaterialContentVo();
                vo.setBannerCover(result.getImageUrl());
                vo.setContent(result.getVideoUrl());
                vo.setTaskId(result.getTaskId());
                vo.setClipsTaskStatus(result.getStatus());
                return vo;
              }).collect(Collectors.toList());
    }
}
