package cn.jojo.front.jaguar.adaptor.service.impl;

import cn.jojo.front.jaguar.common.pojo.bo.ExperienceCardBo;
import cn.jojo.infra.sdk.api.metadata.IRpcResult;
import cn.jojo.mall.boom.rpc.api.dto.ExperienceCardDto;
import cn.jojo.mall.boom.rpc.api.req.ExperienceCardReq;
import cn.jojo.mall.boom.rpc.api.service.ExperienceCardRpcService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 2025/3/18 10:30
 */
@Service
@Slf4j
public class ExperienceCardRpcClient {
    @DubboReference
    private ExperienceCardRpcService experienceCardRpcService;

    public List<ExperienceCardBo> queryExperienceCard(Long userId) {
        if (userId == null) {
            return Collections.emptyList();
        }
        ExperienceCardReq req = new ExperienceCardReq();
        req.setUserIds(Collections.singletonList(userId));
        try {
            IRpcResult<List<ExperienceCardDto>> rpcResult = experienceCardRpcService.queryExperienceCard(req);
            if (!rpcResult.checkSuccess()) {
                log.error("invoke experienceCardRpcService.queryExperienceCard failed, message={}",
                        rpcResult.getMessage());
                return Collections.emptyList();
            }
            return Optional.ofNullable(rpcResult.getData()).orElse(Collections.emptyList())
                .stream()
                .map(i -> {
                    ExperienceCardBo bo = new ExperienceCardBo();
                    BeanUtils.copyProperties(i, bo);
                    return bo;
                }).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("获取体验卡失败，{}",e.getMessage());
            return Collections.emptyList();
        }
    }
}
