package cn.jojo.front.jaguar.admin.controller;

import cn.jojo.common.api.ex.DefaultHttpActionPageResult;
import cn.jojo.common.api.ex.DefaultHttpActionResult;
import cn.jojo.common.api.ex.IHttpActionResult;
import cn.jojo.front.jaguar.common.pojo.entity.delamination.DelaminationGroup;
import cn.jojo.front.jaguar.common.pojo.req.BaseListReq;
import cn.jojo.front.jaguar.common.pojo.req.DelaminationGroupSaveReq;
import cn.jojo.front.jaguar.common.pojo.req.PriorityUpdateReq;
import cn.jojo.front.jaguar.common.pojo.vo.DelaminationGroupDetailVo;
import cn.jojo.front.jaguar.common.pojo.vo.DelaminationGroupListVo;
import cn.jojo.front.jaguar.core.service.delamination.DelaminationGroupService;
import cn.jojo.infra.sdk.api.metadata.IPageResp;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import javax.annotation.Resource;
import javax.validation.constraints.NotNull;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */

@RestController
@RequestMapping("/admin/delamination-group")
@Tag(name = "Jaguar管理后台")
@Validated
public class DelaminationGroupController {

    @Resource
    private DelaminationGroupService delaminationGroupService;

    @GetMapping("/list")
    @Operation(summary ="分群组配置列表",   description = "获取分群组配置列表")
    public IHttpActionResult<IPageResp<DelaminationGroupListVo>> list(BaseListReq<Void> req) {
        Page<DelaminationGroupListVo> page = delaminationGroupService.listDelaminationGroup(req);
        return DefaultHttpActionPageResult
            .successWithPageData(page.getCurrent(), page.getSize(), page.getTotal(), page.getRecords());
    }

    @GetMapping("/detail")
    @Operation(summary ="分群组详情",   description = "获取分群组详情")
    public IHttpActionResult<DelaminationGroupDetailVo> detail(@RequestParam @NotNull Long id) {
        DelaminationGroupDetailVo detail = delaminationGroupService.getDelaminationGroupDetailById(id);
        return DefaultHttpActionResult.successWithData(detail);
    }

    @PostMapping("/save")
    @Operation(summary ="保存或更新分群组",   description = "保存或更新分群组")
    public IHttpActionResult<Long> save(@RequestBody @Validated DelaminationGroupSaveReq req) {
        DelaminationGroup delaminationGroup = delaminationGroupService.saveOrUpdate(req);
        return DefaultHttpActionResult.successWithData(delaminationGroup.getId());
    }

    @PostMapping("/updatePriority")
    @Operation(summary ="更新优先级",   description = "更新优先级")
    public IHttpActionResult<Boolean> updatePriority(@RequestBody @Validated PriorityUpdateReq req) {
        delaminationGroupService.updatePriority(req);
        return DefaultHttpActionResult.successWithData(true);
    }

    @GetMapping("/delete")
    @Operation(summary ="删除分群组",   description = "删除分群组")
    public IHttpActionResult<Boolean> delete(@RequestParam @NotNull Long id) {
        delaminationGroupService.deleteDelaminationGroupById(id);
        return DefaultHttpActionResult.successWithData(true);
    }
}
