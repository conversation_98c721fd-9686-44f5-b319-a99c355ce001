package cn.jojo.front.jaguar.adaptor.service;


import cn.jojo.front.jaguar.common.pojo.req.EduUserClassScheduleItemQueryRpcReq;
import cn.jojo.front.jaguar.common.pojo.vo.EduClassScheduleItemVo;

import java.util.Date;
import java.util.List;

public interface IEduClassScheduleRpcClient {

    List<EduClassScheduleItemVo> queryUserClassScheduleItemsByUserId(Long userId,Long classId,List<Long> lessonIds);

    List<EduClassScheduleItemVo> queryUserClassScheduleItems(EduUserClassScheduleItemQueryRpcReq rpcReq);
}
