package cn.jojo.front.jaguar.admin.controller;

import cn.jojo.common.api.ex.DefaultHttpActionResult;
import cn.jojo.common.api.ex.IHttpActionResult;
import cn.jojo.front.jaguar.common.enums.ListRatingSkuScene;
import cn.jojo.front.jaguar.common.pojo.req.ListLinkSkusReq;
import cn.jojo.front.jaguar.common.pojo.req.RatingLinkRuleOrderUpdateReq;
import cn.jojo.front.jaguar.common.pojo.req.RatingLinkRuleSaveReq;
import cn.jojo.front.jaguar.common.pojo.vo.RatingLinkRuleDetailVo;
import cn.jojo.front.jaguar.common.pojo.vo.RatingLinkRuleListVo;
import cn.jojo.front.jaguar.core.service.impl.rating.RatingLinkRuleService;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/admin/ratings")
@Validated
public class RatingLinkRuleController extends BaseController {
    @Resource
    private RatingLinkRuleService ratingLinkRuleService;

    @PostMapping("/link-rules")
    @Operation(summary = "链路新增接口", description = "新增指定规则下的链路")
    public IHttpActionResult<Boolean> saveOrUpdate(@RequestBody RatingLinkRuleSaveReq req) {
        boolean result = ratingLinkRuleService.saveOrUpdate(req, getEmployeeId(), getEmployeeName());
        return DefaultHttpActionResult.successWithData(result);
    }

    @GetMapping("/link-rules")
    @Operation(summary = "链路详情接口", description = "获取指定id对应的链路详情")
    public IHttpActionResult<RatingLinkRuleDetailVo> detail(@NotNull Long id) {
        RatingLinkRuleDetailVo detail = ratingLinkRuleService.detail(id);
        return DefaultHttpActionResult.successWithData(detail);
    }

    @DeleteMapping("/link-rules")
    @Operation(summary = "链路删除接口", description = "删除指定id对应的链路")
    public IHttpActionResult<Boolean> delete(@NotNull Long id) {
        boolean result = ratingLinkRuleService.delete(id);
        return DefaultHttpActionResult.successWithData(result);
    }

    @GetMapping("/sku")
    @Operation(summary = "sku列表接口", description = "获取指定linkId下的所有sku信息")
    public IHttpActionResult<List<RatingLinkRuleDetailVo.SkuInfoVo>> sku(@NotNull Long linkId) {
        List<RatingLinkRuleDetailVo.SkuInfoVo> skuList =
            ratingLinkRuleService.getSkuList(linkId, ListLinkSkusReq.builder()
                    .scene(ListRatingSkuScene.RATING.getCode())
                .build());
        return DefaultHttpActionResult.successWithData(skuList);
    }

    @PutMapping("/link-rules")
    @Operation(summary = "更新指定链路", description = "更新指定链路")
    public IHttpActionResult<Boolean> updateOrder(@RequestBody RatingLinkRuleOrderUpdateReq req) {
        boolean result = ratingLinkRuleService.updateOrder(req);
        return DefaultHttpActionResult.successWithData(result);
    }

    @GetMapping("/rating-configs")
    @Operation(summary = "获取定级评估规则信息", description = "获取定级评估规则信息")
    public IHttpActionResult<RatingLinkRuleListVo> ratingLinkListRules(@NotNull @Positive Long configId) {
        RatingLinkRuleListVo result = ratingLinkRuleService.getRatingLinkList(configId);
        return DefaultHttpActionResult.successWithData(result);
    }
}
