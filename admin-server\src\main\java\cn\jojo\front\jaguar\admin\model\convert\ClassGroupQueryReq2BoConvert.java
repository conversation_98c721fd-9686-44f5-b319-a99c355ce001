package cn.jojo.front.jaguar.admin.model.convert;

import cn.jojo.edu.common.utils.convert.BaseModelConvert;
import cn.jojo.front.jaguar.admin.model.req.activity.ClassGroupQueryReq;
import cn.jojo.front.jaguar.biz.service.pojo.bo.activity.ClassGroupQueryBo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 下拉选查询req 转bo
 *
 * <AUTHOR>
 * @date 2024/07/03
 */
@Mapper
public interface ClassGroupQueryReq2BoConvert extends BaseModelConvert<ClassGroupQueryReq, ClassGroupQueryBo> {

    ClassGroupQueryReq2BoConvert INSTANCE = Mappers.getMapper(ClassGroupQueryReq2BoConvert.class);
}
