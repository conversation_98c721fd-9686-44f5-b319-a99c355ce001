package cn.jojo.front.jaguar.adaptor.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.jojo.cc.api.domain.request.PropQueryReq;
import cn.jojo.cc.api.service.PropService;
import cn.jojo.cc.common.dto.StickerPropDto;
import cn.jojo.front.jaguar.adaptor.model.req.CcPropReq;
import cn.jojo.front.jaguar.adaptor.service.ICcPropRpcClient;
import cn.jojo.front.jaguar.common.utils.CustomBeanUtils;
import cn.jojo.infra.sdk.api.metadata.IRpcResult;
import cn.jojo.pagani.common.exception.RpcBusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class CcPropRpcClient implements ICcPropRpcClient {

    @DubboReference
    private PropService propService;

    @Override
    public List<StickerPropDto> queryStickerPropByIds(CcPropReq req) {

        if (ObjectUtil.isEmpty(req.getPropIds())) {
            return Lists.newArrayList();
        }

        PropQueryReq propQueryReq = CustomBeanUtils.copyProperties(req, PropQueryReq::new);

        try {
            IRpcResult<List<StickerPropDto>> rpcResult = propService.queryStickerPropById(propQueryReq);
            if (!rpcResult.checkSuccess()) {
                log.error("invoke CcPropRpcClient.queryStickerPropByIds failed, message={}", rpcResult.getMessage());
                return Lists.newArrayList();
            }
            return rpcResult.getData();
        } catch (Exception e) {
            log.error("invoke CcPropRpcClient.queryStickerPropByIds failed", e);
            throw new RpcBusinessException("rpc error, Contact technical personnel ");
        }
    }

    @Override
    public List<StickerPropDto> queryStickerPropByKeys(CcPropReq req) {

        if (ObjectUtil.isEmpty(req.getPropKeys())) {
            return Lists.newArrayList();
        }

        PropQueryReq propQueryReq = CustomBeanUtils.copyProperties(req, PropQueryReq::new);

        try {
            IRpcResult<List<StickerPropDto>> rpcResult = propService.queryStickerPropByKey(propQueryReq);
            if (!rpcResult.checkSuccess()) {
                log.error("invoke CcPropRpcClient.queryStickerPropByKeys failed, message={}", rpcResult.getMessage());
                return Lists.newArrayList();
            }
            return rpcResult.getData();
        } catch (Exception e) {
            log.error("invoke CcPropRpcClient.queryStickerPropByKeys failed", e);
            throw new RpcBusinessException("rpc error, Contact technical personnel ");
        }
    }
}
