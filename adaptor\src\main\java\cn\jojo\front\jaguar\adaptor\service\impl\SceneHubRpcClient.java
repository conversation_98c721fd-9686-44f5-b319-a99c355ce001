package cn.jojo.front.jaguar.adaptor.service.impl;


import cn.jojo.edu.fantasy.rpc.api.dto.EduSceneHubAggregationDto;
import cn.jojo.edu.fantasy.rpc.api.dto.EduSceneHubDto;
import cn.jojo.edu.fantasy.rpc.api.dto.SceneHubExtraDto;
import cn.jojo.edu.fantasy.rpc.api.req.EduSceneHubReq;
import cn.jojo.edu.fantasy.rpc.api.req.SceneHubAggregationReq;
import cn.jojo.edu.fantasy.rpc.api.req.SceneHubExtraReq;
import cn.jojo.edu.fantasy.rpc.api.service.IEduSceneHubRpcService;
import cn.jojo.front.jaguar.adaptor.service.ISceneHubRpcClient;
import cn.jojo.front.jaguar.common.pojo.bo.EduSceneHubAggregationBo;
import cn.jojo.front.jaguar.common.pojo.bo.EduSceneHubBo;
import cn.jojo.front.jaguar.common.pojo.bo.ExtraFieldBo;
import cn.jojo.front.jaguar.common.pojo.bo.SceneHubExtraBo;
import cn.jojo.front.jaguar.common.pojo.req.EduSceneHubRpcReq;
import cn.jojo.front.jaguar.common.pojo.req.SceneHubExtraFuzzyReq;
import cn.jojo.front.jaguar.common.pojo.req.SceneHubExtraQueryRpcReq;
import cn.jojo.front.jaguar.common.pojo.req.SceneHubExtraRpcReq;
import cn.jojo.infra.sdk.api.metadata.IPageResp;
import cn.jojo.infra.sdk.api.metadata.IRpcResult;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class SceneHubRpcClient implements ISceneHubRpcClient {
    @Reference
    private IEduSceneHubRpcService eduSceneHubRpcService;

    @Value("${scene.hub.batch.size:100}")
    private Integer batchSize;


    @Override
    public List<EduSceneHubAggregationBo> querySceneHubAggregation(List<Long> hubIds, Long sceneId) {
        if (CollectionUtils.isEmpty(hubIds)) {
            return Collections.emptyList();
        }
        return Lists.partition(hubIds, batchSize)
                .stream()
                .map(item -> querySceneHub(item, sceneId))
                .flatMap(Collection::stream)
                .map(item -> {
                    List<ExtraFieldBo> extraFields = Optional.ofNullable(item.getExtraFields())
                            .orElse(Collections.emptyList())
                            .stream()
                            .map(field -> {
                                ExtraFieldBo extraField = new ExtraFieldBo();
                                BeanUtils.copyProperties(field, extraField);
                                return extraField;
                            }).collect(Collectors.toList());
                    EduSceneHubAggregationBo result = new EduSceneHubAggregationBo();
                    BeanUtils.copyProperties(item, result);
                    return result.setExtraFields(extraFields);
                }).collect(Collectors.toList());
    }

    private List<EduSceneHubAggregationDto> querySceneHub(List<Long> hubIds, Long sceneId) {
        SceneHubAggregationReq req = SceneHubAggregationReq.builder().hubIds(hubIds)
                .sceneId(sceneId).build();
        IRpcResult<List<EduSceneHubAggregationDto>> rpcResult = eduSceneHubRpcService.querySceneHubAggregation(req);
        if (!rpcResult.checkSuccess()) {
            log.error("invoke querySceneHubAggregation failed, message={}", rpcResult.getMessage());
            return Lists.newArrayList();
        }
        return Optional.ofNullable(rpcResult.getData()).orElse(Collections.emptyList());
    }

    @Override
    public List<EduSceneHubBo> querySceneHubs(EduSceneHubRpcReq rpcReq) {

        EduSceneHubReq req = new EduSceneHubReq();
        BeanUtils.copyProperties(rpcReq, req);
        int pageNumber = 1;
        List<EduSceneHubDto> rpcList = Lists.newArrayList();
        try {
            while (true) {
                req.setPageNum(pageNumber++);
                IRpcResult<IPageResp<EduSceneHubDto>> rpcResult = eduSceneHubRpcService.querySceneHubPage(req);
                if (!rpcResult.checkSuccess()) {
                    log.error("invoke querySceneHub failed, message={}", rpcResult.getMessage());
                    return Collections.emptyList();
                }

                IPageResp<EduSceneHubDto> pageResp = rpcResult.getData();
                if (CollectionUtils.isEmpty(pageResp.getPageRecords())) {
                    break;
                }

                rpcList.addAll(pageResp.getPageRecords());
                if (rpcList.size() >= pageResp.getTotalCount()) {
                    break;
                }
            }
            return rpcList.stream().map(item -> {
                EduSceneHubBo bo = new EduSceneHubBo();
                BeanUtils.copyProperties(item, bo);
                return bo;
            }).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("invoke querySceneHub failed ", e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<EduSceneHubAggregationBo> listSceneHubAggregations(SceneHubExtraRpcReq rpcReq) {
        List<EduSceneHubAggregationBo> results = querySceneHubAggregation(rpcReq.getHubIds(), rpcReq.getSceneId());
        return Optional.ofNullable(results)
            .orElse(Collections.emptyList())
            .stream()
            .filter(item -> filerFieldFuzzyGroups(item, rpcReq.getFieldFuzzyGroups()))
            .collect(Collectors.toList());
    }

    private boolean filerFieldFuzzyGroups(EduSceneHubAggregationBo sceneHubExtra,
                                          List<SceneHubExtraFuzzyReq> fieldFuzzyGroups) {
        if (CollectionUtils.isEmpty(fieldFuzzyGroups)) {
            return true;
        }

        return fieldFuzzyGroups.stream()
            .allMatch(fieldFuzzyGroup -> sceneHubExtra.getExtraFields().stream()
                .filter(extraField -> fieldFuzzyGroup.getFieldName().equals(extraField.getFieldName()))
                .map(ExtraFieldBo::getFieldValue)
                .anyMatch(filedValue -> {
                    if (fieldFuzzyGroup.isFuzzyQueryValue()) {
                        return filedValue.contains(fieldFuzzyGroup.getFieldValue());
                    } else {
                        return filedValue.equals(fieldFuzzyGroup.getFieldValue());
                    }
                }));
    }

    @Override
    public List<SceneHubExtraBo> listSceneHubExtras(SceneHubExtraQueryRpcReq rpcReq) {
        SceneHubExtraReq req = new SceneHubExtraReq();
        BeanUtils.copyProperties(rpcReq, req);
        IRpcResult<List<SceneHubExtraDto>> rpcResult = eduSceneHubRpcService.querySceneHubExtra(req);
        if (!rpcResult.checkSuccess()) {
            log.error("failed invoke querySceneHubExtra, message = {}", rpcResult.getMessage());
            return Collections.emptyList();
        }
        return Optional.ofNullable(rpcResult.getData())
            .orElse(Collections.emptyList())
            .stream()
            .map(item -> {
                List<ExtraFieldBo> extraFields = Optional.ofNullable(item.getExtraFields())
                    .orElse(Collections.emptyList())
                    .stream()
                    .map(i -> new ExtraFieldBo()
                        .setFieldName(i.getFieldName())
                        .setFieldValue(i.getFieldValue()))
                    .collect(Collectors.toList());
                SceneHubExtraBo bo = new SceneHubExtraBo();
                BeanUtils.copyProperties(item, bo);
                return bo.setExtraFields(extraFields);
            }).collect(Collectors.toList());
    }
}
