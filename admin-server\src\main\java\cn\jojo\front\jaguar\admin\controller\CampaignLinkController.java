package cn.jojo.front.jaguar.admin.controller;

import cn.jojo.common.api.ex.DefaultHttpActionResult;
import cn.jojo.common.api.ex.IHttpActionResult;
import cn.jojo.front.jaguar.common.pojo.req.ListLinkSkusReq;
import cn.jojo.front.jaguar.common.pojo.vo.CampaignSkuInfoVo;
import cn.jojo.front.jaguar.core.service.impl.rating.RatingLinkRuleService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/10/21 14:05
 */
@RestController
@RequestMapping("/admin/campaign-links")
@Validated
public class CampaignLinkController {

    @Resource
    private RatingLinkRuleService ratingLinkRuleService;

    @GetMapping("/{linkId}/skus")
    @Operation(summary = "获取营销活动sku列表接口", description = "根据链路id获取指定营销活动链路下的所有sku信息")
    public IHttpActionResult<List<CampaignSkuInfoVo>> skus(
        @NotNull @PathVariable @Parameter(description = "链路id", required = true) Long linkId,
        @Validated ListLinkSkusReq req) {
        List<CampaignSkuInfoVo> skuList = ratingLinkRuleService.getSkuList(linkId, req)
            .stream()
            .map(sku -> new CampaignSkuInfoVo().setSkuId(sku.getSkuId()).setSkuName(sku.getSkuName()))
            .collect(Collectors.toList());
        return DefaultHttpActionResult.successWithData(skuList);
    }
}
