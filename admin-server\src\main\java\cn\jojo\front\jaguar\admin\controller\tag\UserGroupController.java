package cn.jojo.front.jaguar.admin.controller.tag;

import cn.jojo.common.api.ex.DefaultHttpActionPageResult;
import cn.jojo.common.api.ex.DefaultHttpActionResult;
import cn.jojo.common.api.ex.IHttpActionResult;
import cn.jojo.front.jaguar.adaptor.service.FbTagUserGroupRpcService;
import cn.jojo.front.jaguar.biz.service.CrmUserGroupBizService;
import cn.jojo.front.jaguar.common.enums.UserGroupType;
import cn.jojo.front.jaguar.common.pojo.bo.PageResultBo;
import cn.jojo.front.jaguar.common.pojo.req.FbUserGroupListQueryReq;
import cn.jojo.front.jaguar.common.pojo.req.FbUserGroupSaveReq;
import cn.jojo.front.jaguar.common.pojo.vo.FbTagUserGroupListVo;
import cn.jojo.front.jaguar.common.pojo.vo.UserGroupInfoVo;
import cn.jojo.infra.sdk.api.metadata.IPageResp;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import oagateway.client.springbootstarter.server.EmployeeClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Collections;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("admin/user/group")
@Tag(name = "fb-tag-admin管理后台")
@Validated
public class UserGroupController {

    @Resource
    private EmployeeClient employeeClient;

    @Resource
    private FbTagUserGroupRpcService fbTagUserGroupRpcService;
    @Resource
    private CrmUserGroupBizService crmUserGroupBizService;

    @GetMapping("/list")
    @Operation(summary = "获取分群分页列表")
    public IHttpActionResult<IPageResp<FbTagUserGroupListVo>> list(@Validated FbUserGroupListQueryReq req) {
        req.setDefault();
        Long employeeId = employeeClient.getEmployeeId();
        PageResultBo<FbTagUserGroupListVo> pageResult = fbTagUserGroupRpcService.listUserGroupPages(req, employeeId);
        return DefaultHttpActionPageResult.successWithPageData(pageResult.getPageNum(),
                pageResult.getPageSize(), pageResult.getTotal(), pageResult.getRecords());
    }

    @GetMapping("/status/update")
    @Operation(summary ="更新分群状态 targetStatus=VALID INVALID DELETED")
    public IHttpActionResult<Boolean> updateStatus(@RequestParam @NotNull Long id, @RequestParam @NotBlank String targetStatus) {
        boolean result = fbTagUserGroupRpcService.updateUserGroupStatus(id, targetStatus);
        return DefaultHttpActionResult.successWithData(result);
    }

    @PostMapping("/save")
    @Operation(summary ="保存分群")
    public IHttpActionResult<Boolean> save(@RequestBody @Validated FbUserGroupSaveReq req) {
        req.setCreatorId(employeeClient.getEmployeeId());
        boolean result = fbTagUserGroupRpcService.addUserGroup(req);
        return DefaultHttpActionResult.successWithData(result);
    }

    @GetMapping("/search")
    @Operation(summary ="搜索分群")
    public IHttpActionResult<IPageResp<UserGroupInfoVo>> search(Long groupId, String fuzzyName,
            @NotNull String groupType,
            @NotNull Long pageNum,
            @NotNull Long pageSize) {
        if(UserGroupType.of(groupType) == null) {
            return DefaultHttpActionPageResult.successWithPageData(1L, 10L, 0L, Collections.emptyList());
        }
        PageResultBo<UserGroupInfoVo> pageResult = crmUserGroupBizService
            .queryCrmUserGroupList(groupId, fuzzyName, groupType, pageNum, pageSize);
        return DefaultHttpActionPageResult.successWithPageData(pageResult.getPageNum(), pageResult.getPageSize(),
                pageResult.getTotal(), pageResult.getRecords());
    }
}
