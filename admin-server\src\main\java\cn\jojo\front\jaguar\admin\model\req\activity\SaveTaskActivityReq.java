package cn.jojo.front.jaguar.admin.model.req.activity;

import cn.jojo.front.jaguar.common.enums.task.ActivityTypeEnum;
import cn.jojo.front.jaguar.common.enums.task.CourseTypeEnum;
import cn.jojo.front.jaguar.common.enums.task.InnerSubjectType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SaveTaskActivityReq {

    /**
     * 活动名称
     */
    @Schema(description = "活动名称", maxLength = 30)
    @NotBlank(message = "name can not be blank")
    @Length(max = 30, message = "name max length is 30")
    private String name;

    /**
     * 活动类型(1.今日活动、2.首月激励、3.全年激励)在apollo可配置。
     */
    @Schema(description = "活动类型，1.2为老类型，列表会屏蔽掉 ", implementation = ActivityTypeEnum.class)
    @NotNull(message = "type can not be null")
    private Integer type;

    /**
     * 类型
     */
    @Schema(description = "计划类型", implementation = CourseTypeEnum.class)
    private Integer courseType;

    /**
     * 活动描述
     */
    @Schema(description = "活动描述")
    private String description;

    @Schema(description = "品类", implementation = InnerSubjectType.class)
    @NotNull(message = "subject type can not be null")
    private Integer subjectType;

    @Schema(description = "课程id")
    @NotNull(message = "courseId can not be null")
    private Long courseId;

    @Schema(description = "活动规则汇总")
    @NotNull(message = "activityRule can not be null")
    private ActivityRuleReq activityRule;
    /**
     * 开始时间
     */
    @Schema(description = "开始时间")
    private Long startTime;

    /**
     * 结束时间
     */
    @Schema(description = "结束时间 ")
    private Long endTime;

    @Schema(description = "任务ids, 采用覆盖模式（即最新的集合会覆盖数据库数据)")
    private List<Long> taskIds;

    @Schema(description = "心愿礼物ids，即商城skuId")
    private List<Long> wishGiftIds;

    @Schema(description = "贴纸页ids，贴纸页id集合")
    private List<Long> stickerPageIds;

    @Schema(description = "活动卡ids，活动卡id集合")
    private List<Long> cardIds;

    @Schema(description = "活动资源包")
    private ActivityResourcePackageReq activityResourcePackage;

    @Schema(description = "活动引导资源包")
    private ActivityGuideResourcePackageReq activityGuideResourcePackage;

    @Schema(description = "活动主题id")
    private Long themeId;

}
