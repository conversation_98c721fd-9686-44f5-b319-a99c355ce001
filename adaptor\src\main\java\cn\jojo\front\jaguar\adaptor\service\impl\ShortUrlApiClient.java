package cn.jojo.front.jaguar.adaptor.service.impl;

import cn.jojo.front.jaguar.adaptor.service.IShortUrlApiClient;
import cn.jojo.infra.sdk.api.metadata.IRpcResult;
import cn.tinman.sharedservices.mall.mars.api.request.ShortUrlReq;
import cn.tinman.sharedservices.mall.mars.api.response.ShortUrlResp;
import cn.tinman.sharedservices.mall.mars.api.service.IShortUrlApiService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class ShortUrlApiClient implements IShortUrlApiClient {
    @DubboReference
    private IShortUrlApiService shortUrlApiService;

    @Override
    public ShortUrlResp generateShortUrl(String url, Long shortUrlBizId, String groupName, Date expireTime, String tittle) {
        if (StringUtils.isBlank(url) || shortUrlBizId == null || StringUtils.isBlank(groupName)) {
            log.info("生成短链失败，参数错误");
            return null;
        }
        ShortUrlReq req = new ShortUrlReq()
                .setShortUrlBizId(shortUrlBizId)
                .setGroupName(groupName)
                .setUrl(url)
                .setExpiresTime(expireTime)
                .setTitle(tittle);
        try {
            IRpcResult<ShortUrlResp> rpcResult = shortUrlApiService.generateShortUrl(req);
            if (!rpcResult.checkSuccess()) {
                log.error("invoke shortUrlApiService.generateShortUrl failed, message:{}", rpcResult.getMessage());
                return null;
            }
            return rpcResult.getData();
        } catch (Exception e) {
            log.error("invoke shortUrlApiService.generateShortUrl failed", e);
            return null;
        }
    }
}
