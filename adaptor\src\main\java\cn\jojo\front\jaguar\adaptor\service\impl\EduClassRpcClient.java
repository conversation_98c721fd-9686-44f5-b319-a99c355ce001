package cn.jojo.front.jaguar.adaptor.service.impl;


import cn.hutool.core.util.ObjectUtil;
import cn.jojo.edu.malacca.rpc.api.dto.EduClassDto;
import cn.jojo.edu.malacca.rpc.api.dto.EduUserCourseExtensionDto;
import cn.jojo.edu.malacca.rpc.api.req.EduClassQueryReq;
import cn.jojo.edu.malacca.rpc.api.req.EduUserCourseExtensionReq;
import cn.jojo.edu.malacca.rpc.api.req.EduUserCourseQueryReq;
import cn.jojo.edu.malacca.rpc.api.service.EduClassRpcService;
import cn.jojo.edu.malacca.rpc.api.service.EduUserCourseRpcService;
import cn.jojo.front.jaguar.adaptor.model.req.EduClassReq;
import cn.jojo.front.jaguar.adaptor.service.IEduClassRpcClient;
import cn.jojo.front.jaguar.common.pojo.bo.CourseBo;
import cn.jojo.front.jaguar.common.pojo.bo.UserCourse;
import cn.jojo.front.jaguar.common.pojo.bo.UserCourseExtensionQueryBo;
import cn.jojo.front.jaguar.common.pojo.bo.UserCourseQueryBo;
import cn.jojo.front.jaguar.common.pojo.vo.UserCourseExtensionVo;
import cn.jojo.front.jaguar.common.pojo.vo.eduUserClass.EduClassVo;
import cn.jojo.front.jaguar.common.utils.CustomBeanUtils;
import cn.jojo.infra.sdk.api.metadata.IPageResp;
import cn.jojo.infra.sdk.api.metadata.IRpcResult;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.api.client.util.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Component
@Slf4j
public class EduClassRpcClient implements IEduClassRpcClient {

    @Reference
    private EduClassRpcService eduClassRpcService;
    @Reference
    private EduUserCourseRpcService userCourseRpcService;


    @Override
    public List<EduClassVo> queryClassesByClassIds(EduClassReq eduClassReq) {
        if (ObjectUtil.isAllEmpty(eduClassReq.getClassIds(), eduClassReq.getClassKeys(),
            eduClassReq.getCourseIds(), eduClassReq.getGroupIds())) {
            return Lists.newArrayList();
        }

        EduClassQueryReq req = CustomBeanUtils.copyProperties(eduClassReq, EduClassQueryReq::new);
        try {
            IRpcResult<List<EduClassDto>> rpcResult = eduClassRpcService.queryClasses(req);
            if (!rpcResult.checkSuccess()) {
                log.error("EduClassRpcClient.queryClassesByClassId failed, req = {}", req);

                return Collections.emptyList();
            }
            List<EduClassVo> list = rpcResult.getData().stream()
                .map(data -> CustomBeanUtils.copyProperties(data, EduClassVo::new))
                .collect(Collectors.toList());

            list = filterOtherQueryParameter(eduClassReq, list);
            return list;

        } catch (Exception e) {
            log.error("EduClassRpcClient.queryClassesByClassId failed", e);
            return Collections.emptyList();
        }
    }


    /***
     * 过滤其他查询条件
     * 这里 因为 classId 和classKey malacca走缓存但是没过滤，其他条件需要过滤
     * @param eduClassReq 入参
     * @param list 返回全部集合
     * @return 过滤后的集合
     */
    private List<EduClassVo> filterOtherQueryParameter(EduClassReq eduClassReq, List<EduClassVo> list) {

        if (ObjectUtil.isNotEmpty(eduClassReq.getCourseIds())) {
            list = list.stream().filter(data -> eduClassReq.getCourseIds().contains(data.getCourseId()))
                .collect(Collectors.toList());
        }

        if (ObjectUtil.isNotEmpty(eduClassReq.getGroupIds())) {
            list = list.stream().filter(data -> eduClassReq.getGroupIds().contains(data.getGroupId()))
                .collect(Collectors.toList());
        }
        return list;
    }


    @Override
    public IPage<UserCourseExtensionVo> queryExtension(UserCourseExtensionQueryBo<UserCourseQueryBo> queryVo) {
        EduUserCourseQueryReq userCourseQueryReq = new EduUserCourseQueryReq();
        BeanUtils.copyProperties(queryVo.getReq(), userCourseQueryReq);
        EduUserCourseExtensionReq<EduUserCourseQueryReq> extensionReq =
            EduUserCourseExtensionReq.<EduUserCourseQueryReq>builder()
                .req(userCourseQueryReq)
                .userCourseExtensionTypes(queryVo.getUserCourseExtensionTypes())
                .build();
        try {
            IRpcResult<IPageResp<EduUserCourseExtensionDto>> rpcResult =
                userCourseRpcService.queryExtension(extensionReq);
            if (!rpcResult.checkSuccess()) {
                log.error("调用 userCourseRpcService.queryExtension 出现错误，param=[{}]，message=[{}]",
                    JSON.toJSONString(extensionReq), rpcResult.getSubMessage());
                return new Page<>();
            }
            IPageResp<EduUserCourseExtensionDto> pageData = rpcResult.getData();
            List<UserCourseExtensionVo> extensionVoList = pageData.getPageRecords().stream()
                .filter(dto -> dto != null && dto.getUserCourse() != null)
                .map(dto -> {
                    UserCourse userCourse = new UserCourse();
                    BeanUtils.copyProperties(dto.getUserCourse(), userCourse);
                    CourseBo course = Optional.ofNullable(dto.getCourse()).map(data -> {
                        CourseBo result = new CourseBo();
                        BeanUtils.copyProperties(data, result);
                        result.setCourseSegmentCode(
                            Optional.ofNullable(data.getCourseSegmentCode()).map(String::valueOf).orElse(null));
                        return result;
                    }).orElse(null);
                    return UserCourseExtensionVo.builder().userCourse(userCourse).course(course).build();
                })
                .collect(Collectors.toList());
            return new Page<UserCourseExtensionVo>(pageData.getPageNum(), pageData.getPageSize(),
                pageData.getTotalCount())
                .setRecords(extensionVoList);
        } catch (Exception e) {
            log.error("调用 userCourseRpcService.queryExtension 出现错误", e);
            return new Page<>();
        }
    }
}
