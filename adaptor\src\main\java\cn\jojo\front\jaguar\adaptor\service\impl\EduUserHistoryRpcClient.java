package cn.jojo.front.jaguar.adaptor.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.jojo.edu.fantasy.rpc.api.dto.EduUserBrowseRecordDto;
import cn.jojo.edu.fantasy.rpc.api.dto.EduUserHistoryDto;
import cn.jojo.edu.fantasy.rpc.api.req.EduUserBrowseRecordQueryReq;
import cn.jojo.edu.fantasy.rpc.api.req.EduUserHistoryReq;
import cn.jojo.edu.fantasy.rpc.api.service.IEduUserHistoryRpcService;
import cn.jojo.front.jaguar.adaptor.service.IEduUserHistoryRpcClient;
import cn.jojo.front.jaguar.common.pojo.bo.EduUserHistoryBo;
import cn.jojo.front.jaguar.common.pojo.req.UserBrowserRecordReq;
import cn.jojo.front.jaguar.common.pojo.vo.UserBrowserRecordVo;
import cn.jojo.infra.sdk.api.metadata.IPageResp;
import cn.jojo.infra.sdk.api.metadata.IRpcResult;
import cn.jojo.infra.sdk.logging.JoJoLogging;
import com.alibaba.fastjson2.JSON;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: luohuanrong
 * @create: 2023/6/6
 **/
@Slf4j
@Component
public class EduUserHistoryRpcClient implements IEduUserHistoryRpcClient {

    @DubboReference
    private IEduUserHistoryRpcService eduUserStudyRecordRpcService;
    @Value("${edu.user.browser.batch.size:50}")
    private Integer browserBatchSize;

    @Override
    public List<EduUserHistoryBo> queryUserHistoryPage(EduUserHistoryReq req) {
        try {
            IRpcResult<IPageResp<EduUserHistoryDto>> rpcResult = eduUserStudyRecordRpcService.queryUserHistoryPage(req);
            JoJoLogging.logger(log)
                    .unIndex("req", JSON.toJSONString(req))
                    .unIndex("rpcResult", JSON.toJSONString(rpcResult))
                    .info("queryUserHistory userId:{}", req.getUserId());
            if (!rpcResult.checkSuccess() || Objects.isNull(rpcResult.getData())) {
                return Collections.emptyList();
            }
            IPageResp<EduUserHistoryDto> pageResp = rpcResult.getData();
            if (CollUtil.isEmpty(pageResp.getPageRecords())) {
                return Collections.emptyList();
            }
            return convertEduUserLabelBo(pageResp.getPageRecords());
        } catch (Exception e) {
            log.error("queryUserHistory userId:{} error",  req.getUserId(), e);
            return Collections.emptyList();
        }
    }

    private List<EduUserHistoryBo> convertEduUserLabelBo(List<EduUserHistoryDto> list) {
        return list.stream().filter(Objects::nonNull)
                .map(t -> {
                    EduUserHistoryBo eduUserHistoryBo = new EduUserHistoryBo();
                    BeanUtils.copyProperties(t, eduUserHistoryBo);
                    return eduUserHistoryBo;
                })
                .collect(Collectors.toList());
    }

    @Override
    public List<UserBrowserRecordVo> getUserBrowserRecords(UserBrowserRecordReq req) {
        if (Objects.isNull(req.getUserId()) || CollectionUtils.isEmpty(req.getHubIds())) {
            return Collections.emptyList();
        }

        return Lists.partition(req.getHubIds(), browserBatchSize).stream()
            .map(hubIds -> {
                EduUserBrowseRecordQueryReq queryReq = EduUserBrowseRecordQueryReq.builder().build();
                BeanUtils.copyProperties(req, queryReq);
                queryReq.setHubIds(hubIds);
                IRpcResult<List<EduUserBrowseRecordDto>> rpcResult;
                try {
                    rpcResult = eduUserStudyRecordRpcService.queryUserAlbumBrowseRecord(queryReq);
                    if (!rpcResult.checkSuccess()) {
                        JoJoLogging.logger(log)
                            .error("eduUserHistoryRpcClient.getUserBrowserRecords failed,req:{},message:{}",
                                req,
                                rpcResult.getMessage());
                        return Collections.<EduUserBrowseRecordDto>emptyList();
                    }
                    return rpcResult.getData();
                } catch (Exception e) {
                    JoJoLogging.logger(log).error("eduUserHistoryRpcClient.getUserBrowserRecords failed", e);
                    return Collections.<EduUserBrowseRecordDto>emptyList();
                }
            })
            .flatMap(Collection::stream)
            .map(this::convert).collect(Collectors.toList());
    }

    private UserBrowserRecordVo convert(EduUserBrowseRecordDto eduUserBrowseRecordDto) {
        UserBrowserRecordVo recordVo = new UserBrowserRecordVo();
        BeanUtils.copyProperties(eduUserBrowseRecordDto, recordVo);
        return recordVo;
    }

}
