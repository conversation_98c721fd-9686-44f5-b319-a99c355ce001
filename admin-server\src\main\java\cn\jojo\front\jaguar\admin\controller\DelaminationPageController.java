package cn.jojo.front.jaguar.admin.controller;

import cn.jojo.common.api.ex.DefaultHttpActionPageResult;
import cn.jojo.common.api.ex.DefaultHttpActionResult;
import cn.jojo.common.api.ex.IHttpActionResult;
import cn.jojo.front.jaguar.common.pojo.bo.DelaminationPageVo;
import cn.jojo.front.jaguar.common.pojo.req.BaseListReq;
import cn.jojo.front.jaguar.common.pojo.req.DelaminationPageSaveReq;
import cn.jojo.front.jaguar.common.pojo.vo.DelaminationGroupNameVo;
import cn.jojo.front.jaguar.core.service.delamination.DelaminationPageService;
import cn.jojo.infra.sdk.api.metadata.IPageResp;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.constraints.NotNull;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/admin/delamination-page")
@Tag(name = "Jaguar管理后台")
@Validated
public class DelaminationPageController {

    @Resource
    private DelaminationPageService delaminationPageService;

    @GetMapping("/list")
    @Operation(summary ="页面配置列表",   description = "获取页面配置列表")
    public IHttpActionResult<IPageResp<DelaminationPageVo>> pageList(BaseListReq<Void> req) {
        Page<DelaminationPageVo> pageData = delaminationPageService.listDelaminationPage(req);
        return DefaultHttpActionPageResult
            .successWithPageData(pageData.getCurrent(), pageData.getSize(), pageData.getTotal(), pageData.getRecords());
    }

    @PostMapping("/saveOrUpdate")
    @Operation(summary ="新增或更新页面信息",   description = "新增或更新页面信息")
    public IHttpActionResult<Long> saveOrUpdate(@RequestBody @Validated DelaminationPageSaveReq req) {
        Long id = delaminationPageService.saveOrUpdate(req);
        return DefaultHttpActionResult.successWithData(id);
    }

    @GetMapping("/relation")
    @Operation(summary ="页面引用关系",   description = "获取页面引用关系")
    public IHttpActionResult<List<DelaminationGroupNameVo>> relation(@RequestParam @NotNull Long id) {
        List<DelaminationGroupNameVo> resultList = delaminationPageService.groupRelation(id);
        return DefaultHttpActionResult.successWithData(resultList);
    }
}
