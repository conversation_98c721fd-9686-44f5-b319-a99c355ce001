package cn.jojo.front.jaguar.admin.controller.activity;

import cn.jojo.front.jaguar.biz.service.activitity.IStickerBizService;
import cn.jojo.front.jaguar.biz.service.activitity.IStickerPagesBizService;
import cn.jojo.front.jaguar.common.pojo.vo.activity.StickerPageVo;
import cn.jojo.front.jaguar.common.pojo.vo.activity.StickerVo;
import cn.jojo.infra.sdk.api.metadata.DefaultHttpResult;
import cn.jojo.infra.sdk.api.metadata.IHttpResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.hibernate.validator.constraints.Length;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 贴纸controller
 * @date 2024/9/23 19:36
 **/

@Validated
@RestController
@RequestMapping("admin/sticker-pages")
@Tag(name = "贴纸页")
public class StickerPagesController {

    @Resource
    private IStickerPagesBizService stickerPagesBizService;
    @Resource
    private IStickerBizService stickerBizService;

    @Operation(summary = "贴纸页查询", description = "贴纸页查询")
    @GetMapping("/{stickerPageKey}")
    public IHttpResult<StickerPageVo> getStickerPages(@Valid
                                                      @Schema(description = "贴纸页key")
                                                      @Length(max = 20)
                                                      @NotBlank(message = "stickerPageKey must not blank")
                                                      @PathVariable String stickerPageKey,

                                                      @Schema(description = "课程id，匹配教研环境使用，避免错误操作等原因导致业务继续进行和脏数据落库等，所以不做默认值处理")
                                                      @Positive Long courseId) {

        return DefaultHttpResult.successWithData(stickerPagesBizService.getStickerPages(stickerPageKey, courseId));
    }


    @Operation(summary = "查询贴纸页关联道具", description = "查询贴纸页关联道具")
    @GetMapping("/{stickerPageId}/sticker-props/{stickerPropKey}")
    public IHttpResult<StickerVo> getStickerProp(@Valid
                                                 @Schema(description = "贴纸页id")
                                                 @NotNull(message = "stickerPageId must not null")
                                                     @Positive
                                                     @PathVariable Long stickerPageId,

                                                 @Schema(description = "贴纸道具key")
                                                 @Length(max = 30)
                                                 @PathVariable String stickerPropKey,

                                                 @Schema(description = "课程id，匹配教研环境使用，避免错误操作等原因导致业务继续进行和脏数据落库等，所以不做默认值处理")
                                                 @Positive Long courseId) {

        return DefaultHttpResult.successWithData(
            stickerBizService.getByStickerKey(stickerPropKey, courseId, stickerPageId));
    }
}
