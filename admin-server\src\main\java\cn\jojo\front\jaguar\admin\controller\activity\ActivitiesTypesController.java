package cn.jojo.front.jaguar.admin.controller.activity;

import cn.jojo.front.jaguar.admin.model.convert.ActivityModuleTypeReq2BoConvert;
import cn.jojo.front.jaguar.admin.model.req.QueryConfigurationRequest;
import cn.jojo.front.jaguar.biz.service.activitity.IActivityModuleTypeBizService;
import cn.jojo.front.jaguar.biz.service.pojo.bo.common.ActivityModuleTypeBo;
import cn.jojo.front.jaguar.common.pojo.vo.common.ActivityModuleTypeVo;
import cn.jojo.infra.sdk.api.metadata.DefaultHttpResult;
import cn.jojo.infra.sdk.api.metadata.IHttpResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 活动下对应模块枚举类型查询controller
 * @date 2024/3/14 16:17
 **/

@Validated
@RestController
@RequestMapping("admin/activity-types")
@Tag(name = "活动下对应模块枚举类型查询controller")
public class ActivitiesTypesController {

    @Resource
    private IActivityModuleTypeBizService activityModuleTypeBizService;

    /**
     * 活动下对应模块枚举类型查询
     *
     * @param queryReq 活动下对应模块枚举类型查询 req
     */
    @GetMapping
    @Operation(summary = "活动下对应模块枚举类型查询", description = "活动下对应模块枚举类型查询")
    public IHttpResult<List<ActivityModuleTypeVo>> queryTypes(@ModelAttribute QueryConfigurationRequest queryReq) {

        ActivityModuleTypeBo activityModuleTypeBo = ActivityModuleTypeReq2BoConvert.INSTANCE.model1ToModel2(queryReq);

        return DefaultHttpResult.successWithData(activityModuleTypeBizService.list(activityModuleTypeBo));
    }
}
