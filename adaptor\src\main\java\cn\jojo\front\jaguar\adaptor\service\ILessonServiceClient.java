package cn.jojo.front.jaguar.adaptor.service;



import cn.jojo.cc.common.dto.CourseLessonDto;
import cn.jojo.front.jaguar.common.pojo.bo.SimpleLessonBo;

import java.util.List;

public interface ILessonServiceClient {


    /**
     * 获取简单内容信息
     *
     * @param lessonIds
     * @return
     */
    List<SimpleLessonBo> getSimpleLessonByIds(List<Long> lessonIds);

    /**
     * 获取课程下的所有课时
     *
     * @param courseId 课程id
     * @return 课时信息
     */
    List<CourseLessonDto> listByCourseId(Long courseId);
}
