package cn.jojo.front.jaguar.admin.controller;


import cn.jojo.front.jaguar.biz.service.EvaluationMatchRuleBizService;
import cn.jojo.front.jaguar.common.enums.EvaluationMatchRuleGroupEnum;
import cn.jojo.front.jaguar.common.enums.PhaseEnum;
import cn.jojo.front.jaguar.common.exception.BusinessException;
import cn.jojo.front.jaguar.common.pojo.bo.EmployeeBo;
import cn.jojo.front.jaguar.common.pojo.check.InsertGroup;
import cn.jojo.front.jaguar.common.pojo.check.UpdateGroup;
import cn.jojo.front.jaguar.common.pojo.req.EvaluationMatchRuleReq;
import cn.jojo.front.jaguar.common.pojo.vo.EvaluationMatchRuleVo;
import cn.jojo.infra.sdk.api.metadata.DefaultHttpResult;
import cn.jojo.infra.sdk.api.metadata.IHttpResult;
import cn.jojo.infra.sdk.api.metadata.IPageResp;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Optional;

@RestController
@RequestMapping(value = "admin/evaluation-match-rules")
@Tag(name = "问卷规则相关")
public class EvaluationMatchRuleController extends BaseController {
    @Resource
    private EvaluationMatchRuleBizService evaluationMatchRuleBizService;

    @GetMapping
    @Operation(description = "分页查询规则列表", summary = "分页查询规则列表")
    public IHttpResult<IPageResp<EvaluationMatchRuleVo>> findByGroup(
        @Parameter(description = "分组", schema = @Schema(implementation = EvaluationMatchRuleGroupEnum.class))
        @RequestParam(value = "group", defaultValue = "1", required = false) Integer group,
        @Parameter(description = "课程阶段")
        @RequestParam(value = "courseSegmentCode", required = false) Integer courseSegmentCode,
        @Parameter(description = "期")
        @RequestParam(value = "phase", required = false) Integer phase,
        @Parameter(description = "页索引(从第一页开始)")
        @RequestParam(value = "pageIndex", defaultValue = "1", required = false) int pageIndex,
        @Parameter(description = "每页条数")
        @RequestParam(value = "pageSize", defaultValue = "20", required = false) int pageSize) {

        if (phase != null && PhaseEnum.codeOf(phase) == null) {
            throw BusinessException.paramException("phase should be in (1-12)");
        }

        EvaluationMatchRuleGroupEnum groupEnum =
            Optional.ofNullable(EvaluationMatchRuleGroupEnum.codeOf(group))
                .orElseThrow(() -> BusinessException.paramException("type not exit;" + group));

        return DefaultHttpResult.successWithData(evaluationMatchRuleBizService.findByGroup(groupEnum, courseSegmentCode, phase, pageIndex, pageSize));
    }

    @PostMapping
    @Operation(description = "添加规则", summary = "添加规则")
    public IHttpResult<Boolean> save(@Validated(value = InsertGroup.class) @RequestBody EvaluationMatchRuleReq req) {
        evaluationMatchRuleBizService.saveOrUpdate(req, new EmployeeBo().setEmployeeId(getEmployeeId()).setEmployeeName(getEmployeeName()));
        return DefaultHttpResult.successWithData(true);
    }

    @PutMapping("/{ruleId}")
    @Operation(description = "更新规则", summary = "更新规则")
    public IHttpResult<Boolean> update(@Parameter(description = "规则ID") @PathVariable Long ruleId,
                                       @Validated(value = UpdateGroup.class) @RequestBody EvaluationMatchRuleReq req) {
        req.setId(ruleId);
        evaluationMatchRuleBizService.saveOrUpdate(req, new EmployeeBo().setEmployeeId(getEmployeeId()).setEmployeeName(getEmployeeName()));
        return DefaultHttpResult.successWithData(true);
    }

    @DeleteMapping("/{ruleId}")
    @Operation(description = "删除规则", summary = "删除规则")
    public IHttpResult<Boolean> del(@Parameter(description = "规则ID") @PathVariable Long ruleId) {
        evaluationMatchRuleBizService.del(ruleId, new EmployeeBo().setEmployeeId(getEmployeeId()).setEmployeeName(getEmployeeName()));
        return DefaultHttpResult.successWithData(true);
    }
}
