package cn.jojo.front.jaguar.admin.model.dto.task;

import cn.jojo.front.jaguar.common.enums.task.TaskConditionTargetTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2024/03/11
 **/

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema(description="子任务dto")
public class SubTaskDto {
    @Schema(description="子任务id")
    private Long id;

    @Schema(description="条件id")
    private Long conditionId;

    @Schema(description="条件：TargetTypeEnum", implementation = TaskConditionTargetTypeEnum.class)
    private Integer targetType;

    @Schema(description="次数")
    private String targetValue;

    @Schema(description="范围：主题月id,课时id,时间")
    private EventScopeDto eventScope;

    @Schema(description = "奖励列表")
    private List<RewardDto> rewards;

    @Schema(description="子任务扩展资源")
    private TaskExtendResourceDto taskExtendResource;

}
