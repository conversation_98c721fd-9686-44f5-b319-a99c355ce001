package cn.jojo.front.jaguar.adaptor.service.impl;

import cn.jojo.crm.request.group.UnifyPageGroupReq;
import cn.jojo.crm.response.group.UnifyGroupResp;
import cn.jojo.crm.service.IGroupService;
import cn.jojo.front.jaguar.adaptor.service.ICrmUserGroupClient;
import cn.jojo.front.jaguar.common.pojo.bo.PageResultBo;
import cn.jojo.front.jaguar.common.pojo.bo.UserGroupInfoBo;
import cn.jojo.front.jaguar.common.pojo.req.CrmUserGroupListReq;
import cn.jojo.infra.sdk.api.metadata.IPageResp;
import cn.jojo.infra.sdk.api.metadata.IRpcResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class CrmUserGroupClient implements ICrmUserGroupClient {

    @DubboReference
    private IGroupService groupService;


    @Override
    public PageResultBo<UserGroupInfoBo> queryUserGroup(CrmUserGroupListReq listReq) {
        UnifyPageGroupReq req = UnifyPageGroupReq.builder().build();
        BeanUtils.copyProperties(listReq, req);
        req.setPageSize(listReq.getPageSize());
        req.setPageNum(listReq.getPageNum());
        IRpcResult<IPageResp<UnifyGroupResp>> rpcResult = groupService.unifyPage(req);
        if (!rpcResult.checkSuccess()) {
            log.error("invoke groupService.unifyPage failed, message={}", rpcResult.getMessage());
            return new PageResultBo<>();
        }
        Optional<IPageResp<UnifyGroupResp>> dataOpt = Optional.ofNullable(rpcResult.getData());
        List<UserGroupInfoBo> dataList = dataOpt
                .map(IPageResp::getPageRecords)
                .orElse(Collections.emptyList())
                .stream()
                .map(item -> {
                    UserGroupInfoBo bo = new UserGroupInfoBo();
                    BeanUtils.copyProperties(item, bo);
                    return bo;
                }).collect(Collectors.toList());
        return new PageResultBo<UserGroupInfoBo>()
                .setRecords(dataList)
                .setPageNum(dataOpt.map(IPageResp::getPageNum).orElse(1L))
                .setPageSize(dataOpt.map(IPageResp::getPageSize).orElse(10L))
                .setTotal(dataOpt.map(IPageResp::getTotalCount).orElse(0L));
    }
}
