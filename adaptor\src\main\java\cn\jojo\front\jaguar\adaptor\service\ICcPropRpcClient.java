package cn.jojo.front.jaguar.adaptor.service;

import cn.jojo.cc.common.dto.StickerPropDto;
import cn.jojo.front.jaguar.adaptor.model.req.CcPropReq;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description CC 道具service client
 * @date 2024/9/26 19:43
 **/
public interface ICcPropRpcClient {


    /**
     * 根据道具 ID 查询贴纸道具
     *
     * @param req 查询参数，必填项如下：
     * @return 贴纸道具列表
     */
    List<StickerPropDto> queryStickerPropByIds(CcPropReq req);

    /**
     * 根据道具 key 查询贴纸道具
     *
     * @param req 查询参数，必填项如下：
     * @return 贴纸道具列表
     */
    List<StickerPropDto> queryStickerPropByKeys(CcPropReq req);
}
