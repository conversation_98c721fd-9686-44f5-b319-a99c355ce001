package cn.jojo.front.jaguar.adaptor.service;


import cn.jojo.edu.fantasy.rpc.api.dto.EduAlbumAggregationDto;
import cn.jojo.edu.fantasy.rpc.api.dto.EduAlbumContentUpdateDto;
import cn.jojo.front.jaguar.common.pojo.bo.AlbumBo;
import cn.jojo.front.jaguar.common.pojo.bo.EduAlbumBo;
import cn.jojo.front.jaguar.common.pojo.bo.EduResourcesAlbumBo;
import cn.jojo.front.jaguar.common.pojo.req.ListResourcesAlbumRpcReq;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/03/24
 **/
public interface IEduAlbumRpcClient {

    List<EduAlbumAggregationDto> queryAggregation(List<Long> albumIds, List<Integer> typeList, Boolean containsInvalid);

    List<EduAlbumContentUpdateDto> getContentUpdateAlbums(List<Long> albumIds);

    /**
     * 模糊查询album相关专辑数据信息
     *
     * @param hubId       专辑库id
     * @param albumId     专辑id
     * @param albumName   专辑名称
     * @param contentType 内容类型
     * @param pageNum     頁碼
     * @param pageSize    页数据量
     * @return 信息列表
     */
    Page<AlbumBo> getAlbumsByFuzzyQuery(String contentType, Long hubId, Long albumId, String albumName,
                                        Integer pageNum, Integer pageSize);


    /**
     * 根据专辑id批量查询查询专辑名称
     *
     * @param albumId
     * @return
     */
    List<EduAlbumBo> getAlbumsByIds(List<Long> albumId);


    /**
     * 获取资源和专辑关联关系列表
     *
     * @return results
     */
    List<EduResourcesAlbumBo> listResourcesAlbums(ListResourcesAlbumRpcReq rpcReq);
}
