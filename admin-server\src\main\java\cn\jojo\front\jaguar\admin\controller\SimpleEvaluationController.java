package cn.jojo.front.jaguar.admin.controller;


import cn.jojo.common.api.ex.DefaultHttpActionPageResult;
import cn.jojo.common.api.ex.DefaultHttpActionResult;
import cn.jojo.common.api.ex.IHttpActionResult;
import cn.jojo.front.jaguar.common.pojo.bo.PageBo;
import cn.jojo.front.jaguar.common.pojo.req.SimpleEvaluationBindingQuestionSaveReq;
import cn.jojo.front.jaguar.common.pojo.req.SimpleEvaluationCopyReq;
import cn.jojo.front.jaguar.common.pojo.req.SimpleEvaluationDimensionRuleSaveReq;
import cn.jojo.front.jaguar.common.pojo.req.SimpleEvaluationDimensionSaveReq;
import cn.jojo.front.jaguar.common.pojo.req.SimpleEvaluationListReq;
import cn.jojo.front.jaguar.common.pojo.req.SimpleEvaluationRuleSaveReq;
import cn.jojo.front.jaguar.common.pojo.req.SimpleEvaluationSaveReq;
import cn.jojo.front.jaguar.common.pojo.req.SimpleEvaluationUpdateSaveReq;
import cn.jojo.front.jaguar.common.pojo.vo.EvaluationRelationVo;
import cn.jojo.front.jaguar.common.pojo.vo.EvaluationVo;
import cn.jojo.front.jaguar.common.pojo.vo.SimpleEvaluationListVo;
import cn.jojo.front.jaguar.core.service.question.SimpleEvaluationDimensionService;
import cn.jojo.front.jaguar.core.service.question.SimpleEvaluationService;
import cn.jojo.infra.sdk.api.metadata.IPageResp;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.util.List;
import java.util.Objects;


/**
 * 测评问卷管理
 * <AUTHOR>
 * @date 2023/11/6 14:33
 */
@RestController
@RequestMapping(value = "admin/simple-evaluation")
@Tag(name = "Jaguar管理后台-测评问卷管理")
@Validated
public class SimpleEvaluationController extends BaseController {

    @Autowired
    private SimpleEvaluationService simpleEvaluationService;

    @Autowired
    private SimpleEvaluationDimensionService simpleEvaluationDimensionService;


    @GetMapping("/get-evaluation-list")
    @Operation(summary ="查询问卷列表",   description = "查询问卷列表")
    public IHttpActionResult<IPageResp<SimpleEvaluationListVo>> queryList(@Validated SimpleEvaluationListReq req) {
        if (Objects.nonNull(req.getQuerySelf()) && req.getQuerySelf()) {
            req.setCreateUserId(getEmployeeId());
            req.setCreateUserName(getEmployeeName());
        }
        PageBo<SimpleEvaluationListVo> pageResult = simpleEvaluationService.listEvaluations(req);
        return DefaultHttpActionPageResult.successWithPageData(pageResult.getPageNum(), pageResult.getPageSize(),
            pageResult.getTotal(), pageResult.getData());
    }

    @PostMapping("/save-evaluation")
    @Operation(summary ="保存更新问卷基本信息",   description = "保存更新问卷基本信息")
    public IHttpActionResult<Long> save(@Validated @RequestBody SimpleEvaluationSaveReq body) {
        Long id = simpleEvaluationService.save(body, getEmployeeId(), getEmployeeName());
        return DefaultHttpActionResult.successWithData(id);
    }

    @PostMapping("/save-dimension")
    @Operation(summary = "保存测评问卷维度信息(老接口)", description = "该接口后续不再维护，业务逻辑已迁移至PUT /api/jaguar/admin/simple-evaluations/{evaluationId}")
    public IHttpActionResult<Long> saveDimensionList(@Validated @RequestBody SimpleEvaluationDimensionSaveReq req) {
        simpleEvaluationDimensionService.saveDimensionList(req, getEmployeeId(), getEmployeeName());
        return DefaultHttpActionResult.successWithoutData();
    }

    @GetMapping("/get-evaluation-info")
    @Operation(summary = "查询测评问卷信息(老接口)", description = "该接口后续不再维护，业务逻辑已迁移至GET /api/jaguar/admin/simple-evaluations/{evaluationId}")
    public IHttpActionResult<EvaluationVo> bindQuestion(@RequestParam @NotNull Long id) {
        EvaluationVo evaluationInfo = simpleEvaluationService.getEvaluationInfo(id);
        return DefaultHttpActionResult.successWithData(evaluationInfo);
    }

    @PostMapping("/binding-questions")
    @Operation(summary = "保存测评问卷题目信息(老接口)", description = "该接口后续不再维护，业务逻辑已迁移至PUT /api/jaguar/admin/simple-evaluations/{evaluationId}")
    public IHttpActionResult<Long> bindingQuestions(@Validated @RequestBody SimpleEvaluationBindingQuestionSaveReq req) {
        simpleEvaluationDimensionService.bindingQuestions(req, getEmployeeId(), getEmployeeName());
        return DefaultHttpActionResult.successWithoutData();
    }

    @PostMapping("/set-dimension-rule")
    @Operation(summary = "保存测评问卷维度规则(老接口)", description = "该接口后续不再维护，业务逻辑已迁移至PUT /api/jaguar/admin/simple-evaluations/{evaluationId}")
    public IHttpActionResult<Long> setDimensionRule(@Validated @RequestBody SimpleEvaluationDimensionRuleSaveReq req) {
        simpleEvaluationDimensionService.setRule(req, getEmployeeId(), getEmployeeName());
        return DefaultHttpActionResult.successWithoutData();
    }

    @PostMapping("/set-evaluation-rule")
    @Operation(summary = "设置测评问卷算分规则(老接口)", description = "该接口后续不再维护，业务逻辑已迁移至PUT /api/jaguar/admin/simple-evaluations/{evaluationId}")
    public IHttpActionResult<Long> setEvaluationRule(@Validated @RequestBody SimpleEvaluationRuleSaveReq req) {
        simpleEvaluationService.setRule(req, getEmployeeId(), getEmployeeName());
        return DefaultHttpActionResult.successWithoutData();
    }

    @PostMapping("/update-evaluation")
    @Operation(summary ="更新问卷",   description = "更新问卷")
    public IHttpActionResult<Long> updateEvaluation(@Validated @RequestBody SimpleEvaluationUpdateSaveReq req) {
        simpleEvaluationService.updateEvaluation(req, getEmployeeId(), getEmployeeName());
        return DefaultHttpActionResult.successWithoutData();
    }

    @GetMapping("/relation")
    public IHttpActionResult<List<EvaluationRelationVo>> relation(@NotNull @Positive Long id) {
        List<EvaluationRelationVo> result = simpleEvaluationService.getRelation(id);
        return DefaultHttpActionResult.successWithData(result);
    }
}
