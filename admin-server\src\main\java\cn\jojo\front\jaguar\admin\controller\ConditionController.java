package cn.jojo.front.jaguar.admin.controller;

import cn.jojo.cc.common.dto.SegmentDto;
import cn.jojo.cc.common.dto.SubjectTypeDto;
import cn.jojo.edu.common.dict.course.CourseType;
import cn.jojo.edu.malacca.rpc.api.dto.EduClassDto;
import cn.jojo.edu.malacca.rpc.api.dto.EduTeacherDto;
import cn.jojo.front.jaguar.adaptor.model.req.ClassListReq;
import cn.jojo.front.jaguar.adaptor.service.ConditionParamService;
import cn.jojo.front.jaguar.common.enums.SubCourseType;
import cn.jojo.front.jaguar.common.pojo.vo.CourseSearchVo;
import cn.jojo.front.jaguar.common.pojo.vo.PageJumpVo;
import cn.jojo.infra.sdk.api.metadata.DefaultHttpResult;
import cn.jojo.infra.sdk.api.metadata.IHttpResult;
import cn.jojo.uc.api.domain.dto.DictDto;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("admin/condition")
@Tag(name = "Jaguar管理后台")
@Validated
public class ConditionController {

    @Resource
    private ConditionParamService conditionParamService;

    @Operation(summary ="科目类型列表",   description = "获取科目类型列表")
    @GetMapping("/getAllSubjectType")
    public IHttpResult<List<SubjectTypeDto>> getAllSubjectType() {
        return DefaultHttpResult.successWithData(conditionParamService.getAllSubjectType());
    }

    @Operation(summary = "课程列表", description = "获取课程列表")
    @GetMapping("/listCourse")
    public IHttpResult<List<CourseSearchVo>> listCourse(
        @RequestParam(value = "courseType", required = false) @Parameter(description = "课程类型") CourseType courseType,
        @RequestParam(value = "subCourseType", required = false) @Parameter(description = "子课程类型") SubCourseType subCourseType,
        @RequestParam(value = "subjectType", required = false) @Parameter(description = "科目类型") Integer subjectType,
        @RequestParam(value = "courseSegmentCodes", required = false) @Parameter(description = "课程阶段") List<Integer> courseSegmentCodes,
        @RequestParam(value = "keyword", required = false) @Parameter(description = "关键词") String keyword) {
        Integer courseTypeValue = courseType != null ? courseType.getType() : null;
        Integer subCourseTypeValue = subCourseType != null ? subCourseType.getType() : null;
        return DefaultHttpResult.successWithData(
                conditionParamService.listCourse(courseTypeValue, subCourseTypeValue, subjectType, courseSegmentCodes, keyword));
    }

    @Operation(summary ="班级列表",   description = "获取班级列表")
    @GetMapping("/listClass")
    public IHttpResult<List<EduClassDto>> listClass(@Validated ClassListReq req) {
        return DefaultHttpResult
            .successWithData(conditionParamService.listClass(req.getCourseIds(), req.getClassIds()));
    }

    @Operation(summary ="主题月列表",   description = "获取主题月列表")
    @GetMapping("/listSubject")
    public IHttpResult<List<SegmentDto>> listSubject(@RequestParam(value = "courseId") @NotNull @Positive Integer courseId) {
        return DefaultHttpResult.successWithData(conditionParamService.listSubject(courseId));
    }

    @Operation(summary ="获取所有页面url",   description = "获取所有页面url")
    @GetMapping("/listPageUrl")
    public IHttpResult<List<PageJumpVo>> listPageUrl() {
        return DefaultHttpResult.successWithData(conditionParamService.listPageUrl());
    }

    @Operation(summary ="老师列表",   description = "获取老师列表")
    @GetMapping("/listTeacher")
    public IHttpResult<List<EduTeacherDto>> listTeacher(@RequestParam(value = "classIds") @NotEmpty List<Integer> classIds) {
        return DefaultHttpResult.successWithData(conditionParamService.listTeacher(classIds));
    }

    @Operation(summary ="年级列表",   description = "获取年级列表")
    @GetMapping("/listGrade")
    public IHttpResult<List<DictDto>> listAllGrade() {
        return DefaultHttpResult.successWithData(conditionParamService.listGrade());
    }
}
