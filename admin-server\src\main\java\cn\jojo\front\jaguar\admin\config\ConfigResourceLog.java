package cn.jojo.front.jaguar.admin.config;

import javax.annotation.PostConstruct;
import org.apache.commons.lang.StringUtils;
import org.redisson.api.RedissonClient;
import org.redisson.config.SingleServerConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
import org.springframework.stereotype.Component;

@Component
public class ConfigResourceLog {

    public static final Logger logger = LoggerFactory.getLogger(ConfigResourceLog.class);

    @Value("${apollo.meta}")
    private String apolloMeta;

    @Autowired
    private DataSourceProperties basicProperties;

    @Autowired
    private RedissonClient redissonClient;

    @PostConstruct
    public void resourceLog() {
        logger.info("ResourceLog-apolloMeta:{}", apolloMeta);
        logger.info("ResourceLog-dataSource url:{}, username:{}, password:{}", basicProperties.getUrl(),
            basicProperties.getUsername(),
            !StringUtils.isEmpty(basicProperties.getPassword()) ? desensitizePwd(basicProperties.getPassword())
                : basicProperties.getPassword());
        SingleServerConfig singleServerConfig = redissonClient.getConfig().useSingleServer();
        logger.info("ResourceLog-redissonClient database:{}, address:{}, password:{}",
            singleServerConfig.getDatabase(), singleServerConfig.getAddress(),
            !StringUtils.isEmpty(basicProperties.getPassword()) ? desensitizePwd(basicProperties.getPassword())
                : basicProperties.getPassword());

    }

    private String desensitizePwd(String password) {
        if (StringUtils.isBlank(password) || StringUtils.length(password) <= 6) {
            return StringUtils.EMPTY;
        }
        return StringUtils.substring(password, 0, 3) + "***" + StringUtils.substring(password, -3);
    }

}
