package cn.jojo.front.jaguar.admin.controller;

import cn.jojo.common.api.ex.DefaultHttpActionPageResult;
import cn.jojo.common.api.ex.IHttpActionResult;
import cn.jojo.front.jaguar.biz.service.impl.ChannelNumberBizService;
import cn.jojo.front.jaguar.common.pojo.bo.PageBo;
import cn.jojo.front.jaguar.common.pojo.req.ChannelNoDimensionListPageReq;
import cn.jojo.front.jaguar.common.pojo.req.ChannelNoEnumListPageReq;
import cn.jojo.front.jaguar.common.pojo.req.ChannelNumberListReq;
import cn.jojo.front.jaguar.common.pojo.vo.ChannelNoDimensionItemVo;
import cn.jojo.front.jaguar.common.pojo.vo.ChannelNoEnumItemVo;
import cn.jojo.front.jaguar.common.pojo.vo.ChannelNoItemVo;
import cn.jojo.infra.sdk.api.metadata.IPageResp;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * 2025/3/18 14:42
 */
@RestController
@RequestMapping("/admin")
@Validated
public class ChannelNumberController {
    @Resource
    private ChannelNumberBizService channelNumberBizService;

    @GetMapping("/channel-numbers")
    @Operation(summary = "获取渠道码列表", description = "分页获取商城渠道码列表，包含版本、维度等信息")
    public IHttpActionResult<IPageResp<ChannelNoItemVo>> list(ChannelNumberListReq req) {
        PageBo<ChannelNoItemVo> pageInfo = channelNumberBizService
            .listChannelNoPage(req.getChannelNos(), req.getFuzzyQuery(), req.getPageNum(), req.getPageSize());
        return DefaultHttpActionPageResult.successWithPageData(
            pageInfo.getPageNum(), pageInfo.getPageSize(), pageInfo.getTotal(), pageInfo.getData());
    }

    @GetMapping("/channel-number-dimensions")
    @Operation(summary = "获取渠道码维度列表", description = "获取渠道码维度列表，用于B端下拉选择")
    public IHttpActionResult<IPageResp<ChannelNoDimensionItemVo>> listDimension(ChannelNoDimensionListPageReq req) {
        PageBo<ChannelNoDimensionItemVo> pageInfo = channelNumberBizService
            .getDimensionListPage(req);
        return DefaultHttpActionPageResult.successWithPageData(
            pageInfo.getPageNum(), pageInfo.getPageSize(), pageInfo.getTotal(), pageInfo.getData()
        );
    }

    @GetMapping("/channel-number-enums")
    @Operation(summary = "获取渠道码枚举列表", description = "获取渠道码枚举列表，用于B端下拉选择")
    public IHttpActionResult<IPageResp<ChannelNoEnumItemVo>> listEnum(ChannelNoEnumListPageReq req) {
        PageBo<ChannelNoEnumItemVo> pageInfo = channelNumberBizService
            .getEnumListPage(req);
        return DefaultHttpActionPageResult.successWithPageData(
            pageInfo.getPageNum(), pageInfo.getPageSize(), pageInfo.getTotal(), pageInfo.getData()
        );
    }
}
