package cn.jojo.front.jaguar.adaptor.service;


import cn.jojo.front.jaguar.common.pojo.bo.UserLoginInfoVO;
import cn.jojo.uc.api.domain.dto.WechatUserFollowSimpleDto;

import java.util.List;

public interface IUserCenterRpcServiceClient {


    /**
     * 根据包名与用户id获取第一次和最近一次登录时间
     * @param packageName 包名
     * @param userId 用户id
     * @return 登录信息
     */
    UserLoginInfoVO getLoginTimeByPackageName(String packageName, Long userId);

    WechatUserFollowSimpleDto getFollowStatusByAppIdAndUserId(Long userId, String appId);
}
