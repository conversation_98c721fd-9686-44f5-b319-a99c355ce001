package cn.jojo.front.jaguar.admin.controller;

import cn.jojo.edu.common.utils.IterateUtil;
import oagateway.client.springbootstarter.model.EmployeeInfo;
import oagateway.client.springbootstarter.model.Result;
import oagateway.client.springbootstarter.server.EmployeeClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 描述: 基础BastController 用于统一获取用户信息、简单参数校验
 * <p>
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">yangxx</a>
 * @version : Ver 1.0
 * @date : 2019-11-18 11:33
 */
public class BaseController {

    private static final Logger log = LoggerFactory.getLogger(BaseController.class);

    @Autowired
    private EmployeeClient employeeClient;

    /**
     * 获取员工ID
     *
     * @return 结果
     */
    public Long getEmployeeId() {
        return employeeClient.getEmployeeId();
    }

    /**
     * 获取员工姓名
     *
     * @return 结果
     */
    public String getEmployeeName() {
        Long employeeId = employeeClient.getEmployeeId();
        return getEmployeeName(employeeId);
    }

    /**
     * 获取员工姓名
     *
     * @param employeeId 员工ID
     * @return 员工名称
     */
    public String getEmployeeName(Long employeeId) {
        try {
            Result<EmployeeInfo> result = employeeClient.searchEmployee(employeeId);
            if (Boolean.TRUE.equals(result.isSuccess())) {
                return result.getData().getUserName();
            } else {
                log.warn("调用员工服务查询信息失败，employeeId:{},status:{},errorCode:{},errorMsg:{}", employeeId,
                    result.getStatus(), result.getErrorCode(), result.getErrorMsg());
                return String.valueOf(employeeId);
            }

        } catch (IOException e) {
            log.error("调用员工服务查询信息失败，employeeId:{}", employeeId, e);
        }

        return String.valueOf(employeeId);
    }

    /**
     * 获取员工姓名
     *
     * @param employeeId 员工ID
     * @return 员工名称
     */
    public Map<Long, String> getEmployeeNames(List<Long> employeeId) {
        try {
            Result<List<EmployeeInfo>> result = employeeClient.searchEmployees(employeeId);
            if (Boolean.TRUE.equals(result.isSuccess())) {
                return result.getData().stream()
                    .collect(Collectors.toMap(EmployeeInfo::getId, EmployeeInfo::getUserName, (a, b) -> a));
            } else {
                log.warn("调用员工服务批量查询信息失败，employeeId:{},status:{},errorCode:{},errorMsg:{}", employeeId,
                    result.getStatus(), result.getErrorCode(), result.getErrorMsg());
                return employeeId.stream().collect(Collectors.toMap(a -> a, String::valueOf, (a, b) -> a));
            }
        } catch (IOException e) {
            log.error("调用员工服务批量查询信息失败，employeeId:{}", employeeId, e);
        }

        return employeeId.stream().collect(Collectors.toMap(a -> a, String::valueOf, (a, b) -> a));
    }

    public Map<Long, String> bathGetEmployeeNames(List<Long> employeeIds, int batchSize) {
        if (batchSize <= 0) {
            batchSize = 100;
        }
        List<EmployeeInfo> employeeInfos = IterateUtil.spiltIterate(ids -> {
            try {
                Result<List<EmployeeInfo>> result = employeeClient.searchEmployees(ids);
                if (Boolean.TRUE.equals(result.isSuccess())) {
                    return result.getData();
                }
            } catch (IOException e) {
                log.error("调用员工服务批量查询信息失败，employeeIds:{}", ids, e);
            }
            return ids.stream().map(id -> {
                EmployeeInfo employeeInfo = new EmployeeInfo();
                employeeInfo.setId(id);
                employeeInfo.setUserName(String.valueOf(id));
                return employeeInfo;
            }).collect(Collectors.toList());
        }, employeeIds, batchSize);
        return employeeInfos.stream().collect(
            Collectors.toMap(EmployeeInfo::getId, EmployeeInfo::getUserName, (a, b) -> a));
    }

}
