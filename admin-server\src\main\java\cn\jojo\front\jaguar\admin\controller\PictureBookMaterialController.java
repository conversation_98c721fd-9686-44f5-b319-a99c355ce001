package cn.jojo.front.jaguar.admin.controller;

import cn.jojo.common.api.ex.DefaultHttpActionPageResult;
import cn.jojo.common.api.ex.DefaultHttpActionResult;
import cn.jojo.common.api.ex.IHttpActionResult;
import cn.jojo.front.jaguar.common.pojo.req.BaseListReq;
import cn.jojo.front.jaguar.common.pojo.req.PictureBookMaterialSaveReq;
import cn.jojo.front.jaguar.common.pojo.vo.PictureBookMaterialInfoVo;
import cn.jojo.front.jaguar.common.pojo.vo.PictureBookMaterialListVo;
import cn.jojo.front.jaguar.common.pojo.vo.PictureBookMaterialRelationVo;
import cn.jojo.front.jaguar.core.service.picturebook.PictureBookMaterialDetailService;
import cn.jojo.front.jaguar.core.service.picturebook.PictureBookMaterialService;
import cn.jojo.infra.sdk.api.metadata.DefaultHttpResult;
import cn.jojo.infra.sdk.api.metadata.IHttpResult;
import cn.jojo.infra.sdk.api.metadata.IPageResp;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import javax.annotation.Resource;
import javax.validation.constraints.NotNull;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2022/10/24 15:59
 * @desc
 */
@Tag(name = "Jaguar管理后台")
@Validated
@RestController
@RequestMapping(value = "admin/picture-book-material")
public class PictureBookMaterialController {

    @Resource
    private PictureBookMaterialService pictureBookMaterialService;
    @Resource
    private PictureBookMaterialDetailService pictureBookMaterialDetailService;

    /**
     * 查询绘本素材列表
     *
     * @param req 查询参数
     * @return 绘本素材列表
     */
    @GetMapping("list")
    @Operation(summary ="绘本素材详情列表",   description = "绘本素材详情列表")
    public IHttpActionResult<IPageResp<PictureBookMaterialListVo>> listPictureBook(BaseListReq<Void> req) {
        IPage<PictureBookMaterialListVo> page = pictureBookMaterialService.listPage(req);
        return DefaultHttpActionPageResult
            .successWithPageData(page.getCurrent(), page.getSize(), page.getTotal(), page.getRecords());
    }

    /**
     * 保存绘本素材
     *
     * @param req 保存参数
     * @return 绘本素材id
     */
    @PostMapping("/saveOrUpdate")
    @Operation(summary ="保存绘本素材",   description = "保存绘本素材")
    public IHttpActionResult<Long> saveOrUpdateAdvertisement(
        @RequestBody @Validated PictureBookMaterialSaveReq req) {
        Long id = pictureBookMaterialService.saveOrUpdate(req);
        pictureBookMaterialService.deleteCache(Collections.singleton(id));
        pictureBookMaterialDetailService.deleteCache(Collections.singleton(id));
        return DefaultHttpActionResult.successWithData(id);
    }

    /**
     * 查询绘本素材详情
     *
     * @param pictureBookMaterialId 绘本素材id
     * @return 绘本素材详情
     */
    @GetMapping("/detail")
    @Operation(summary ="绘本素材详情",   description = "绘本素材详情")
    public IHttpActionResult<PictureBookMaterialInfoVo> detail(@RequestParam @NotNull Long pictureBookMaterialId) {
        Optional<PictureBookMaterialInfoVo> detail = pictureBookMaterialService.getDetail(pictureBookMaterialId);
        return DefaultHttpActionResult.successWithData(detail.orElse(null));
    }

    /**
     * 删除绘本素材
     *
     * @param pictureBookMaterialId 绘本素材id
     * @return 是否成功
     */
    @GetMapping("/delete")
    @Operation(summary ="删除绘本素材",   description = "删除绘本素材")
    public IHttpResult<Boolean> delete(@RequestParam @NotNull Long pictureBookMaterialId) {
        pictureBookMaterialService.deleteById(pictureBookMaterialId);
        pictureBookMaterialService.deleteCache(Collections.singletonList(pictureBookMaterialId));
        return DefaultHttpResult.successWithData(true);
    }

    @GetMapping("/relation")
    @Operation(summary ="获取素材关联关系",   description = "获取素材关联关系")
    public IHttpActionResult<List<PictureBookMaterialRelationVo>> relation(@RequestParam @NotNull Long id) {
        List<PictureBookMaterialRelationVo> relations = pictureBookMaterialService.getConfigRelationById(id);
        return DefaultHttpActionResult.successWithData(relations);
    }
}
