package cn.jojo.front.jaguar.admin.controller;

import cn.jojo.common.api.ex.DefaultHttpActionPageResult;
import cn.jojo.common.api.ex.IHttpActionResult;
import cn.jojo.front.jaguar.common.pojo.req.SquarePlateSearchReq;
import cn.jojo.front.jaguar.common.pojo.vo.SquarePlateSearchVo;
import cn.jojo.front.jaguar.core.service.square.SquareMaterialService;
import cn.jojo.infra.sdk.api.metadata.IPageResp;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2024/9/5 15:09
 */
@RestController
@RequestMapping("/admin/square-plates")
@Tag(name = "Jaguar管理后台")
@Validated
public class SquarePlateControllerV2 {


    @Resource
    private SquareMaterialService squareMaterialService;

    @PostMapping("/search")
    @Operation(summary = "分页搜索广场板块列表", description = "分页搜索广场板块(对应squareMaterial)")
    public IHttpActionResult<IPageResp<SquarePlateSearchVo>> listSquareMaterial(@RequestBody SquarePlateSearchReq req) {
        IPage<SquarePlateSearchVo> page = squareMaterialService.searchSquarePlates(req);
        return DefaultHttpActionPageResult
            .successWithPageData(page.getCurrent(), page.getSize(), page.getTotal(), page.getRecords());
    }

}
