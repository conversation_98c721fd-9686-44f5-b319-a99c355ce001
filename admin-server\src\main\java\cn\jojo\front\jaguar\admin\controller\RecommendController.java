package cn.jojo.front.jaguar.admin.controller;

import cn.jojo.cc.api.domain.request.CourseSearchReq;
import cn.jojo.front.jaguar.biz.service.CourseBizService;
import cn.jojo.front.jaguar.common.enums.RecommendType;
import cn.jojo.front.jaguar.common.pojo.bo.EmployeeBo;
import cn.jojo.front.jaguar.common.pojo.recommend.Recommend;
import cn.jojo.front.jaguar.common.pojo.req.PriorityUpdateReq;
import cn.jojo.front.jaguar.common.pojo.req.RecommendListReq;
import cn.jojo.front.jaguar.common.pojo.req.RecommendSaveReq;
import cn.jojo.front.jaguar.common.pojo.vo.*;
import cn.jojo.front.jaguar.core.service.recommend.RecommendService;
import cn.jojo.infra.sdk.api.metadata.DefaultHttpResult;
import cn.jojo.infra.sdk.api.metadata.DefaultPageResp;
import cn.jojo.infra.sdk.api.metadata.IHttpResult;
import cn.jojo.infra.sdk.api.metadata.IPageResp;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;

import java.util.Collections;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.constraints.NotNull;

import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2022/03/15
 **/
@RestController
@RequestMapping("/admin/recommend")
@Tag(name = "Jaguar管理后台")
@Validated
public class RecommendController extends BaseController {

    @Resource
    private RecommendService recommendService;

    @Resource
    private CourseBizService courseBizService;

    @GetMapping("/getRecommendList")
    @Operation(summary ="recommend列表",   description = "获取recommend列表")
    public IPageResp<RecommendListVo> getRecommendList(RecommendListReq<Void> req) {
        IPage<RecommendListVo> page = recommendService.getRecommendPage(req);
        return DefaultPageResp.buildPageResp(req, page.getTotal(), page.getRecords());
    }

    @PostMapping("/updateRecommendPriority")
    @Operation(summary ="recommend优先级",   description = "更新recommend优先级")
    public IHttpResult<Boolean> updateRecommendPriority(@RequestBody @Validated PriorityUpdateReq req) {
        recommendService.updatePriority(req);
        return DefaultHttpResult.successWithData(true);
    }

    @GetMapping("/getRecommendInfo")
    @Operation(summary ="recommend详情",   description = "获取recommend详情")
    public IHttpResult<RecommendVo> getRecommendInfo(@RequestParam("recommendId") @NotNull Integer recommendId) {
        List<RecommendVo> recommendVos = recommendService
            .listRecommendDetailByIds(Collections.singletonList(recommendId));
        return DefaultHttpResult.successWithData(CollectionUtils.isEmpty(recommendVos) ? null : recommendVos.get(0));
    }

    @PostMapping("/saveRecommend")
    @Operation(summary ="recommend保存",   description = "保存recommend")
    public IHttpResult<Integer> saveRecommend(@RequestBody @Validated RecommendSaveReq req) {
        Recommend oldRecommend = recommendService.findById(req.getId());
        Recommend recommend = recommendService.saveOrUpdateRecommend(req, new EmployeeBo()
            .setEmployeeId(getEmployeeId())
            .setEmployeeName(getEmployeeName()));
        recommendService.deleteCache(Collections.singleton(recommend));
        //若修改了计划类型，需删除修改之前的缓存
        if (oldRecommend != null && req.getRecommendCourseType() != null && !req.getRecommendCourseType()
            .equals(oldRecommend.getRecommendCourseType())) {
            recommendService.deleteCache(Collections.singleton(oldRecommend));
        }
        return DefaultHttpResult.successWithData(recommend.getId());
    }

    @Operation(summary ="删除recommend",   description = "删除recommend")
    @GetMapping("/deleteRecommend")
    public IHttpResult<Boolean> deleteRecommend(@RequestParam @NotNull Integer recommendId) {
        Recommend recommend = recommendService.deleteRecommend(recommendId, new EmployeeBo()
            .setEmployeeId(getEmployeeId())
            .setEmployeeName(getEmployeeName()));
        recommendService.deleteCache(Collections.singleton(recommend));
        return DefaultHttpResult.successWithData(true);
    }

    @Operation(summary ="获取推荐类型",   description = "获取推荐类型")
    @GetMapping("/getRecommendTypeList")
    public IHttpResult<List<RecommendTypeVo>> getRecommendTypeList() {
        return DefaultHttpResult.successWithData(recommendService.getRecommendTypeList());
    }

    @Operation(summary ="获取课程列表",   description = "获取课程列表")
    @GetMapping("/getCourseList")
    public IHttpResult<List<CourseVo>> getCourseList(@RequestParam @NotNull RecommendType recommendType) {
        CourseSearchReq courseSearchReq = new CourseSearchReq();
        courseSearchReq.setCourseType(recommendType.getTargetCourseType());
        courseSearchReq.setSubjectType(recommendType.getTargetSubjectType());
        return DefaultHttpResult.successWithData(courseBizService.searchCourse(courseSearchReq));
    }

    @Operation(summary ="获取推荐页面信息",   description = "获取推荐页面信息")
    @GetMapping("/getRecommendPageInfo")
    public IHttpResult<List<RecommendPageInfoVo>> getRecommendPageInfoList() {
        return DefaultHttpResult.successWithData(recommendService.getRecommendPageInfoList());
    }

}
