package cn.jojo.front.jaguar.admin.controller;

import cn.jojo.common.api.ex.DefaultHttpActionResult;
import cn.jojo.common.api.ex.IHttpActionResult;
import cn.jojo.front.jaguar.common.pojo.bo.PageBo;
import cn.jojo.front.jaguar.common.pojo.req.ReferenceConditionItemReq;
import cn.jojo.front.jaguar.common.pojo.req.ReferenceConditionItemSaveReq;
import cn.jojo.front.jaguar.common.pojo.vo.ReferenceConditionItemVo;
import cn.jojo.front.jaguar.core.service.impl.referencecondition.ReferenceConditionItemService;
import cn.jojo.infra.sdk.api.metadata.DefaultPageResp;
import cn.jojo.infra.sdk.api.metadata.IPageResp;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * 2025/5/22 14:45
 */
@Validated
@RequestMapping("/admin")
@RestController
@Tag(name = "Jaguar管理后台")
public class ReferenceConditionItemController {
    @Resource
    private ReferenceConditionItemService referenceConditionItemService;

    @GetMapping("/reference-conditions/{referenceConditionId}/items")
    @Operation(summary = "分页查询引用规则下子项配置信息列表", description = "通过分页参数以及过滤条件查询引用规则下子项配置信息列表")
    public IPageResp<ReferenceConditionItemVo> page(ReferenceConditionItemReq req,
                                                    @NotNull @PathVariable Long referenceConditionId) {
        PageBo<ReferenceConditionItemVo> page = referenceConditionItemService.list(req);
        return DefaultPageResp.buildPageResp(page.getPageNum(), page.getPageSize(), page.getTotal(), page.getData());
    }

    @PostMapping("/reference-conditions/{referenceConditionId}/items")
    @Operation(summary = "保存引用规则下子项配置信息", description = "保存引用规则下子项配置信息")
    public IHttpActionResult<Void> save(@Validated @RequestBody ReferenceConditionItemSaveReq req,
                                        @NotNull @PathVariable Long referenceConditionId) {
        referenceConditionItemService.save(req);
        return DefaultHttpActionResult.successWithoutData();
    }

    @PatchMapping("/reference-conditions/{referenceConditionId}/items/{id}")
    @Operation(summary = "更新引用规则下子项配置信息", description = "更新引用规则下子项配置信息")
    public IHttpActionResult<Void> update(@NotNull @PathVariable Long id,
                                          @NotNull @PathVariable Long referenceConditionId) {
        referenceConditionItemService.delete(id);
        return DefaultHttpActionResult.successWithoutData();
    }
}
