<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>cn.jojo.front.jaguar</groupId>
        <artifactId>front-jaguar</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>adaptor</artifactId>
    <version>${revision}</version>
    <dependencies>
        <!--本项目业务依赖 -->
        <dependency>
            <groupId>cn.jojo.front.jaguar</groupId>
            <artifactId>common</artifactId>
        </dependency>

        <!--二方依赖 -->
        <dependency>
            <groupId>cn.jojo.infra.sdk</groupId>
            <artifactId>microservice-sdk-springboot</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.jojo.infra</groupId>
            <artifactId>basic-rpc-api</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.jojo.share.cc</groupId>
            <artifactId>rpc-api</artifactId>
            <version>${course-center.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>api-sdk</artifactId>
                    <groupId>cn.jojo.infra.sdk</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>jackson-core</artifactId>
                    <groupId>com.fasterxml.jackson.core</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>cn.jojo.mall.boom</groupId>
            <artifactId>rpc-api</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.tinman.sharedservices</groupId>
            <artifactId>oa-gateway-client-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.jojo.edu.malacca</groupId>
            <artifactId>rpc-api</artifactId>
            <version>${edu-malacca-rpc-api.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>api-sdk</artifactId>
                    <groupId>cn.jojo.infra.sdk</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>cn.jojo.crm</groupId>
            <artifactId>rpc-api</artifactId>
            <version>${crm-rpc-api.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>api-sdk</artifactId>
                    <groupId>cn.jojo.infra.sdk</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>cn.jojo.dayu</groupId>
            <artifactId>rpc-api</artifactId>
            <version>${fb-dayu-rpc.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>api-sdk</artifactId>
                    <groupId>cn.jojo.infra.sdk</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>cn.jojo.vulcan.nuwa</groupId>
            <artifactId>vulcan-nuwa-rpc-api</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.tinman.sharedservices</groupId>
            <artifactId>mars-api</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.tinman.clouds.meta</groupId>
            <artifactId>rpc-api</artifactId>
        </dependency>

        <!--三方依赖 -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <artifactId>mockito-core</artifactId>
                    <groupId>org.mockito</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>cn.jojo.infra.sdk</groupId>
            <artifactId>microservice-sdk-test</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>jackson-core</artifactId>
                    <groupId>com.fasterxml.jackson.core</groupId>
                </exclusion>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-databind</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>cn.jojo.share.uc</groupId>
            <artifactId>rpc-api</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.hibernate</groupId>
                    <artifactId>hibernate-validator</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>api-sdk</artifactId>
                    <groupId>cn.jojo.infra.sdk</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>cn.jojo.edu.fantasy</groupId>
            <artifactId>rpc-api</artifactId>
            <version>${edu-fantasy-rpc-api.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>cn.jojo.infra.sdk</groupId>
                    <artifactId>microservice-sdk-springboot</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>cn.jojo.pagani</groupId>
            <artifactId>rpc-api</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.jojo.fb.tag</groupId>
            <artifactId>rpc-api</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.tinman.sharedservices</groupId>
            <artifactId>cashback-api</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>api-sdk</artifactId>
                    <groupId>cn.jojo.infra.sdk</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>cn.jojo.edu.study</groupId>
            <artifactId>rpc-api</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.jojo.edu.wings</groupId>
            <artifactId>rpc-api</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.jojo.dourm</groupId>
            <artifactId>rpc-api</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.jojo.tag</groupId>
            <artifactId>rpc-api</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.jojo.survey</groupId>
            <artifactId>rpc-api</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.jojo.follower</groupId>
            <artifactId>rpc-api</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.tinman.sharedservices</groupId>
            <artifactId>channel-api</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.tinman.sharedservices</groupId>
            <artifactId>product-api</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.tinman.clouds</groupId>
            <artifactId>hermes-rpc-api</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.tinman.sharedservices</groupId>
            <artifactId>order-api</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>
            <!-- 要将源码放上去，需要加入这个插件 -->
            <plugin>
                <artifactId>maven-source-plugin</artifactId>
                <version>3.0.1</version>
                <configuration>
                    <attach>true</attach>
                </configuration>
                <executions>
                    <execution>
                        <phase>compile</phase>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
