package cn.jojo.front.jaguar.admin.controller;

import cn.jojo.common.api.ex.DefaultHttpActionPageResult;
import cn.jojo.common.api.ex.DefaultHttpActionResult;
import cn.jojo.common.api.ex.IHttpActionResult;
import cn.jojo.front.jaguar.common.pojo.SquarePlateListReq;
import cn.jojo.front.jaguar.common.pojo.bo.ThemeColorResultBo;
import cn.jojo.front.jaguar.common.pojo.req.PriorityUpdateReq;
import cn.jojo.front.jaguar.common.pojo.req.SquareConfigurationSaveReq;
import cn.jojo.front.jaguar.common.pojo.vo.SquareConfigurationInfoVo;
import cn.jojo.front.jaguar.common.pojo.vo.SquareConfigurationListVo;
import cn.jojo.front.jaguar.core.service.square.SquareConfigurationService;
import cn.jojo.infra.sdk.api.metadata.IPageResp;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping(value = "/admin/square/configuration")
@Tag(name = "Jaguar管理后台")
@Validated
public class SquareConfigurationController {

    @Resource
    private SquareConfigurationService squareConfigurationService;

    @GetMapping("/list")
    @Operation(summary ="广场配置列表(已废弃)",   description = "广场配置列表(已迁移至/api/jaguar/admin/square-configurations)")
    public IHttpActionResult<IPageResp<SquareConfigurationListVo>> getSquareConfigurationList(
        @RequestParam("materialId") @NotNull Integer materialId, SquarePlateListReq req) {
        IPage<SquareConfigurationListVo> pageResult = squareConfigurationService
            .listSquareConfiguration(materialId, req);
        return DefaultHttpActionPageResult
            .successWithPageData(pageResult.getCurrent(), pageResult.getSize(), pageResult.getTotal(),
                pageResult.getRecords());
    }

    @GetMapping("/detail")
    @Operation(summary ="广场配置详情(已废弃)",   description = "广场配置详情(已迁移至/api/jaguar/admin/square-configurations/{id})")
    public IHttpActionResult<SquareConfigurationInfoVo> getSquareConfigurationDetail(@RequestParam("id") @NotNull Integer id) {
        SquareConfigurationInfoVo result = squareConfigurationService
            .listSquareConfigurationDetailByIds(Collections.singleton(id)).stream().findFirst().orElse(null);
        return DefaultHttpActionResult.successWithData(result);
    }

    @GetMapping("/theme")
    @Operation(summary ="广场配置主题",   description = "广场配置主题")
    public IHttpActionResult<List<ThemeColorResultBo>> getThemeColorResult() {
        return DefaultHttpActionResult.successWithData(squareConfigurationService.getThemeColorResult());
    }

    @PostMapping("/update")
    @Operation(summary = "更新或保存广场配置信息(已废弃)", method = "POST", description = "更新或保存广场配置信息(已迁移至/api/jaguar/admin/square-configurations)")
    public IHttpActionResult<Boolean> saveOrUpdate(@RequestBody @Validated SquareConfigurationSaveReq req) {
        squareConfigurationService.saveSquareConfiguration(req);
        return DefaultHttpActionResult.successWithData(true);
    }

    @GetMapping("/updatePriority")
    @Operation(summary ="更新或保存广场配置优先级",   description = "更新或保存广场配置优先级")
    public IHttpActionResult<Boolean> updatePriority(@Validated PriorityUpdateReq req) {
        squareConfigurationService.updatePriority(req);
        return DefaultHttpActionResult.successWithData(true);
    }

    @GetMapping("/delete")
    @Operation(summary ="删除广场配置",   description = "删除广场配置")
    public IHttpActionResult<Boolean> deleteSquareConfiguration(@RequestParam("id") @NotNull Integer id) {
        squareConfigurationService.deleteSquareConfiguration(id);
        return DefaultHttpActionResult.successWithData(true);
    }
}
