package cn.jojo.front.jaguar.adaptor.service;

import cn.jojo.front.jaguar.common.bo.courseextension.CourseSegmentExtensionBo;
import cn.jojo.front.jaguar.common.pojo.req.CourseSegmentExtensionQueryRpcReq;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2023/11/3 10:33
 * @desc
 */
public interface ICourseExtensionRpcClient {

    /**
     * 查询课堂延展信息
     *
     * @param rpcReq
     * @return
     */
    Optional<CourseSegmentExtensionBo> queryCourseSegmentExtension(CourseSegmentExtensionQueryRpcReq rpcReq);

}
