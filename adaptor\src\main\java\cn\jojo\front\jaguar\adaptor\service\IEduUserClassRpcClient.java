package cn.jojo.front.jaguar.adaptor.service;

import cn.jojo.edu.malacca.rpc.api.dto.EduUserClassDto;
import cn.jojo.edu.malacca.rpc.api.dto.EduUserClassExtensionDto;
import cn.jojo.front.jaguar.common.pojo.bo.EduUserClassBo;
import cn.jojo.front.jaguar.common.pojo.req.QueryUserClassReq;
import cn.jojo.front.jaguar.common.pojo.vo.eduUserClass.EduUserClassExtensionVo;

import java.util.List;

public interface IEduUserClassRpcClient {

    List<EduUserClassExtensionVo> queryExtensionByParam(Long userId, Integer pageNum, Integer pageSize);

    List<EduUserClassExtensionDto> queryByOrderId(Long userId, String orderId);

    EduUserClassDto queryByClassId(Long userId, Long classId);

    List<EduUserClassBo>  queryUserClasses(QueryUserClassReq req);
}
