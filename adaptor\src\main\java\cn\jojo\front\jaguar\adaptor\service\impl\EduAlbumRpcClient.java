package cn.jojo.front.jaguar.adaptor.service.impl;


import cn.hutool.core.text.CharSequenceUtil;
import cn.jojo.edu.fantasy.common.dict.AlbumStatus;
import cn.jojo.edu.fantasy.rpc.api.dto.EduAlbumAggregationDto;
import cn.jojo.edu.fantasy.rpc.api.dto.EduAlbumContentUpdateDto;
import cn.jojo.edu.fantasy.rpc.api.dto.EduAlbumPagingDto;
import cn.jojo.edu.fantasy.rpc.api.dto.EduResourcesAlbumDto;
import cn.jojo.edu.fantasy.rpc.api.req.EduAlbumExtensionReq;
import cn.jojo.edu.fantasy.rpc.api.req.EduAlbumPagingReq;
import cn.jojo.edu.fantasy.rpc.api.req.EduAlbumQueryReq;
import cn.jojo.edu.fantasy.rpc.api.req.EduContentUpdateAlbumsReq;
import cn.jojo.edu.fantasy.rpc.api.req.EduResourcesAlbumReq;
import cn.jojo.edu.fantasy.rpc.api.req.enums.EduAlbumAggregationType;
import cn.jojo.edu.fantasy.rpc.api.service.IEduAlbumRpcService;
import cn.jojo.front.jaguar.adaptor.service.IEduAlbumRpcClient;
import cn.jojo.front.jaguar.common.pojo.bo.AlbumBo;
import cn.jojo.front.jaguar.common.pojo.bo.EduAlbumBo;
import cn.jojo.front.jaguar.common.pojo.bo.EduResourcesAlbumBo;
import cn.jojo.front.jaguar.common.pojo.req.ListResourcesAlbumRpcReq;
import cn.jojo.infra.sdk.api.metadata.IPageResp;
import cn.jojo.infra.sdk.api.metadata.IRpcResult;
import cn.jojo.infra.sdk.logging.JoJoLogging;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/03/24
 **/
@Component
@Slf4j
public class EduAlbumRpcClient implements IEduAlbumRpcClient {

    @Reference
    private IEduAlbumRpcService eduAlbumRpcService;
    @Value("${album.info.max.size:10}")
    private Integer albumInfoMaxSize;
    @Value("${album.id.max.size:50}")
    private Integer albumIdMaxSize;

    @Override
    public List<EduAlbumAggregationDto> queryAggregation(
        List<Long> albumIds, List<Integer> typeList, Boolean containsInvalid) {
        if (CollectionUtils.isEmpty(albumIds)) {
            return Lists.newArrayList();
        }
        EduAlbumExtensionReq req = EduAlbumExtensionReq.builder()
            .queryReq(EduAlbumQueryReq.builder().albumIds(albumIds).containsInvalid(containsInvalid).build())
            .build();
        if (!CollectionUtils.isEmpty(typeList)) {
            req.setAggregationTypes(typeList.stream().map(EduAlbumAggregationType::get).collect(Collectors.toList()));
        }
        return queryAggregation(req);
    }

    /**
     * 调用教务查询资源聚合信息
     *
     * @param req 教务rpc接口入参
     * @return
     */
    private List<EduAlbumAggregationDto> queryAggregation(EduAlbumExtensionReq req) {
        IRpcResult<List<EduAlbumAggregationDto>> rpcResult;
        try {
            rpcResult = eduAlbumRpcService.queryAggregation(req);
            if (!rpcResult.checkSuccess()) {
                JoJoLogging.logger(log).error("eduAlbumRpcService.queryAggregation failed,req:{},message:{}", req,
                        rpcResult.getMessage());
                return Collections.emptyList();
            }
            return rpcResult.getData();
        } catch (Exception e) {
            JoJoLogging.logger(log).error("eduAlbumRpcService.queryAggregation failed", e);
            return Collections.emptyList();
        }
    }

    /**
     * 获取更新过的专辑
     *
     * @param albumIds 批量专辑ID
     * @return 有过更新的专辑
     */
    @Override
    public List<EduAlbumContentUpdateDto> getContentUpdateAlbums(List<Long> albumIds) {
        IRpcResult<List<EduAlbumContentUpdateDto>> rpcResult;
        try {
            EduContentUpdateAlbumsReq req = EduContentUpdateAlbumsReq.builder()
                .albumIds(albumIds)
                .build();
            rpcResult = eduAlbumRpcService.getContentUpdateAlbums(req);
            if (!rpcResult.checkSuccess()) {
                JoJoLogging.logger(log).error("eduAlbumRpcService.getContentUpdateAlbums failed,req:{},message:{}", req,
                    rpcResult.getMessage());
                return Collections.emptyList();
            }
            return rpcResult.getData();
        } catch (Exception e) {
            JoJoLogging.logger(log).error("eduAlbumRpcService.getContentUpdateAlbums failed", e);
            return Collections.emptyList();
        }
    }

    @Override
    public Page<AlbumBo> getAlbumsByFuzzyQuery(String contentType, Long hubId, Long albumId, String albumName,
                                               Integer pageNum, Integer pageSize) {
        if (CharSequenceUtil.isEmpty(contentType)) {
            return new Page<>();
        }
        EduAlbumPagingReq req = EduAlbumPagingReq.builder()
            .hubIds(Objects.isNull(hubId) ? Collections.emptyList() : Collections.singletonList(hubId))
            .albumIds(Objects.isNull(albumId) ? Collections.emptyList() : Collections.singletonList(albumId))
            .albumName(albumName)
            .fuzzyQuery(CharSequenceUtil.isNotBlank(albumName) ? 1 : 0)
            .pageSize(pageSize)
            .status(AlbumStatus.ACTIVATED.getStatus())
            .pageNum(pageNum)
            .contentType(contentType).build();
        return getAlbumBoPages(req);
    }

    @Override
    public List<EduAlbumBo> getAlbumsByIds(List<Long> albumIds) {
        if (CollectionUtils.isEmpty(albumIds)) {
            return Collections.emptyList();
        }
        return Lists.partition(albumIds, albumInfoMaxSize).stream().map(ids -> {
            try {
                IRpcResult<IPageResp<EduAlbumPagingDto>> result =
                    eduAlbumRpcService.queryAlbumsByPage(EduAlbumPagingReq.builder()
                        .albumIds(ids)
                        .build());
                if (!result.checkSuccess()) {
                    JoJoLogging.logger(log)
                        .error("eduAlbumRpcService.getAlbumById failed,req:{},message:{}", albumIds,
                            result.getMessage());
                    return Collections.<EduAlbumBo>emptyList();
                }
                return result.getData().getPageRecords().stream().filter(Objects::nonNull).map(data -> {
                    EduAlbumBo bo = new EduAlbumBo();
                    BeanUtils.copyProperties(data, bo);
                    return bo;
                }).collect(Collectors.toList());
            } catch (Exception e) {
                JoJoLogging.logger(log).error("eduAlbumRpcService.getAlbumsByIds failed", e);
                return Collections.<EduAlbumBo>emptyList();
            }
        }).flatMap(List::stream).collect(Collectors.toList());
    }

    @Override
    public List<EduResourcesAlbumBo> listResourcesAlbums(ListResourcesAlbumRpcReq rpcReq) {
        if (CollectionUtils.isEmpty(rpcReq.getAlbumIds())) {
            return Collections.emptyList();
        }

        return Lists.partition(rpcReq.getAlbumIds(), albumIdMaxSize).stream()
            .map(aIds -> {
                IRpcResult<List<EduResourcesAlbumDto>> rpcResult;
                try {
                    rpcResult = eduAlbumRpcService.queryResourcesAlbums(EduResourcesAlbumReq.builder()
                        .albumIds(aIds)
                        .resIds(rpcReq.getResIds())
                        .build());
                    if (!rpcResult.checkSuccess()) {
                        JoJoLogging.logger(log)
                            .error("eduAlbumRpcService.listResourcesAlbums failed,req:{},message:{}", rpcReq,
                                rpcResult.getMessage());
                        return Collections.emptyList();
                    }
                    return rpcResult.getData();
                } catch (Exception e) {
                    JoJoLogging.logger(log).error("eduAlbumRpcService.listResourcesAlbums failed", e);
                    return Collections.emptyList();
                }
            })
            .flatMap(Collection::stream)
            .map(dto -> {
                EduResourcesAlbumBo bo = new EduResourcesAlbumBo();
                BeanUtils.copyProperties(dto, bo);
                return bo;
            })
            .collect(Collectors.toList());    }

    /**
     * 获取专辑基础信息
     *
     * @param req 专辑请求
     * @return Page<AlbumBo>
     */
    private Page<AlbumBo> getAlbumBoPages(EduAlbumPagingReq req) {
        IRpcResult<IPageResp<EduAlbumPagingDto>> albumsRpcResult = eduAlbumRpcService.queryAlbumsByPage(req);
        if (!albumsRpcResult.checkSuccess()) {
            log.error("invoke eduAlbumRpcService.queryAlbumsByPage failed, messages={}", albumsRpcResult.getMessage());
            return new Page<>();
        }
        Optional<IPageResp<EduAlbumPagingDto>> pageResult = Optional.ofNullable(albumsRpcResult.getData());
        return new Page<AlbumBo>()
            .setRecords(pageResult
                .map(IPageResp::getPageRecords).orElse(Collections.emptyList())
                .stream()
                .map(item -> {
                    AlbumBo bo = new AlbumBo();
                    BeanUtils.copyProperties(item, bo);
                    bo.setAlbumId(item.getId());
                    return bo;
                }).collect(Collectors.toList()))
            .setTotal(pageResult.map(IPageResp::getTotalCount).orElse(0L))
            .setCurrent(pageResult.map(IPageResp::getPageNum).orElse(0L))
            .setSize(pageResult.map(IPageResp::getPageSize).orElse(0L));
    }
}
