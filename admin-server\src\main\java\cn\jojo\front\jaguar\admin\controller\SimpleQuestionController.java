package cn.jojo.front.jaguar.admin.controller;

import cn.jojo.common.api.ex.DefaultHttpActionPageResult;
import cn.jojo.common.api.ex.DefaultHttpActionResult;
import cn.jojo.common.api.ex.IHttpActionResult;
import cn.jojo.front.jaguar.common.pojo.bo.PageBo;
import cn.jojo.front.jaguar.common.pojo.req.SimpleQuestionListReq;
import cn.jojo.front.jaguar.common.pojo.req.SimpleQuestionSaveReq;
import cn.jojo.front.jaguar.common.pojo.vo.SimpleQuestionDetailVo;
import cn.jojo.front.jaguar.common.pojo.vo.SimpleQuestionListVo;
import cn.jojo.front.jaguar.common.pojo.vo.SimpleQuestionSubjectCategoryVo;
import cn.jojo.front.jaguar.core.service.question.SimpleQuestionCategoryService;
import cn.jojo.front.jaguar.core.service.question.SimpleQuestionService;
import cn.jojo.infra.sdk.api.metadata.IPageResp;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping(value = "admin/simple-question")
@Tag(name = "Jaguar管理后台")
public class SimpleQuestionController extends BaseController {

    @Resource
    private SimpleQuestionService simpleQuestionService;
    @Resource
    private SimpleQuestionCategoryService simpleQuestionCategoryService;

    @GetMapping("/list")
    @Operation(summary ="题目列表",   description = "题目列表")
    public IHttpActionResult<IPageResp<SimpleQuestionListVo>> listQuestions(@Validated SimpleQuestionListReq req) {
        PageBo<SimpleQuestionListVo> pageResult = simpleQuestionService.listQuestions(req);
        return DefaultHttpActionPageResult.successWithPageData(pageResult.getPageNum(), pageResult.getPageSize(),
                pageResult.getTotal(), pageResult.getData());
    }

    /**
     * @See cn.jojo.front.jaguar.admin.controller.SimpleQuestionControllerV2
     */
    @GetMapping("/detail")
    @Operation(summary = "获取题目详情", description = "通过题目id获取题目详情。该接口已弃用，迁移至/api/jaguar/admin/questions/{id}")
    public IHttpActionResult<SimpleQuestionDetailVo> getQuestionDetail(@NotNull(message = "id is null") Long id) {
        SimpleQuestionDetailVo simpleQuestionListVo = simpleQuestionService.getSimpleQuestionDetail(id);
        return DefaultHttpActionResult.successWithData(simpleQuestionListVo);
    }

    /**
     * @See cn.jojo.front.jaguar.admin.controller.SimpleQuestionControllerV2
     */
    @PostMapping("/save")
    @Operation(summary = "保存题目", description = "保存题目详情。该接口已弃用，迁移至/api/jaguar/admin/questions")
    public IHttpActionResult<Boolean> save(@Validated @RequestBody SimpleQuestionSaveReq body) {
        boolean result = simpleQuestionService.saveOrUpdate(body, getEmployeeId(), getEmployeeName());
        return DefaultHttpActionResult.successWithData(result);
    }

    @GetMapping("/update-status")
    @Operation(summary ="更新题目参数",   description = "更新题目参数")
    public IHttpActionResult<Boolean> updateStatus(@NotNull(message = "id is null") Long id,
            @NotNull(message = "targetStatus is null") String targetStatus) {
        simpleQuestionService.updateStatus(id, targetStatus, getEmployeeId(), getEmployeeName());
        return DefaultHttpActionResult.successWithData(true);
    }

    @GetMapping("/all-categories")
    @Operation(summary ="获取所有分类",   description = "获取所有分类")
    public IHttpActionResult<SimpleQuestionSubjectCategoryVo> getAllCategories() {
        SimpleQuestionSubjectCategoryVo allCategories = simpleQuestionCategoryService.getAllCategories();
        return DefaultHttpActionResult.successWithData(allCategories);
    }

}
