package cn.jojo.front.jaguar.adaptor.service;

import cn.tinman.sharedservices.mall.mars.api.response.ShortUrlResp;

import java.util.Date;

/**
 * <AUTHOR>
 */
public interface IShortUrlApiClient {
    /**
     * 生成锻炼
     *
     * @param url           源链接
     * @param shortUrlBizId 找营销申请
     * @param groupName     找营销申请
     * @param expireTime    短链过期时间
     * @param tittle        标题
     * @return 短链信息
     */
    ShortUrlResp generateShortUrl(String url, Long shortUrlBizId, String groupName, Date expireTime, String tittle);
}
