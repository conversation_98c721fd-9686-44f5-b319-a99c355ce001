package cn.jojo.front.jaguar.admin.controller;

import cn.jojo.common.api.ex.DefaultHttpActionPageResult;
import cn.jojo.common.api.ex.DefaultHttpActionResult;
import cn.jojo.common.api.ex.IHttpActionResult;
import cn.jojo.front.jaguar.common.pojo.bo.PageBo;
import cn.jojo.front.jaguar.common.pojo.req.SquarePageListReq;
import cn.jojo.front.jaguar.common.pojo.req.SquarePageMoveReq;
import cn.jojo.front.jaguar.common.pojo.req.SquarePageSaveReq;
import cn.jojo.front.jaguar.common.pojo.vo.SquarePageListVo;
import cn.jojo.front.jaguar.core.service.square.SquarePageService;
import cn.jojo.infra.sdk.api.metadata.IPageResp;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/admin/square/page")
@Tag(name = "Jaguar管理后台")
public class SquarePageController {

    @Resource
    private SquarePageService squarePageService;

    @GetMapping("/list")
    @Operation(summary ="广场页面列表",   description = "广场页面列表")
    public IHttpActionResult<IPageResp<SquarePageListVo>> list(@Validated SquarePageListReq req) {
        PageBo<SquarePageListVo> page = squarePageService.listSquarePage(req);
        return DefaultHttpActionPageResult
                .successWithPageData(page.getPageNum(), page.getPageSize(), page.getTotal(), page.getData());
    }

    @PostMapping("/save")
    @Operation(summary ="广场页面保存",   description = "广场页面保存")
    public IHttpActionResult<Boolean> save(@Validated @RequestBody SquarePageSaveReq req) {
        squarePageService.saveOrUpdate(req);
        return DefaultHttpActionResult.successWithData(true);
    }


    @GetMapping("/move")
    @Operation(summary ="移动广场页面",   description = "移动广场页面")
    public IHttpActionResult<Boolean> moveSquareMaterial(SquarePageMoveReq req) {
        req.setPageId(req.getPageId() == null ? 1L : req.getPageId());
        return DefaultHttpActionResult.successWithData(squarePageService.move(req));
    }
}
