package cn.jojo.front.jaguar.adaptor.service;


import java.util.List;

import cn.jojo.front.jaguar.common.pojo.bo.UserFavorBo;
import cn.jojo.front.jaguar.common.pojo.req.UserFavorCancelReq;
import cn.jojo.front.jaguar.common.pojo.req.UserFavorConfirmReq;
import cn.jojo.front.jaguar.common.pojo.req.UserFavorPageReq;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

/**
 * @Description: 收藏相关rpc接口
 * @Copyright: Copyright (c) 2022  ALL RIGHTS RESERVED.
 * @Company: 书声科技有限公司
 * @Author: chenHao
 * @CreateDate: 2022/10/11 18:48
 * @UpdateDate: 2022/10/11 18:48
 * @UpdateRemark: init
 * @Version: 1.0
 */
public interface IEduUserFavorRpcClient {

    /**
     * 收藏
     *
     * @param req 用户对资源/专辑的收藏请求
     */
    void confirm(UserFavorConfirmReq req);

    /**
     * 取消收藏
     *
     * @param req 用户对资源/专辑的取消收藏请求
     */
    void cancel(UserFavorCancelReq req);

    /**
     * 分页查询收藏信息（默认有效）
     *
     * @param req 用户对资源/专辑的分页收藏查询请求
     * @return 用户收藏业务数据
     */
    Page<UserFavorBo> queryByPage(UserFavorPageReq req);

    /**
     * 分页查询收藏信息（默认有效）
     *
     * @param req 用户对资源/专辑的分页收藏查询请求
     * @return 用户收藏业务数据
     */
    List<UserFavorBo> queryByList(UserFavorPageReq req);
}
