package cn.jojo.front.jaguar.admin.controller;

import cn.jojo.front.jaguar.common.annotation.Av1Transform;
import cn.jojo.front.jaguar.common.pojo.bo.EmployeeBo;
import cn.jojo.front.jaguar.common.pojo.entity.contentconfiguration.ContentConfiguration;
import cn.jojo.front.jaguar.common.pojo.req.AlbumPageReq;
import cn.jojo.front.jaguar.common.pojo.req.ContentConfigurationListReq;
import cn.jojo.front.jaguar.common.pojo.req.ContentConfigurationPriorityUpdateReq;
import cn.jojo.front.jaguar.common.pojo.req.ContentConfigurationSaveReq;
import cn.jojo.front.jaguar.common.pojo.req.HubPageReq;
import cn.jojo.front.jaguar.common.pojo.req.RankingCardPageReq;
import cn.jojo.front.jaguar.common.pojo.req.RankingCardSaveReq;
import cn.jojo.front.jaguar.common.pojo.req.ResPageReq;
import cn.jojo.front.jaguar.common.pojo.vo.AlbumPageVo;
import cn.jojo.front.jaguar.common.pojo.vo.ContentConfigurationDetailVo;
import cn.jojo.front.jaguar.common.pojo.vo.ContentConfigurationListVo;
import cn.jojo.front.jaguar.common.pojo.vo.HubPageVo;
import cn.jojo.front.jaguar.common.pojo.vo.HubResContentTypeVo;
import cn.jojo.front.jaguar.common.pojo.vo.HubToAlbumToResCoverVo;
import cn.jojo.front.jaguar.common.pojo.vo.RankingCardVo;
import cn.jojo.front.jaguar.common.pojo.vo.ResPageVo;
import cn.jojo.front.jaguar.core.service.plate.ContentConfigurationService;
import cn.jojo.front.jaguar.core.service.plate.RankingCardService;
import cn.jojo.infra.sdk.api.metadata.DefaultHttpResult;
import cn.jojo.infra.sdk.api.metadata.DefaultPageResp;
import cn.jojo.infra.sdk.api.metadata.IHttpResult;
import cn.jojo.infra.sdk.api.metadata.IPageResp;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import java.util.Collections;
import java.util.List;
import javax.validation.constraints.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * Description: 内容配置接口 date: 2021/4/9 16:38
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/admin/contentConfiguration")
@Tag(name = "Jaguar管理后台")
@Validated
public class ContentConfigurationController extends BaseController {

    @Autowired
    private ContentConfigurationService contentConfigurationService;

    @Autowired
    private RankingCardService rankCardService;

    /**
     * @Description: 查询内容配置列表
     * @author: xr
     * @date: 2021/4/9
     */
    @GetMapping("/getContentConfigurationList")
    @Operation(summary ="内容配置列表",   description = "获取内容配置列表")
    public IPageResp<ContentConfigurationListVo> getContentConfigurationList(ContentConfigurationListReq<Void> req) {
        IPage<ContentConfigurationListVo> page = contentConfigurationService.getContentConfigurationList(req);
        return DefaultPageResp.buildPageResp(req, page.getTotal(), page.getRecords());
    }

    /**
     * @Description: 更改优先级
     * @author: xr
     * @date: 2021/4/9
     */
    @PostMapping("/updateContentConfigurationPriority")
    @Operation(summary ="contentConfiguration优先级",   description = "更新contentConfiguration优先级")
    public IHttpResult<Boolean> updateContentConfigurationPriority(
        @RequestBody @Validated ContentConfigurationPriorityUpdateReq req) {
        ContentConfiguration contentConfiguration = contentConfigurationService.updatePriority(req);
        if (contentConfiguration != null) {
            contentConfigurationService.deleteCache(Collections.singleton(contentConfiguration));
        }

        return DefaultHttpResult.successWithData(true);
    }

    /**
     * @Description: 保存内容配置
     * @author: xr
     * @date: 2021/4/9
     */
    @PostMapping("/saveContentConfiguration")
    @Operation(summary = "contentConfiguration保存", description = "保存contentConfiguration")
    @Av1Transform
    public IHttpResult<Integer> saveContentConfiguration(@RequestBody @Validated ContentConfigurationSaveReq req) {
        if (req.updateStatus()) {
            ContentConfiguration contentConfiguration =  contentConfigurationService.updateStatus(req, new EmployeeBo()
                .setEmployeeId(getEmployeeId())
                .setEmployeeName(getEmployeeName()));
            return DefaultHttpResult.successWithData(contentConfiguration.getId());
        }

        ContentConfiguration contentConfiguration = contentConfigurationService
            .saveContentConfiguration(req, new EmployeeBo()
                .setEmployeeId(getEmployeeId())
                .setEmployeeName(getEmployeeName()));
        contentConfigurationService.deleteCache(Collections.singleton(contentConfiguration));
        return DefaultHttpResult.successWithData(contentConfiguration.getId());
    }

    /**
     * @Description: 获取详情
     * @author: xr
     * @date: 2021/4/9
     */
    @GetMapping("/getContentConfigurationDetailInfo")
    @Operation(summary ="contentConfiguration详情",   description = "获取contentConfiguration详情")
    public IHttpResult<ContentConfigurationDetailVo> getContentConfigurationDetailInfo(
        @RequestParam("contentConfigurationId") @NotNull Integer contentConfigurationId) {
        ContentConfigurationDetailVo contentConfigurationDetailInfo = contentConfigurationService
            .getContentConfigurationDetailInfo(contentConfigurationId);
        return DefaultHttpResult.successWithData(contentConfigurationDetailInfo);
    }

    /**
     * @Description: 删除内容配置
     * @author: xr
     * @date: 2021/4/12
     */
    @PostMapping("/deleteContentConfiguration")
    @Operation(summary ="删除contentConfiguration详情",   description = "删除contentConfiguration详情")
    public IHttpResult<Boolean> deleteContentConfiguration(
        @RequestParam("contentConfigurationId") @NotNull Integer contentConfigurationId) {
        ContentConfiguration contentConfiguration = contentConfigurationService
            .deleteContentConfiguration(contentConfigurationId, new EmployeeBo()
                .setEmployeeId(getEmployeeId())
                .setEmployeeName(getEmployeeName()));
        contentConfigurationService.deleteCache(Collections.singleton(contentConfiguration));
        return DefaultHttpResult.successWithData(true);
    }

    /**
     * 保存内容配置中榜单数据
     *
     * @param req 榜单数据保存对象
     * @return IHttpResult<Long>
     */
    @PostMapping("/saveRankingCard")
    @Operation(summary ="rankingCard数据保存",   description = "保存rankingCard")
    public IHttpResult<Long> saveRankingCard(@RequestBody @Validated RankingCardSaveReq req) {
        return DefaultHttpResult.successWithData(rankCardService.saveOrUpdate(req));
    }

    /**
     * 查询榜单数据资源
     *
     * @param req 普通/兼容分页请求
     * @return IHttpResult<List<RankingCardVo>>
     */
    @GetMapping("/getRankingCardList")
    @Operation(summary ="查询rankingCard列表",   description = "查询rankingCard列表")
    public IHttpResult<List<RankingCardVo>> getRankingCardList(@Validated RankingCardPageReq<Void> req) {
        return DefaultHttpResult.successWithData(rankCardService.getRankingCardList(req));
    }

    /**
     * 模糊查询hub分页接口
     *
     * @param req 分页请求
     * @return IPageResp<HubPageVo>
     */
    @GetMapping("/hubPageList")
    @Operation(summary ="hub查询列表",   description = "获取hub查询列表")
    public IPageResp<HubPageVo> getHubPageList(HubPageReq<Void> req) {
        IPage<HubPageVo> page = contentConfigurationService.getHubPageList(req);
        return DefaultPageResp.buildPageResp(req, page.getTotal(), page.getRecords());
    }

    /**
     * 模糊查询album分页接口
     *
     * @param req 分页请求
     * @return IPageResp<AlbumPageVo>
     */
    @GetMapping("/albumPageList")
    @Operation(summary ="album查询列表",   description = "获取album查询列表")
    public IPageResp<AlbumPageVo> getAlbumPageList(AlbumPageReq<Void> req) {
        IPage<AlbumPageVo> page =
            contentConfigurationService.getAlbumPageList(req);
        return DefaultPageResp.buildPageResp(req, page.getTotal(), page.getRecords());
    }


    /**
     * 模糊查询res分页接口
     *
     * @param req 分页请求
     * @return IPageResp<ResPageVo>
     */
    @GetMapping("/resPageList")
    @Operation(summary ="resource查询列表",   description = "获取res查询列表")
    public IPageResp<ResPageVo> getResPageList(ResPageReq<Void> req) {
        IPage<ResPageVo> page = contentConfigurationService.getResPageList(req);
        return DefaultPageResp.buildPageResp(req, page.getTotal(), page.getRecords());
    }

    /**
     * 反查 更新封面数据
     *
     * @param hubId   专辑库ID
     * @param albumId 专辑ID
     * @param resId   资源ID
     * @return HubToAlbumToResCoverVo
     */
    @GetMapping("/updateResCover")
    @Operation(summary ="更新封面数据",   description = "更新封面数据")
    public IHttpResult<HubToAlbumToResCoverVo> updateResCover(@RequestParam(value = "hubId") @NotNull Long hubId,
                                                              @RequestParam(value = "albumId", required = false)
                                                                  Long albumId,
                                                              @RequestParam(value = "resId", required = false)
                                                                  Long resId) {
        return DefaultHttpResult.successWithData(contentConfigurationService.updateResCover(hubId, albumId, resId));
    }

    /**
     * hub分页类型下拉
     *
     * @return IHttpResult<List<String>>
     */
    @GetMapping("/contentType")
    @Operation(summary ="查询hub分页类型下拉",   description = "查询hub专辑数据类型")
    public IHttpResult<List<HubResContentTypeVo>> getHubContentTypeList(
        @RequestParam(value = "sceneType", required = false) Integer sceneType) {
        return DefaultHttpResult.successWithData(contentConfigurationService.getHubContentTypeList(sceneType));
    }
}
