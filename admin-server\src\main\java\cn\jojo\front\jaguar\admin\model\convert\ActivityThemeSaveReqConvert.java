package cn.jojo.front.jaguar.admin.model.convert;

import cn.jojo.edu.common.utils.convert.BaseModelConvert;
import cn.jojo.front.jaguar.admin.model.req.activity.ActivityThemeSaveReq;
import cn.jojo.front.jaguar.common.pojo.vo.activity.ActivityThemeOperateVo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface ActivityThemeSaveReqConvert extends BaseModelConvert<ActivityThemeSaveReq, ActivityThemeOperateVo> {

    ActivityThemeSaveReqConvert INSTANCE = Mappers.getMapper(ActivityThemeSaveReqConvert.class);
}
