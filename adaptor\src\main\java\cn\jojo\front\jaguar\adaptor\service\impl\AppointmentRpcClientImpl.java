package cn.jojo.front.jaguar.adaptor.service.impl;

import cn.jojo.front.jaguar.adaptor.service.AppointmentRpcClient;
import cn.jojo.infra.sdk.api.metadata.IRpcResult;
import cn.jojo.survey.user.AppointmentRpcApi;
import cn.jojo.survey.user.request.QueryAppointmentRequest;
import cn.jojo.survey.user.response.QueryAppointmentResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Slf4j
@Component
public class AppointmentRpcClientImpl implements AppointmentRpcClient {

    @DubboReference
    private AppointmentRpcApi appointmentRpcApi;

    @Override
    public QueryAppointmentResponse queryAppointmentTemplate(Long classId) {
        if (Objects.isNull(classId)) {
            return new QueryAppointmentResponse();
        }
        QueryAppointmentRequest request = new QueryAppointmentRequest();
        request.setClassId(classId);
        IRpcResult<QueryAppointmentResponse> rpcResult = null;
        try {
            rpcResult = appointmentRpcApi.queryAppointmentTemplate(request);
            if (Objects.isNull(rpcResult) || !rpcResult.checkSuccess()) {
                return new QueryAppointmentResponse();
            }
        } catch (Exception e) {
            log.error("query appointment template error", e);
            return new QueryAppointmentResponse();
        }

        return rpcResult.getData();
    }
}
