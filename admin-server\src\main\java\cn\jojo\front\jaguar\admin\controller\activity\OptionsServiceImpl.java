package cn.jojo.front.jaguar.admin.controller.activity;

import cn.hutool.core.util.ObjectUtil;
import cn.jojo.front.jaguar.common.enums.option.BaseOptionsInterface;
import cn.jojo.front.jaguar.common.enums.option.JoJoOptions;
import cn.jojo.front.jaguar.common.enums.option.OptionsBo;
import cn.jojo.front.jaguar.common.pojo.vo.activity.DictionaryAdminVo;
import cn.jojo.front.jaguar.common.pojo.vo.activity.DictionaryVo;
import cn.jojo.front.jaguar.common.pojo.vo.activity.QueryOptionsVo;
import com.google.common.collect.Lists;
import java.lang.reflect.Field;
import java.util.List;
import java.util.stream.Collectors;
import org.springframework.stereotype.Service;
import org.springframework.util.ReflectionUtils;

/**
 * @description: 下拉枚举公共查询接口实现
 * @author: wangzhilei
 * @date: 2022/12/8
 **/
@Service
public class OptionsServiceImpl {

    public DictionaryAdminVo queryOptions(QueryOptionsVo queryOptionsVo)
        throws IllegalAccessException {

        Field[] fields = queryOptionsVo.getClass().getDeclaredFields();

        List<DictionaryVo> dictionaryVos = Lists.newArrayList();
        for (Field filed : fields) {
            ReflectionUtils.makeAccessible(filed);
            Object value = filed.get(queryOptionsVo);
            JoJoOptions jojoOptions = filed.getAnnotation(JoJoOptions.class);

            if (whetherToAddResult(value, jojoOptions)) {
                Class<? extends BaseOptionsInterface> using = jojoOptions.using();
                BaseOptionsInterface baseOptionsInterface = using.getEnumConstants()[0];
                List<OptionsBo> options = baseOptionsInterface.getOptions();
                options.forEach(optionsBo -> optionsBo.setLabel(optionsBo.getLabel()));
                if (ObjectUtil.isNotEmpty(queryOptionsVo.getFilterCode())) {
                    options = options.stream()
                        .filter(optionsBo -> queryOptionsVo.getFilterCode().equals(optionsBo.getRelation()))
                        .collect(Collectors.toList());
                }

                dictionaryVos.add(DictionaryVo.builder().dictionaryType(filed.getName()).optionsBos(options).build());
            }
        }

        return DictionaryAdminVo.builder().dictionaryVos(dictionaryVos).build();
    }

    /**
     * 是否要添加到结果
     *
     * @param value       价值
     * @param jojoOptions jojo下拉选类型枚举注解
     * @return boolean
     */
    private boolean whetherToAddResult(Object value, JoJoOptions jojoOptions) {
        return ObjectUtil.isNotEmpty(value)
            && ObjectUtil.isNotEmpty(jojoOptions)
            && value instanceof Boolean
            && Boolean.TRUE.equals(value);
    }
}
