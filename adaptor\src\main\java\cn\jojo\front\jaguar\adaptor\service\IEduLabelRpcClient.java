package cn.jojo.front.jaguar.adaptor.service;

import cn.jojo.edu.fantasy.rpc.api.dto.EduLabelDto;
import cn.jojo.front.jaguar.common.pojo.bo.EduLabelBo;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;

public interface IEduLabelRpcClient {

    List<EduLabelBo> queryLabelInfoList(List<Long> labelIdList);

    /**
     * 分页查询标签信息
     *
     * @param pageNum 分页页码
     * @param pageSize 分页大小
     * @param labelTypes 标签类型
     * @param labelIds 标签Id 精确查询 可为空
     * @param labelName 标签类型 模糊查询 可为空
     * @return
     */
    Page<EduLabelDto> queryLabelPage(Integer pageNum, Integer pageSize, List<String> labelTypes, List<Long> labelIds, String labelName);

}
