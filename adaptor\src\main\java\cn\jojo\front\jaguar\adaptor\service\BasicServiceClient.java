package cn.jojo.front.jaguar.adaptor.service;

import cn.jojo.cc.common.dto.DictDto;
import cn.jojo.cc.common.dto.SubjectTypeDto;
import java.util.List;


/**
 * <AUTHOR>
 */
public interface BasicServiceClient {
    /**
     * 根据课程阶段信息获取获取课程阶段码
     * @return
     * @param dictType
     */
    List<DictDto> getDictDtoList(String dictType);

    List<SubjectTypeDto> getAllSubjectType();

    /**
     * 根据租户获取课程阶段信息
     *
     * @return
     */
    List<SubjectTypeDto> listCourseSubjectByTenant();
}
