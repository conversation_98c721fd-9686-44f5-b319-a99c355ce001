package cn.jojo.front.jaguar.admin.common.advice;

import cn.jojo.edu.common.utils.exceptions.ParamValidException;
import cn.jojo.front.jaguar.common.exception.BusinessException;
import cn.jojo.front.jaguar.common.exception.SystemException;
import cn.jojo.front.jaguar.common.utils.ApolloUtil;
import cn.jojo.infra.sdk.api.constants.ApiResultPlatformCodeConstants;
import cn.jojo.infra.sdk.api.metadata.DefaultHttpResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.converter.HttpMessageConversionException;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.BindException;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.ValidationException;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@ControllerAdvice
@Slf4j
public class GlobalExceptionHandler {
    @ExceptionHandler(SystemException.class)
    public @ResponseBody Object systemExceptionHandler(HttpServletRequest request, HttpServletResponse response,
                                                       SystemException exception) {
        log.error("系统异常, message={}", exception.getMessage(), exception);

        DefaultHttpResult<Object> httpResult = new DefaultHttpResult<>();

        httpResult.setMessage(ApiResultPlatformCodeConstants.SYSTEM_ERROR.getMessage());
        httpResult.setCode(ApiResultPlatformCodeConstants.SYSTEM_ERROR.getCode());

        return httpResult;
    }

    @ExceptionHandler(Exception.class)
    public @ResponseBody
    Object exceptionHandler(HttpServletRequest request, HttpServletResponse response, Exception exception) {
        DefaultHttpResult<Object> httpResult = new DefaultHttpResult<>();
        DefaultHttpResult<Object> paramHttpResult = ParamExceptionHandler(request, response, exception, httpResult);
        if (paramHttpResult != null) {
            return paramHttpResult;
        }

        if (exception instanceof BusinessException) {
            log.warn("捕获到业务异常:{}, exception:", exception.getMessage(), exception);
            httpResult.setMessage(exception.getMessage());
            httpResult.setCode(((BusinessException) exception).getCode().getCode());
        } else {
            log.error("捕获到系统错误:{}, exception:", exception.getMessage(), exception);
            httpResult.setCode(ApiResultPlatformCodeConstants.SYSTEM_ERROR.getCode());
            httpResult.setMessage(ApolloUtil.getStringMessage("error.system.error"));
        }

        return httpResult;
    }

    private DefaultHttpResult<Object> ParamExceptionHandler(HttpServletRequest request, HttpServletResponse response,
                                                            Exception exception,
                                                            DefaultHttpResult<Object> httpResult) {

        if (exception instanceof ValidationException || exception instanceof MissingServletRequestParameterException) {
            // param参数不符合验证规则 如：非空字符串或非null等
            log.warn("捕获到参数校验异常, 非空字符串或非null等 path={}, exception:", request.getServletPath(),
                exception);
            httpResult.setMessage(exception.getMessage());
            httpResult.setCode(ApiResultPlatformCodeConstants.PARAM_ERROR.getCode());

            return httpResult;
        }

        if (exception instanceof MethodArgumentNotValidException) {
            // 嵌套验证
            log.warn("捕获到参数校验异常, 嵌套验证 path={}, exception:", request.getServletPath(), exception);
            BindingResult bindingResult = ((MethodArgumentNotValidException) exception).getBindingResult();

            httpResult.setMessage(Objects.requireNonNull(bindingResult.getFieldError()).getDefaultMessage());
            httpResult.setCode(ApiResultPlatformCodeConstants.PARAM_ERROR.getCode());

            return httpResult;
        }

        if (exception instanceof MethodArgumentTypeMismatchException) {
            // 参数类型 如：integer 传 string
            log.warn("捕获到参数校验异常, 参数类型 path={}, exception:", request.getServletPath(), exception);
            MethodArgumentTypeMismatchException ex = (MethodArgumentTypeMismatchException) exception;
            String name = ex.getName();
            String message = name + "is illegal!";
            httpResult.setMessage(message);
            httpResult.setCode(ApiResultPlatformCodeConstants.PARAM_ERROR.getCode());

            return httpResult;
        }

        if (exception instanceof HttpMessageNotReadableException) {
            // 参数解析 如：json解析
            log.warn("捕获到参数校验异常, 参数解析 path={}, exception:", request.getServletPath(), exception);
            httpResult.setMessage("param error");
            httpResult.setCode(ApiResultPlatformCodeConstants.PARAM_ERROR.getCode());

            return httpResult;
        }

        if (exception instanceof BindException) {
            // 参数绑定 如：json解析
            log.warn("捕获到参数校验异常, 参数绑定 path={}, exception:", request.getServletPath(),
                exception);
            BindingResult bindingResult = ((BindException) exception).getBindingResult();
            httpResult.setMessage(Objects.requireNonNull(bindingResult.getFieldError()).getDefaultMessage());
            httpResult.setCode(ApiResultPlatformCodeConstants.PARAM_ERROR.getCode());

            return httpResult;
        }

        if (exception instanceof HttpMessageConversionException) {
            // 参数类型转换
            log.warn("捕获到参数校验异常, 参数类型转换 path={}, exception:", request.getServletPath(),
                exception);
            httpResult.setMessage("param error");
            httpResult.setCode(ApiResultPlatformCodeConstants.PARAM_ERROR.getCode());

            return httpResult;
        }

        if (exception instanceof ParamValidException) {
            // 参数类型转换
            log.warn("捕获到参数校验异常, 参数校验不通过 path={}, exception:", request.getServletPath(),
                exception);
            String msgPrefix = ApolloUtil.getStringMessage("error.param.error");
            httpResult.setMessage(msgPrefix + ":" + exception.getMessage());
            httpResult.setCode(ApiResultPlatformCodeConstants.PARAM_ERROR.getCode());

            return httpResult;
        }

        return null;
    }
}
