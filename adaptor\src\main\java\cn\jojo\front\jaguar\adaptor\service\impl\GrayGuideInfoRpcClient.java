package cn.jojo.front.jaguar.adaptor.service.impl;

import cn.jojo.front.jaguar.adaptor.service.IGrayGuideInfoRpcClient;
import cn.jojo.front.jaguar.common.pojo.bo.GrayGuideInfoBo;
import cn.jojo.front.jaguar.common.pojo.req.GrayGuideInfoReq;
import cn.jojo.front.jaguar.common.utils.ModelConvertUtil;
import cn.jojo.infra.sdk.api.metadata.IRpcResult;
import cn.jojo.pagani.rpc.api.domain.dto.guide.GrayGuideInfoDTO;
import cn.jojo.pagani.rpc.api.domain.req.GetGrayScaleReq;
import cn.jojo.pagani.rpc.api.service.GrayGuideInfoRpcService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class GrayGuideInfoRpcClient implements IGrayGuideInfoRpcClient {

    @Reference
    private GrayGuideInfoRpcService grayGuideInfoRpcService;

    @Override
    public void saveGuideInfo(GrayGuideInfoReq guideInfoReq) {
        IRpcResult<Void> rpcResult =
            grayGuideInfoRpcService.insertGuideInfo(ModelConvertUtil.build(guideInfoReq, GrayGuideInfoDTO.class));
        if (!rpcResult.checkSuccess()) {
            log.warn("invoke grayGuideInfoRpcService.insertGuideInfo fail:{}", rpcResult.getMessage());
        }
    }

    @Override
    public GrayGuideInfoBo grayScaleByUserId(Long userId) {
//        IRpcResult<GrayGuideInfoDTO> rpcResult = grayGuideInfoRpcService.grayScaleByUserInfo(userId);
        GetGrayScaleReq req = GetGrayScaleReq.builder()
            .userId(userId)
            .build();
        req.setRequireWriteRoute(Boolean.TRUE);

        IRpcResult<GrayGuideInfoDTO> rpcResult = grayGuideInfoRpcService.grayScaleByUserInfoV2(req);
        if (!rpcResult.checkSuccess()) {
            log.warn("invoke grayGuideInfoRpcService.grayScaleByUserInfo fail:{}", rpcResult.getMessage());
        }
        return ModelConvertUtil.build(rpcResult.getData(), GrayGuideInfoBo.class);
    }

    @Override
    public void updateGuideInfo(GrayGuideInfoReq guideInfoReq) {
        IRpcResult<Void> rpcResult =
            grayGuideInfoRpcService.updateGuideInfo(ModelConvertUtil.build(guideInfoReq, GrayGuideInfoDTO.class));
        if (!rpcResult.checkSuccess()) {
            log.warn("invoke grayGuideInfoRpcService.updateGuideInfo fail:{}", rpcResult.getMessage());
        }
    }
}
