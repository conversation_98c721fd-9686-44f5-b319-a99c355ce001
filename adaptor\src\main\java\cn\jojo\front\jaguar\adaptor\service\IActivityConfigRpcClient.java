package cn.jojo.front.jaguar.adaptor.service;

import cn.jojo.edu.wings.rpc.api.dto.activity.ActivityBasicInfoDto;
import cn.jojo.edu.wings.rpc.api.dto.activity.ActivityCustomConfigNewDto;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface IActivityConfigRpcClient {

    /**
     * 查询活动基础配置信息
     *
     * @param activityIds         活动id
     * @param classTagActivityIds 活动id 传其中一个即可
     * @param assembleDetail      是否返回详情
     * @return 活动列表
     */
    List<ActivityBasicInfoDto> getActivityList(List<Long> activityIds, List<Long> classTagActivityIds,
                                               boolean assembleDetail);

    /**
     * 获取活动配置
     *
     * @param activityId         活动id
     * @param classTagActivityId 活动id
     * @param types              获取类型
     * @return 聚合结果
     */
    ActivityCustomConfigNewDto queryActivityCustomConfig(Long activityId, Long classTagActivityId,
                                                         List<String> types);
}
