package cn.jojo.front.jaguar.admin.model.convert;

import cn.jojo.edu.common.utils.convert.BaseModelConvert;
import cn.jojo.front.jaguar.admin.model.convert.decorator.TaskUpdateReq2VoConvertDecorator;
import cn.jojo.front.jaguar.admin.model.req.task.TaskUpdateReq;
import cn.jojo.front.jaguar.admin.model.req.task.TaskVoiceReq;
import cn.jojo.front.jaguar.biz.service.pojo.bo.task.TaskVo;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson2.JSONValidator;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

import org.apache.commons.lang3.StringUtils;
import org.mapstruct.DecoratedWith;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

/**
 * vo转换
 *
 * <AUTHOR>
 * @date 2022/05/16
 */
@Mapper
@DecoratedWith(value = TaskUpdateReq2VoConvertDecorator.class)
public interface TaskUpdateReq2VoConvert extends BaseModelConvert<TaskUpdateReq, TaskVo> {

    TaskUpdateReq2VoConvert INSTANCE = Mappers.getMapper(TaskUpdateReq2VoConvert.class);

    @Mapping(source = "taskVoices", target = "taskVoice", qualifiedByName = "taskVoiceToString")
    TaskVo model1ToModel2(TaskUpdateReq req);

    @Mapping(source = "taskVoice", target = "taskVoices", qualifiedByName = "taskVoiceToObj")
    TaskUpdateReq model2ToModel1(TaskVo taskVo);

    @Named("taskVoiceToObj")
    default List<TaskVoiceReq> taskVoiceToObj(String taskVoice){
        if (!JSONValidator.from(taskVoice).validate()) {
            return Collections.emptyList();
        }
        return JSON.parseObject(taskVoice,new TypeReference<List<TaskVoiceReq>>(){});
    }

    @Named("taskVoiceToString")
    default String taskVoiceToString(List<TaskVoiceReq> taskVoices){
        if(Objects.isNull(taskVoices)){
            return StringUtils.EMPTY;
        }
        return JSON.toJSONString(taskVoices);
    }
}
