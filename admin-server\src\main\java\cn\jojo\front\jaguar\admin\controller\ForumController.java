package cn.jojo.front.jaguar.admin.controller;

import cn.jojo.front.jaguar.common.pojo.bo.EmployeeBo;
import cn.jojo.front.jaguar.common.pojo.entity.forum.Forum;
import cn.jojo.front.jaguar.common.pojo.entity.forum.ForumCategory;
import cn.jojo.front.jaguar.common.pojo.req.ForumRuleListReq;
import cn.jojo.front.jaguar.common.pojo.req.ForumRuleSaveReq;
import cn.jojo.front.jaguar.common.pojo.req.PriorityUpdateReq;
import cn.jojo.front.jaguar.common.pojo.vo.ForumCardTemplateVo;
import cn.jojo.front.jaguar.common.pojo.vo.ForumRuleDetailVo;
import cn.jojo.front.jaguar.common.pojo.vo.ForumRuleListVo;
import cn.jojo.front.jaguar.core.service.forum.ForumCardTemplateService;
import cn.jojo.front.jaguar.core.service.forum.ForumService;
import cn.jojo.infra.sdk.api.metadata.DefaultHttpResult;
import cn.jojo.infra.sdk.api.metadata.DefaultPageResp;
import cn.jojo.infra.sdk.api.metadata.IHttpResult;
import cn.jojo.infra.sdk.api.metadata.IPageResp;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import java.util.Collections;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.constraints.NotNull;

import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("admin/forum")
@Tag(name = "Jaguar管理后台")
@Validated
public class ForumController extends BaseController {

    @Resource
    private ForumService forumService;
    @Resource
    private ForumCardTemplateService forumCardTemplateService;

    @Operation(summary ="板块规则列表",   description = "获取板块规则列表")
    @GetMapping("/getForumGroupList")
    public IPageResp<ForumRuleListVo> getForumList(ForumRuleListReq<Void> req) {
        IPage<ForumRuleListVo> page = forumService.listForumRule(req);
        return DefaultPageResp.buildPageResp(req, page.getTotal(), page.getRecords());
    }

    @Operation(summary ="板块优先级",   description = "更新板块优先级")
    @PostMapping("/updateForumRulePriority")
    public IHttpResult<Boolean> updateForumRulePriority(@RequestBody @Validated PriorityUpdateReq req) {
        Forum forum = forumService.updatePriority(req);
        if (forum !=null) {
            forumService.deleteCacheByForumIds(Collections.singleton(req.getBusinessId()), forum.getForumKey());
        }

        return DefaultHttpResult.successWithData(true);
    }

    @Operation(summary ="板块规则详情",   description = "获取板块规则详情")
    @GetMapping("/getForumRuleDetail")
    public IHttpResult<ForumRuleDetailVo> getForumRuleDetail(@RequestParam("forumRuleId") @NotNull Integer forumRuleId) {
        List<ForumRuleDetailVo> resultList = forumService.listForumRuleDetailByIds(Collections.singleton(forumRuleId));
        return DefaultHttpResult.successWithData(CollectionUtils.isEmpty(resultList) ? null : resultList.get(0));
    }

    @Operation(summary ="板块规则保存",   description = "保存板块规则")
    @PostMapping("/saveForumRule")
    public IHttpResult<Integer> saveForumRule(@RequestBody @Validated ForumRuleSaveReq req) {
        Forum forum = forumService.saveOrUpdate(req, new EmployeeBo()
            .setEmployeeId(getEmployeeId())
            .setEmployeeName(getEmployeeName()));
        forumService.deleteCacheByForumIds(Collections.singleton(forum.getId()), forum.getForumKey());
        return DefaultHttpResult.successWithData(forum.getId());
    }

    @Operation(summary ="板块",   description = "获取所有板块")
    @GetMapping("/getAllForum")
    public IHttpResult<List<ForumCategory>> getAllForum() {
        return DefaultHttpResult.successWithData(forumService.getAllForum());
    }

    @Operation(summary ="板块卡片模板",   description = "获取所有板块卡片模板")
    @GetMapping("/getAllTemplate")
    public IHttpResult<List<ForumCardTemplateVo>> getAllTemplate() {
        return DefaultHttpResult.successWithData(forumCardTemplateService.getAllTemplate());
    }

    @GetMapping("deleteForumRule")
    public IHttpResult<Boolean> deleteForumRule(@RequestParam("forumRuleId") @NotNull Integer forumRuleId) {
        Forum forum = forumService.deleteForumRuleById(forumRuleId, new EmployeeBo()
            .setEmployeeId(getEmployeeId())
            .setEmployeeName(getEmployeeName()));
        forumService.deleteCacheByForumIds(Collections.singleton(forum.getId()), forum.getForumKey());
        return DefaultHttpResult.successWithData(true);
    }
}
