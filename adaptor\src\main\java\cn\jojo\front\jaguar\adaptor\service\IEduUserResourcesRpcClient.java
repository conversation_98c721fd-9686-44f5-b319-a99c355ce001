package cn.jojo.front.jaguar.adaptor.service;



import cn.jojo.front.jaguar.common.pojo.req.EduUserResourcesQueryRpcReq;
import cn.jojo.front.jaguar.common.pojo.vo.EduUserResourcesExtensionVo;

import java.util.List;

public interface IEduUserResourcesRpcClient {



    /**
     * 查询用户资源信息列表
     *
     * @param req 请求参数
     * @return 查询结果
     * type 参考 EduUserResAggregationType
     */
    List<EduUserResourcesExtensionVo> queryAggregation(EduUserResourcesQueryRpcReq req);


}
