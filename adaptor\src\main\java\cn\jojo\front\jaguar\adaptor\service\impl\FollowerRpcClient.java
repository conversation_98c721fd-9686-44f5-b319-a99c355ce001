package cn.jojo.front.jaguar.adaptor.service.impl;

import cn.jojo.follower.api.FollowerRpcApi;
import cn.jojo.follower.api.request.GetFollowerRequest;
import cn.jojo.follower.api.response.FollowerResponse;
import cn.jojo.front.jaguar.adaptor.service.IFollowerRpcClient;
import cn.jojo.infra.sdk.api.metadata.IRpcResult;
import cn.jojo.infra.sdk.logging.JoJoLogging;
import cn.tinman.clouds.jojoread.common.api.utils.ModelConvertUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class FollowerRpcClient implements IFollowerRpcClient {
    @DubboReference
    private FollowerRpcApi followerRpcApi;

    @Override
    public FollowerResponse getFollower(Long userId, String wechatAppId) {
        if (userId == null || wechatAppId == null) {
            return null;
        }
        GetFollowerRequest request = GetFollowerRequest.builder().userId(userId).appId(wechatAppId).build();
        IRpcResult<FollowerResponse> rpcResult;
        try {
            rpcResult = followerRpcApi.getFollower(request);
            if (!rpcResult.checkSuccess()) {
                JoJoLogging.logger(log)
                    .error("followerRpcApi getFollower failed, req:{}, message:{}", request, rpcResult.getMessage());
                return null;
            }
        } catch (Exception e) {
            JoJoLogging.logger(log).error("followerRpcApi getFollower failed", e);
            return null;
        }
        return ModelConvertUtil.build(rpcResult.getData(), FollowerResponse.class);
    }
}
