package cn.jojo.front.jaguar.admin.model.dto.task;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @description 分页dto
 * @date 2024/03/11
 **/

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema(description="勋章Dto")
public class MedalDto {

    @Schema(description="品类")
    private String subjectType;
    @Schema(description="阶段")
    private Long courseSegmentCode;
    @Schema(description="勋章类型：0 不可升级 1 可升级")
    private Integer type;
    @Schema(description="稀有度")
    private Integer rarity;
    @Schema(description="备注")
    private String remark;
    @Schema(description="分组key")
    private String groupKey;
    @Schema(description="次序")
    private Integer groupSort;

    @Schema(description="资源列表")
    private List<MedalResourceDto> resourceList;

    @Schema(description = "勋章是否已关联")
    private Boolean contactReward;

}
