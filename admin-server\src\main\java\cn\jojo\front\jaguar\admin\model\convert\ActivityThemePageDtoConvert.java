package cn.jojo.front.jaguar.admin.model.convert;

import cn.jojo.edu.common.utils.convert.BaseModelConvert;
import cn.jojo.front.jaguar.admin.model.dto.task.activity.ActivityThemePageDto;
import cn.jojo.front.jaguar.common.pojo.vo.activity.ActivityThemePageVo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface ActivityThemePageDtoConvert extends BaseModelConvert<ActivityThemePageDto, ActivityThemePageVo> {

    ActivityThemePageDtoConvert INSTANCE = Mappers.getMapper(ActivityThemePageDtoConvert.class);
}
