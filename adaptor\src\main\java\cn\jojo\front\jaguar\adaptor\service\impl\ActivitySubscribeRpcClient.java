package cn.jojo.front.jaguar.adaptor.service.impl;

import cn.jojo.front.jaguar.adaptor.service.IActivitySubscribeRpcClient;
import cn.jojo.front.jaguar.common.bo.MessageSendBodyBo;
import cn.jojo.infra.sdk.api.metadata.IRpcResult;
import cn.jojo.pagani.rpc.api.domain.dto.ActivitySubscribeMessageDto;
import cn.jojo.pagani.rpc.api.service.ActivitySubscribeRpcService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class ActivitySubscribeRpcClient implements IActivitySubscribeRpcClient {
    @DubboReference
    private ActivitySubscribeRpcService activitySubscribeRpcService;

    @Override
    public boolean sendMessage(MessageSendBodyBo body) {
        ActivitySubscribeMessageDto req = new ActivitySubscribeMessageDto()
                .setUserId(body.getUserId())
                .setAppPushContent(body.getAppPushContent())
                .setMessageParam(body.getMessageParam())
                .setSendTime(body.getSendTime())
                .setSendType(body.getSendType())
                .setAppPushTitle(body.getAppPushTitle())
                .setBizId(body.getId())
                .setLinkUrl(body.getLinkUrl());
        try {
            IRpcResult<Boolean> rpcResult = activitySubscribeRpcService.sendMessage(req);
            if (!rpcResult.checkSuccess()) {
                log.error("invoke activitySubscribeRpcService.sendMessage failed, message={}", rpcResult.getMessage());
                return false;
            }
            return Optional.ofNullable(rpcResult.getData()).orElse(false);
        } catch (Exception e) {
            log.error("invoke activitySubscribeRpcService.sendMessage failed", e);
        }
        return false;
    }
}
