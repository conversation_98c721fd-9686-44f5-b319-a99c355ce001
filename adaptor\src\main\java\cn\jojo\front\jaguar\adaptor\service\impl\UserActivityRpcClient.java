package cn.jojo.front.jaguar.adaptor.service.impl;

import cn.jojo.edu.wings.rpc.api.req.activity.ActivityParticipatedUserCoutQueryReq;
import cn.jojo.edu.wings.rpc.api.service.IUserActivityRpcService;
import cn.jojo.front.jaguar.adaptor.service.IUserActivityRpcClient;
import cn.jojo.infra.sdk.api.metadata.IRpcResult;
import java.util.List;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class UserActivityRpcClient implements IUserActivityRpcClient {

    @DubboReference
    private IUserActivityRpcService userActivityRpcService;

    @Override
    public Integer countParticipateUser(List<Long> classTagActivityIds, Long activityId, Long classId,
            Long classTeacherId) {
        if (CollectionUtils.isEmpty(classTagActivityIds) && (activityId == null || classId == null)) {
            log.error("参数校验失败");
            return 0;
        }
        ActivityParticipatedUserCoutQueryReq req = new ActivityParticipatedUserCoutQueryReq();
        req.setActivityId(activityId);
        req.setClassId(classId);
        req.setClassTeacherId(classTeacherId);
        req.setClassTagActivityIds(classTagActivityIds);
        try {
            IRpcResult<Integer> rpcResult = userActivityRpcService.countParticipateUser(req);
            if (!rpcResult.checkSuccess()) {
                log.error("invoke userActivityRpcService.countParticipateUser failed, message={}",
                        rpcResult.getMessage());
                return 0;
            }
            return Optional.ofNullable(rpcResult.getData()).orElse(0);
        } catch (Exception e) {
            log.error("invoke userActivityRpcService.countParticipateUser failed", e);
            return 0;
        }
    }
}
