package cn.jojo.front.jaguar.admin.controller;

import cn.jojo.common.api.ex.DefaultHttpActionPageResult;
import cn.jojo.common.api.ex.IHttpActionResult;
import cn.jojo.front.jaguar.common.pojo.bo.PageBo;
import cn.jojo.front.jaguar.common.pojo.req.drainagecourse.DrainageCourseConfigListReq;
import cn.jojo.front.jaguar.common.pojo.req.drainagecourse.DrainageCourseConfigPatchReq;
import cn.jojo.front.jaguar.common.pojo.req.drainagecourse.DrainageCourseConfigSaveReq;
import cn.jojo.front.jaguar.common.pojo.vo.DrainageCourseConfigVo;
import cn.jojo.front.jaguar.core.service.impl.drainagecourse.DrainageCourseConfigService;
import cn.jojo.infra.sdk.api.metadata.DefaultHttpResult;
import cn.jojo.infra.sdk.api.metadata.IHttpResult;
import cn.jojo.infra.sdk.api.metadata.IPageResp;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2024/11/25 17:09
 */
@Tag(name = "引流课配置管理")
@Validated
@RestController
@RequestMapping("/admin/drainage-course-configs")
public class DrainageCourseConfigController extends BaseController {

    @Resource
    private DrainageCourseConfigService drainageCourseConfigService;

    @PostMapping
    @Operation(summary = "新增引流课配置", description = "新增引流课配置")
    public IHttpResult<Long> addDrainageCourseConfig(
        @Validated @RequestBody DrainageCourseConfigSaveReq req) {
        req.setOperatorUserId(getEmployeeId());
        req.setOperatorUserName(getEmployeeName());
        Long id = drainageCourseConfigService.saveOrUpdate(req);
        return DefaultHttpResult.successWithData(id);
    }

    @PutMapping("/{id}")
    @Operation(summary = "修改引流课配置", description = "修改引流课配置")
    public IHttpResult<Long> updateDrainageCourseConfig(
        @PathVariable("id") @NotNull(message = "id cannot be null") @Parameter(description = "id") Long id,
        @Validated @RequestBody DrainageCourseConfigSaveReq req) {
        req.setId(id);
        req.setOperatorUserId(getEmployeeId());
        req.setOperatorUserName(getEmployeeName());
        drainageCourseConfigService.saveOrUpdate(req);
        return DefaultHttpResult.successWithData(id);
    }

    @GetMapping
    @Operation(summary = "查询引流课配置列表", description = "查询引流课配置列表")
    public IHttpActionResult<IPageResp<DrainageCourseConfigVo>> listDrainageCourseConfigs(
        DrainageCourseConfigListReq req) {
        PageBo<DrainageCourseConfigVo> pageInfo = drainageCourseConfigService.page(req);
        return DefaultHttpActionPageResult
            .successWithPageData(pageInfo.getPageNum(), pageInfo.getPageSize(), pageInfo.getTotal(),
                pageInfo.getData());
    }

    @GetMapping("/{id}")
    @Operation(summary = "查询引流课配置详情", description = "查询引流课配置详情")
    public IHttpResult<DrainageCourseConfigVo> getDrainageCourseConfig(
        @PathVariable("id") @NotNull(message = "id cannot be null") @Parameter(description = "id") Long id) {
        return DefaultHttpResult.successWithData(drainageCourseConfigService.detail(id));
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "删除引流课配置", description = "删除引流课配置")
    public IHttpResult<Boolean> deleteDrainageCourseConfig(
        @PathVariable("id") @NotNull(message = "id cannot be null") @Parameter(description = "id") Long id) {
        drainageCourseConfigService.delete(id, getEmployeeId(), getEmployeeName());
        return DefaultHttpResult.successWithData(true);
    }

    @PatchMapping("/{id}")
    @Operation(summary = "更新引流课配置状态", description = "更新引流课配置状态")
    public IHttpResult<Boolean> updateDrainageCourseConfigStatus(
        @PathVariable("id") @NotNull(message = "id cannot be null") @Parameter(description = "id") Long id,
        @RequestBody @Validated DrainageCourseConfigPatchReq req) {
        req.setOperatorUserId(getEmployeeId());
        req.setOperatorUserName(getEmployeeName());
        req.setId(id);
        drainageCourseConfigService.updateStatus(req);
        return DefaultHttpResult.successWithData(true);
    }
}
