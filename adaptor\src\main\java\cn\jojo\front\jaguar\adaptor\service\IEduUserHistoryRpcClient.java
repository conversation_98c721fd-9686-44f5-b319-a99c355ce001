package cn.jojo.front.jaguar.adaptor.service;

import cn.jojo.edu.fantasy.rpc.api.req.EduUserHistoryReq;
import cn.jojo.front.jaguar.common.pojo.bo.EduUserHistoryBo;
import cn.jojo.front.jaguar.common.pojo.req.UserBrowserRecordReq;
import cn.jojo.front.jaguar.common.pojo.vo.UserBrowserRecordVo;

import java.util.List;

/**
 * @description:
 * @author: luohuanrong
 * @create: 2023/6/6
 **/
public interface IEduUserHistoryRpcClient {

    List<EduUserHistoryBo> queryUserHistoryPage(EduUserHistoryReq req);


    /**
     * 查询用户浏览记录
     *
     * @param req 请求参数
     * @return 查询数据
     */
    List<UserBrowserRecordVo> getUserBrowserRecords(UserBrowserRecordReq req);

}
