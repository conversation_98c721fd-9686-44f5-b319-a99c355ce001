package cn.jojo.front.jaguar.admin.model.req.activity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class StickerReq {

    @Schema(description = "贴纸key")
    @Length(max = 20)
    @NotBlank(message = "stickerKey must not blank")
    private String stickerKey;
}
