package cn.jojo.front.jaguar.admin.controller;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.jojo.front.jaguar.biz.service.PairedReadingBannerBizService;
import cn.jojo.front.jaguar.common.exception.BusinessException;
import cn.jojo.front.jaguar.common.exception.BusinessToastException;
import cn.jojo.front.jaguar.common.pojo.entity.banner.BannerExcelVo;
import cn.jojo.front.jaguar.common.pojo.entity.banner.BannerListTmpVo;
import cn.jojo.front.jaguar.common.pojo.entity.button.BannerExcelParam;
import cn.jojo.front.jaguar.common.pojo.req.BannerDetailReq;
import cn.jojo.front.jaguar.common.pojo.req.BannerListTmpReq;
import cn.jojo.front.jaguar.common.pojo.req.BannerPageReq;
import cn.jojo.front.jaguar.common.pojo.req.BannerUploadExcelReq;
import cn.jojo.front.jaguar.common.pojo.req.BatchUpdateBannerReq;
import cn.jojo.front.jaguar.core.service.banner.PairedReadingBannerRelationService;
import cn.jojo.infra.sdk.api.constants.ApiResultPlatformCodeConstants;
import cn.jojo.infra.sdk.api.metadata.DefaultHttpResult;
import cn.jojo.infra.sdk.api.metadata.IHttpResult;
import cn.jojo.infra.sdk.api.metadata.IPageResp;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import java.net.URLEncoder;
import java.util.List;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotNull;

import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


@RestController
@Tag(name = "伴读新增banner")
@RequestMapping(value = "/bannerExcel")
@Slf4j
@Validated
public class BannerExcelController {

    @Autowired
    private PairedReadingBannerBizService pairedReadingBannerBizService;

    @Autowired
    private PairedReadingBannerRelationService pairedReadingBannerRelationService;

    @Operation(summary ="excel导出")
    @GetMapping(value = "/exportExcel")
    public IHttpResult<Void> exportExcel(@NotNull Long materialId, HttpServletResponse response) {
        if(materialId == null){
            throw new BusinessToastException("param error");
        }
        try{
            // 告诉浏览器用什么软件可以打开此文件
            response.setHeader("content-Type", "application/vnd.ms-excel");
            // 下载文件的默认名称
            String fileName = URLEncoder.encode("banner明细数据表", "UTF-8").replace("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xls");
            //编码
            response.setCharacterEncoding("UTF-8");
            //ExcelExportUtil.exportExcel()方法的第二个参数为对应实体class对象，第三个参数为对应实体的list集合
            List<BannerExcelParam> bannerExcels =
                pairedReadingBannerBizService.getBannerExcelByMaterialId(materialId);
            if(CollectionUtils.isEmpty(bannerExcels)){
                log.warn("banner明细表数据查询为空");
                return DefaultHttpResult.successWithoutData();
            }
            Workbook workbook = ExcelExportUtil
                .exportExcel(new ExportParams(), BannerExcelParam.class,bannerExcels);
            workbook.write(response.getOutputStream());
        }catch (Exception e){
            log.error("excel export error", e);
        }
        return null;
    }

    @Operation(summary ="excel导入/更换")
    @PostMapping(value = "/importExcel")
    public IHttpResult<List<Long>> importExcel(@Validated BannerUploadExcelReq req) {
        if(req.getFile()== null){
            throw new BusinessException(ApiResultPlatformCodeConstants.PARAM_ERROR,"param error");
        }
        return DefaultHttpResult.successWithData(pairedReadingBannerBizService.importExcel(req.getMaterialId(),req.getFile()));
    }

    @Operation(summary ="banner明细表格查询(type 0-当前展示；1-未展示；2-已展示)")
    @GetMapping(value = "/getBannerList")
    public IHttpResult<IPageResp<BannerExcelVo>> getBannerList(@Validated BannerPageReq req) {
        return DefaultHttpResult.successWithData(pairedReadingBannerBizService.getBannerListByPage(req.getMaterialId(), req.getType(),req.getPageNum(),req.getPageSize(),req.getMaterialName()));
    }

    @Operation(summary ="banner明细表格查询临时编辑用")
    @PostMapping(value = "/getBannerListTmp")
    public IHttpResult<BannerListTmpVo> getBannerListTmp(@RequestBody @Validated BannerListTmpReq req) {
        return DefaultHttpResult.successWithData(pairedReadingBannerBizService.getBannerListTmp(req.getBannerResIds()));
    }

    @Operation(summary ="banner明细单条数据的修改")
    @PostMapping(value = "/updateBannerById")
    public IHttpResult<Void> updateBannerById(@RequestBody @Validated BannerDetailReq req) {
        pairedReadingBannerBizService.updateBannerById(req);
        return DefaultHttpResult.successWithoutData();
    }

    @Operation(summary ="banner明细数据批量修改")
    @PostMapping(value = "/batchUpdateBanner")
    public IHttpResult<Void> batchUpdateBanner(@RequestBody @Validated BatchUpdateBannerReq req) {
        pairedReadingBannerBizService.batchUpdateBanner(req);
        return DefaultHttpResult.successWithoutData();
    }


}
