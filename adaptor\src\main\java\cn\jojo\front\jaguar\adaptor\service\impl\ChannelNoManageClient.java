package cn.jojo.front.jaguar.adaptor.service.impl;

import cn.jojo.infra.sdk.api.metadata.IRpcResult;
import cn.tinman.sharedservices.mall.channel.api.request.ChannelNoBindingReq;
import cn.tinman.sharedservices.mall.channel.api.service.IChannelNoManageService;
import com.alibaba.fastjson2.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * 2024/11/13 13:19
 */
@Service
@Slf4j
public class ChannelNoManageClient {
    @DubboReference
    private IChannelNoManageService channelNoManageService;

    public boolean bindOrCancelChannelNoRel(ChannelNoBindingReq req) {
        try {
            IRpcResult<Void> rpcResult = channelNoManageService.bindOrCancelChannelNoRel(req);
            if (!rpcResult.checkSuccess()) {
                log.error("invoke bindOrCancelChannelNoRel failed, req: {}, rpcResult: {}"
                    , JSON.toJSONString(req), rpcResult.getMessage());
                return false;
            }
            return true;
        } catch (Exception e) {
            log.error("invoke bindOrCancelChannelNoRel failed, req: {}", JSON.toJSONString(req), e);
        }
        return false;
    }
}
