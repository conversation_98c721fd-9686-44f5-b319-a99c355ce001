package cn.jojo.front.jaguar.admin.controller;

import cn.jojo.front.jaguar.common.pojo.bo.EmployeeBo;
import cn.jojo.front.jaguar.common.pojo.entity.emotion.EmotionCourseConfig;
import cn.jojo.front.jaguar.common.pojo.req.BaseListReq;
import cn.jojo.front.jaguar.common.pojo.req.EmotionCourseConfigDetailSaveReq;
import cn.jojo.front.jaguar.common.pojo.req.EmotionCourseConfigSaveReq;
import cn.jojo.front.jaguar.common.pojo.vo.EmotionCourseConfigDetailVo;
import cn.jojo.front.jaguar.common.pojo.vo.EmotionCourseConfigVo;
import cn.jojo.front.jaguar.core.service.emotion.EmotionCourseConfigService;
import cn.jojo.infra.sdk.api.metadata.DefaultHttpResult;
import cn.jojo.infra.sdk.api.metadata.DefaultPageResp;
import cn.jojo.infra.sdk.api.metadata.IHttpResult;
import cn.jojo.infra.sdk.api.metadata.IPageResp;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import java.util.Collections;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;

/**
 * @Description 情绪识别管理
 * <AUTHOR>
 * @Date 2022/7/26 17:05
 */
@RestController
@RequestMapping("/admin/emotionCourseConfig")
@Tag(name = "Jaguar管理后台")
@Validated
public class EmotionCourseConfigController extends BaseController {

    @Autowired
    private EmotionCourseConfigService emotionCourseConfigService;

    @GetMapping("/getEmotionCourseConfigList")
    @Operation(summary ="情绪管理课程列表",   description = "情绪管理课程列表")
    public IPageResp<EmotionCourseConfigVo> getEmotionCourseConfigList(BaseListReq<Void> req) {
        IPage<EmotionCourseConfigVo> page = emotionCourseConfigService.getEmotionCourseConfigList(req);
        return DefaultPageResp.buildPageResp(req, page.getTotal(), page.getRecords());
    }

    @PostMapping("/saveOrUpdateEmotionCourseConfig")
    @Operation(summary ="情绪管理课程新增或修改",   description = "情绪管理课程新增或修改")
    public IHttpResult<Long> saveOrUpdateEmotionCourseConfig(@RequestBody @Validated EmotionCourseConfigSaveReq req) {
        EmotionCourseConfig emotionCourseConfig = emotionCourseConfigService
            .saveOrUpdateEmotionCourseConfig(req, new EmployeeBo()
                .setEmployeeId(getEmployeeId())
                .setEmployeeName(getEmployeeName()));
        if (emotionCourseConfig != null) {
            emotionCourseConfigService.deleteCache(Collections.singletonList(emotionCourseConfig));
        }

        return DefaultHttpResult.successWithData(emotionCourseConfig != null ? emotionCourseConfig.getId() : 0);
    }

    @GetMapping("/deleteEmotionCourseConfig")
    @Operation(summary ="删除情绪管理课程",   description = "删除情绪管理课程")
    public IHttpResult<Boolean> deleteEmotionCourseConfig(
        @RequestParam("emotionCourseConfigId") @NotNull Long emotionCourseConfigId) {
        EmotionCourseConfig emotionCourseConfig = emotionCourseConfigService
            .deleteEmotionCourseConfig(emotionCourseConfigId, new EmployeeBo()
                .setEmployeeId(getEmployeeId())
                .setEmployeeName(getEmployeeName()));
        emotionCourseConfigService.deleteCache(Collections.singletonList(emotionCourseConfig));
        return DefaultHttpResult.successWithData(true);
    }

    @GetMapping("/getEmotionCourseConfigDetail")
    @Operation(summary ="查询课程配置详情",   description = "查询课程配置详情")
    public IHttpResult<EmotionCourseConfigDetailVo> getEmotionCourseConfigDetail(
        @RequestParam("emotionCourseConfigId") @NotNull @Positive Long emotionCourseConfigId) {
        EmotionCourseConfigDetailVo emotionCourseConfigDetailVo = emotionCourseConfigService
            .getEmotionCourseConfigDetail(emotionCourseConfigId);

        return DefaultHttpResult.successWithData(emotionCourseConfigDetailVo);
    }

    @PostMapping("/saveConfigDetail")
    @Operation(summary ="新增班期或用户ID",   description = "新增班期或用户ID")
    public IHttpResult<Boolean> saveConfigDetail(@RequestBody @Validated EmotionCourseConfigDetailSaveReq req) {
        emotionCourseConfigService
            .saveConfigDetail(req, new EmployeeBo()
                .setEmployeeId(getEmployeeId())
                .setEmployeeName(getEmployeeName()));
        return DefaultHttpResult.successWithData(true);
    }

    @GetMapping("/deleteConfigDetail")
    @Operation(summary ="删除班期或用户ID",   description = "删除班期或用户ID")
    public IHttpResult<Boolean> deleteConfigDetail(
        @RequestParam("configDetailId") @NotNull Long emotionCourseConfigId) {
        emotionCourseConfigService
            .deleteConfigDetail(emotionCourseConfigId, new EmployeeBo()
                .setEmployeeId(getEmployeeId())
                .setEmployeeName(getEmployeeName()));

        return DefaultHttpResult.successWithData(true);
    }
}
