package cn.jojo.front.jaguar.admin.controller.task;

import cn.hutool.core.util.ObjectUtil;
import cn.jojo.front.jaguar.admin.controller.BaseController;
import cn.jojo.front.jaguar.admin.model.convert.MedalPageVo2DtoConvert;
import cn.jojo.front.jaguar.admin.model.convert.MedalQueryReq2VoConvert;
import cn.jojo.front.jaguar.admin.model.dto.task.MedalDto;
import cn.jojo.front.jaguar.admin.model.dto.task.MedalPageDto;
import cn.jojo.front.jaguar.admin.model.dto.task.MedalResourceDto;
import cn.jojo.front.jaguar.admin.model.dto.task.MedalResourcePackageDto;
import cn.jojo.front.jaguar.admin.model.req.task.MedalSaveReq;
import cn.jojo.front.jaguar.admin.model.req.task.MedalUpdateReq;
import cn.jojo.front.jaguar.biz.service.pojo.bo.task.MedalPageVo;
import cn.jojo.front.jaguar.biz.service.pojo.bo.task.MedalVo;
import cn.jojo.front.jaguar.biz.service.task.IMedalBizService;
import cn.jojo.front.jaguar.common.pojo.req.task.MedalQueryReq;
import cn.jojo.infra.sdk.api.metadata.DefaultHttpResult;
import cn.jojo.infra.sdk.api.metadata.DefaultPageResp;
import cn.jojo.infra.sdk.api.metadata.IHttpResult;
import cn.jojo.infra.sdk.api.metadata.IPageResp;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;

import java.util.ArrayList;

import java.util.Map;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 勋章管理
 * @date 2024/03/15
 **/

@Validated
@RestController
@RequestMapping("/admin/medals")
@Tag(name = "勋章管理")
public class MedalController extends BaseController {

    @Autowired
    private IMedalBizService medalBizService;
    
    @Value("${oa.employee.page.size:100}")
    int employeesPageSize;

    @GetMapping
    @Operation(summary = "勋章列表", description = "勋章分页查询")
    public IPageResp<MedalPageDto> getMedals(MedalQueryReq req) {
        IPage<MedalPageVo> page = medalBizService.getMedals(MedalQueryReq2VoConvert.INSTANCE.model1ToModel2(req));
        List<MedalPageDto> medalPageDtos = MedalPageVo2DtoConvert.INSTANCE.model1sToModel2s(page.getRecords());
        List<Long> employeeIds = new ArrayList<>(medalPageDtos.size() * 2);
        for (MedalPageDto medal : medalPageDtos) {
            employeeIds.add(medal.getCreateUserId());
            employeeIds.add(medal.getUpdateUserId());
        }
        Map<Long, String> employeeNames = bathGetEmployeeNames(employeeIds, employeesPageSize);
        medalPageDtos.forEach(medal -> {
            medal.setCreateUserName(
                employeeNames.getOrDefault(medal.getCreateUserId(), medal.getCreateUserId().toString()));
            medal.setUpdateUserName(
                employeeNames.getOrDefault(medal.getUpdateUserId(), medal.getUpdateUserId().toString()));
        });
        return DefaultPageResp.buildPageResp(req.getPageNum(), req.getPageSize(), page.getTotal(), medalPageDtos);
    }

    /**
     * 勋章详情查看接口
     *
     * @param id
     * @return
     */
    @GetMapping("/{id}")
    @Operation(summary = "勋章详情查看", description = "勋章详情查看")
    public IHttpResult<MedalDto> getMedalById(
        @Schema(description = "勋章id")
        @NotNull @PathVariable("id") @Positive Long id) {
        List<MedalVo> medalVos = medalBizService.getMedalsById(id);
        if (CollectionUtils.isNotEmpty(medalVos)) {
            List<MedalResourceDto> resourceList =
                medalVos.stream().sorted(Comparator.comparing(MedalVo::getSort)).map(v ->
                    MedalResourceDto.builder()
                        .id(v.getId())
                        .unlockedImage(v.getUnlockedImage())
                        .lockedImage(ObjectUtil.isEmpty(JSON.parseObject(v.getResourceJson(), MedalResourceDto.class))
                            ? StringUtils.EMPTY :
                            JSON.parseObject(v.getResourceJson(), MedalResourceDto.class).getLockedImage())
                        .title(v.getTitle())
                        .resourcePackages(JSON.parseObject(v.getResourcePackage(), MedalResourcePackageDto.class))
                        .build()
                ).collect(Collectors.toList());

            MedalVo medalVo = medalVos.stream().findFirst().orElse(new MedalVo());

            MedalDto medalDto = MedalDto.builder()
                .resourceList(resourceList)
                .subjectType(Optional.ofNullable(medalVo.getSubjectType()).map(Object::toString).orElse(""))
                .courseSegmentCode(medalVo.getCourseSegmentCode())
                .groupKey(medalVo.getGroupKey())
                .groupSort(medalVo.getGroupSort())
                .rarity(medalVo.getRarity())
                .type(medalVo.getType())
                .remark(medalVo.getRemark())
                .contactReward(!medalBizService
                    .validateMedalContactReward(resourceList.stream().map(MedalResourceDto::getId).collect(
                        Collectors.toList())))
                .build();
            return DefaultHttpResult.successWithData(medalDto);
        }
        return DefaultHttpResult.successWithData(null);
    }

    /**
     * 勋章新增接口
     *
     * @param medal
     * @return
     */
    @PostMapping
    @Operation(summary = "勋章新增接口", description = "勋章新增")
    public IHttpResult<Void> createMedal(@Validated @RequestBody MedalSaveReq medal) {
        Long employeeId = getEmployeeId();
        List<MedalVo> medalVos = medal.getResourceList().stream().map(v ->
            MedalVo.builder()
                .resourcePackage(JSON.toJSONString(v.getResourcePackages()))
                .title(v.getTitle())
                .unlockedImage(v.getUnlockedImage())
                .subjectType(medal.getSubjectType())
                .courseSegmentCode(medal.getCourseSegmentCode())
                .type(medal.getType())
                .rarity(medal.getRarity())
                .remark(medal.getRemark())
                .groupSort(medal.getGroupSort())
                .createUserId(employeeId)
                .updateUserId(employeeId)
                .resourceJson(JSON.toJSONString(
                    MedalResourceDto.builder().lockedImage(v.getLockedImage()).unlockedImage(v.getUnlockedImage())
                        .build()))
                .build()
        ).collect(Collectors.toList());

        medalBizService.createMedals(medalVos);

        return DefaultHttpResult.successWithoutData();
    }

    /**
     * 勋章删除接口
     *
     * @param id
     * @return
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "勋章删除接口", description = "删除某个勋章")
    public IHttpResult<Void> deleteMedal(
        @Schema(description = "勋章id")
        @NotNull @Positive @PathVariable("id") Long id) {

        medalBizService.deleteMedal(id);
        return DefaultHttpResult.successWithoutData();
    }

    /**
     * 勋章编辑保存接口
     *
     * @param medal
     * @return
     */
    @PatchMapping
    @Operation(summary = "勋章编辑保存接口", description = "编辑勋章信息并保存")
    public IHttpResult<Void> updateMedal(
        @Validated @RequestBody MedalUpdateReq medal) {

        List<MedalVo> medalVos = medal.getResourceList().stream().map(v ->
            MedalVo.builder()
                .resourcePackage(JSON.toJSONString(v.getResourcePackages()))
                .title(v.getTitle())
                .unlockedImage(v.getUnlockedImage())
                .rarity(medal.getRarity())
                .remark(medal.getRemark())
                .createUserId(getEmployeeId())
                .groupKey(medal.getGroupKey())
                .subjectType(medal.getSubjectType())
                .courseSegmentCode(medal.getCourseSegmentCode())
                .groupSort(medal.getGroupSort())
                .type(medal.getType())
                .id(v.getId())
                .updateUserId(getEmployeeId())
                .resourceJson(JSON.toJSONString(
                    MedalResourceDto.builder().lockedImage(v.getLockedImage()).unlockedImage(v.getUnlockedImage())
                        .build()))
                .build()
        ).collect(Collectors.toList());

        medalBizService.updateMedal(medal.getGroupKey(), medalVos);
        return DefaultHttpResult.successWithoutData();
    }
}
