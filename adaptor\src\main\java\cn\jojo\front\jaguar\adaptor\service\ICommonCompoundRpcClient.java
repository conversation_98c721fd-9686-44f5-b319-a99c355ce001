package cn.jojo.front.jaguar.adaptor.service;

import cn.jojo.front.jaguar.common.pojo.vo.BannerCardVo;
import cn.jojo.front.jaguar.common.pojo.vo.BannerClipsMaterialContentVo;
import cn.jojo.pagani.rpc.api.domain.req.compound.WorkResource;

import java.util.List;

/**
 * @Description: 灰度引导相关rpc
 * @Copyright: Copyright (c) 2022  ALL RIGHTS RESERVED.
 * @Company: 书声科技有限公司
 * @Author: chenHao
 * @CreateDate: 2023/1/3 9:56
 * @UpdateDate: 2023/1/3 9:56
 * @UpdateRemark: init
 * @Version: 1.0
 */
public interface ICommonCompoundRpcClient {

    /**
     * 创建剪辑视频合成任务
     *
     * @param bizType 业务类型
     * @param req 工作资源
     * @return 任务ID
     */
    Long createBannerClipsCompoundTask(Integer bizType, WorkResource req);

    /**
     * 获取剪辑合成结果
     *
     * @param taskId 任务ID
     * @return BannerClipsMaterialContentVo
     */
    BannerClipsMaterialContentVo getClipsBannerMaterialInfo(Long taskId);

    List<BannerClipsMaterialContentVo> getClipsBannerMaterialInfos(List<Long> taskIds);
}
