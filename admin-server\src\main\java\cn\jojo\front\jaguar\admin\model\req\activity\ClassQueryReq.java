package cn.jojo.front.jaguar.admin.model.req.activity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/07/03
 * @Version 1.0
 * @Description
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description="班期数据查询")
public class ClassQueryReq {

    /**
     * 班期id
     */
    @Schema(description="期ID 集合")
    private List<Long> classIds;

    /**
     * 班级key集合
     */
    @Schema(description="班级key 集合")
    private List<String> classKeys;

    /**
     * 课程ID
     */
    @Schema(description="课程ID")
    private Long courseId;

    @Schema(description="活动ID")
    private Long activityId;
}
