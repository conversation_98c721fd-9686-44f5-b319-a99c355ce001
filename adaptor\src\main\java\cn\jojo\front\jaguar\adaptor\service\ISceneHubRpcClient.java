package cn.jojo.front.jaguar.adaptor.service;


import cn.hutool.core.date.DateUtil;
import cn.jojo.edu.fantasy.common.dict.SceneIdType;
import cn.jojo.edu.fantasy.common.dict.scene.SceneHubExtraField;
import cn.jojo.front.jaguar.common.pojo.bo.EduSceneHubAggregationBo;
import cn.jojo.front.jaguar.common.pojo.bo.EduSceneHubBo;
import cn.jojo.front.jaguar.common.pojo.bo.ExtraFieldBo;
import cn.jojo.front.jaguar.common.pojo.bo.SceneHubExtraBo;
import cn.jojo.front.jaguar.common.pojo.req.EduSceneHubRpcReq;
import cn.jojo.front.jaguar.common.pojo.req.SceneHubExtraQueryRpcReq;
import cn.jojo.front.jaguar.common.pojo.req.SceneHubExtraRpcReq;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
public interface ISceneHubRpcClient {

    /**
     * 专辑订阅场景id
     */
    Long SUBSCRIBE_SCENE_ID = 12L;

    /**
     * 获取专辑场景聚合信息
     *
     * @param hubIds  专辑库id
     * @param sceneId 场景id
     * @return 场景列表
     */
    List<EduSceneHubAggregationBo> querySceneHubAggregation(List<Long> hubIds, Long sceneId);


    /**
     * 查询场景专辑库列表
     *
     * @param req 请求参数
     * @return 返回值
     */
    List<EduSceneHubBo> querySceneHubs(EduSceneHubRpcReq req);

    /**
     * 获取专辑场景值下的信息
     *
     * @param rpcReq 请求参数
     * @return 查询结果
     */
    List<EduSceneHubAggregationBo> listSceneHubAggregations(SceneHubExtraRpcReq rpcReq);

    /**
     * 获取某个场景值下的信息
     *
     * @param rpcReq 请求参数
     * @return 专辑信息
     */
    List<SceneHubExtraBo> listSceneHubExtras(SceneHubExtraQueryRpcReq rpcReq);

    /**
     * 获取未更新的订阅hub
     *
     * @param hubIds 专辑库id
     * @return 专辑库id
     */
    default Set<Long> queryNotUpdatedSubscribeHubIds(Collection<Long> hubIds) {
        if (CollectionUtils.isEmpty(hubIds)) {
            return Collections.emptySet();
        }
        Date now = new Date();
        return querySceneHubAggregation(new ArrayList<>(hubIds), SceneIdType.SUBSCRIBE.getId())
            .stream()
            .filter(sceneHub -> {
                Map<String, String> extras = sceneHub.getExtraFields().stream()
                    .collect(Collectors.toMap(ExtraFieldBo::getFieldName, ExtraFieldBo::getFieldValue, (a, b) -> a));
                // 订阅专辑首次更新时间
                String firstUpdateDateStr =
                    extras.get(SceneHubExtraField.SUBSCRIBE_FIRST_UPDATE_DATE.getFieldName());
                if (firstUpdateDateStr == null) {
                    return false;
                }
                Date firstUpdateTime = DateUtil.parseDate(firstUpdateDateStr);
                return now.before(firstUpdateTime);

            }).map(EduSceneHubAggregationBo::getHubId).collect(Collectors.toSet());
    }
}
