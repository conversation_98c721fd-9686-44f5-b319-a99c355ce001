package cn.jojo.front.jaguar.admin.model.dto.task.activity;

import cn.jojo.front.jaguar.common.enums.task.ActivityThemeSceneTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 活动主题操作对象
 *
 * <AUTHOR>
 * @since 2025/4/23 14:03
 */
@Data
@Builder
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "主题详情对象")
public class ActivityThemeDetailDto implements Serializable {

    /**
     * 主题id
     */
    @Schema(description = "主题id")
    private Long id;
    /**
     * 主题名称
     */
    @Schema(description = "主题名称")
    private String name;

    /**
     * 品类
     */
    @Schema(description = "品类")
    private Integer subjectType;

    /**
     * 活动主题场景
     *
     * @see ActivityThemeSceneTypeEnum
     */
    @Schema(description = "活动主题场景", implementation = ActivityThemeSceneTypeEnum.class)
    private String scene;

    /**
     * 场景描述
     */
    @Schema(description = "场景描述")
    private String sceneName;

    /**
     * 页面信息
     */
    @Schema(description = "主题页面配置信息")
    private List<ActivityThemePageDetailDto> pages;
}
