package cn.jojo.front.jaguar.adaptor.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.jojo.front.jaguar.adaptor.service.IChannelRpcClient;
import cn.jojo.infra.sdk.api.metadata.IRpcResult;
import cn.tinman.clouds.meta.rpc.api.model.dto.Channel;
import cn.tinman.clouds.meta.rpc.api.model.dto.ParentChannel;
import cn.tinman.clouds.meta.rpc.api.remote.IChannelService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class ChannelRpcClient implements IChannelRpcClient {
    @DubboReference
    private IChannelService channelService;

    @Override
    public List<ParentChannel> getAllParentChannel() {
        try {
            IRpcResult<List<ParentChannel>> rpcResult = channelService.getAllParentChannel();
            if (!rpcResult.checkSuccess()) {
                log.error("invoke channelService.getAllParentChannel failed, message={}", rpcResult.getData());
                return Collections.emptyList();
            }
            return Optional.ofNullable(rpcResult.getData()).orElse(Collections.emptyList());
        } catch (Exception e) {
            log.error("invoke channelService.getAllParentChannel field", e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<Channel> getChannelByParentChannels(List<String> parentChannels) {
        if (CollectionUtil.isEmpty(parentChannels)) {
            return Collections.emptyList();
        }
        try {
            IRpcResult<List<Channel>> rpcResult = channelService.getParentChannels(parentChannels);
            if (!rpcResult.checkSuccess()) {
                log.error("invoke channelService.getParentChannels failed, message={}", rpcResult.getData());
                return Collections.emptyList();
            }
            return Optional.ofNullable(rpcResult.getData()).orElse(Collections.emptyList());
        } catch (Exception ex) {
            log.error("invoke channelService.getParentChannels failed", ex);
            return Collections.emptyList();
        }
    }

    @Override
    public Optional<String> getParentChannelKeyByChannelKey(String channelKey) {
        if (StringUtils.isBlank(channelKey)) {
            return Optional.empty();
        }
        try {
            IRpcResult<String> rpcResult = channelService.getParentChannelKey(channelKey);
            if (!rpcResult.checkSuccess()) {
                log.error("invoke channelService.getParentChannelKey failed, message={}", rpcResult.getData());
                return Optional.empty();
            }
            return Optional.ofNullable(rpcResult.getData());
        } catch (Exception ex) {
            log.error("invoke channelService.getParentChannels failed", ex);
            return Optional.empty();
        }
    }
}
