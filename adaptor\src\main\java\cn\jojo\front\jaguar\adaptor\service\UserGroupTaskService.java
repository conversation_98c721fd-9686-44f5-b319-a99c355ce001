package cn.jojo.front.jaguar.adaptor.service;

import cn.jojo.front.jaguar.common.pojo.bo.PageResultBo;
import cn.jojo.front.jaguar.common.pojo.req.UserGroupTaskRecordListReq;
import cn.jojo.front.jaguar.common.pojo.vo.UserGroupSyncResultVo;
import cn.jojo.front.jaguar.common.pojo.vo.UserGroupTaskListVo;

/**
 * <AUTHOR>
 */
public interface UserGroupTaskService {

    UserGroupSyncResultVo syncNow(Long id);

    PageResultBo<UserGroupTaskListVo> listUserGroupTaskPage(UserGroupTaskRecordListReq req);

    boolean stopTask(Long id);
}
