package cn.jojo.front.jaguar.adaptor.service;

import cn.jojo.vulcan.nuwa.dubbo.model.dto.ConfigInstanceResDTO;

import java.util.List;
import java.util.Map;

/**
 * @description:
 * @author: luohuanrong
 * @create: 2023/6/8
 **/
public interface INuwaConfigOuterClient {

    /**
     * 功能描述:cacheKey必须放到filterMap里面
     * 〈新版本通过广告位集合的方式获取配置信息〉
     *
     * @param configKey 广告位key
     * @param filterMap 过滤规则
     * @return configKey - >  configRes
     */
    List<ConfigInstanceResDTO> getConfig(String configKey, Map<String, String> filterMap);

}
