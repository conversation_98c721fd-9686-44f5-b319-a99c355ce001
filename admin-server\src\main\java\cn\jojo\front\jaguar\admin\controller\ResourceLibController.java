package cn.jojo.front.jaguar.admin.controller;

import cn.jojo.front.jaguar.common.exception.BusinessException;
import cn.jojo.front.jaguar.common.pojo.req.resource.LibResourceListReq;
import cn.jojo.front.jaguar.common.pojo.req.resource.LibResourceSaveReq;
import cn.jojo.front.jaguar.common.pojo.req.resource.LibResourceUpdateReq;
import cn.jojo.front.jaguar.common.pojo.req.resource.ResourceLibListReq;
import cn.jojo.front.jaguar.common.pojo.req.resource.ResourceLibReferenceListReq;
import cn.jojo.front.jaguar.common.pojo.req.resource.ResourceLibSaveReq;
import cn.jojo.front.jaguar.common.pojo.vo.resource.ResourceLibDetailVo;
import cn.jojo.front.jaguar.common.pojo.vo.resource.ResourceLibReferencesVo;
import cn.jojo.front.jaguar.common.pojo.vo.resource.ResourceVo;
import cn.jojo.front.jaguar.core.service.resource.IBusinessResourceRelationService;
import cn.jojo.front.jaguar.core.service.resource.IResourceLibService;
import cn.jojo.infra.sdk.api.constants.ApiResultPlatformCodeConstants;
import cn.jojo.infra.sdk.api.metadata.DefaultHttpResult;
import cn.jojo.infra.sdk.api.metadata.DefaultPageResp;
import cn.jojo.infra.sdk.api.metadata.IHttpResult;
import cn.jojo.infra.sdk.api.metadata.IPageResp;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.validation.BeanPropertyBindingResult;
import org.springframework.validation.ObjectError;
import org.springframework.validation.Validator;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.ValidationException;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/1/24 10:49
 * @desc
 */
@Slf4j
@Validated
@RequestMapping("/admin/resource-libs")
@RestController
public class ResourceLibController extends BaseController {
    @Resource
    private IResourceLibService resourceLibService;
    @Resource
    private IBusinessResourceRelationService businessResourceRelationService;
    @Qualifier("mvcValidator")
    @Resource
    private Validator validator;

    /**
     * 查询资源库信息
     *
     * @param req 资源库列表请求参数
     * @return HTTP结果，包含资源库信息的HTTP结果
     */
    @GetMapping
    @Operation(summary = "分页查询资源库信息", description = "分页查询资源库信息")
    public IPageResp<ResourceLibDetailVo> query(@Valid ResourceLibListReq req) {
        IPage<ResourceLibDetailVo> page = resourceLibService.page(req);
        return DefaultPageResp.buildPageResp(req, page.getTotal(), page.getRecords());
    }

    /**
     * 查询资源库信息
     */
    @GetMapping("/{id}")
    @Operation(summary = "查询资源库详情", description = "查询资源库详情")
    public IHttpResult<ResourceLibDetailVo> detail(
        @PathVariable(value = "id") @NotNull @Parameter(description = "资源库id") Long id) {
        ResourceLibDetailVo detailVo = resourceLibService.detail(id);
        return DefaultHttpResult.successWithData(detailVo);
    }

    /**
     * 删除资源库
     *
     * @param id 资源库ID
     * @return HTTP结果，包含删除结果的HTTP结果
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除资源库", description = "删除资源库")
    public IHttpResult<Boolean> delete(@PathVariable(value = "id") @NotNull @Parameter(description = "资源库id") Long id) {
        resourceLibService.delete(id);
        return DefaultHttpResult.successWithData(true);
    }

    /**
     * 更新资源库
     *
     * @param req 资源库更新请求参数
     * @return HTTP结果，包含更新结果的HTTP结果
     */
    @PutMapping
    @Operation(summary = "更新资源库", description = "更新资源库")
    public IHttpResult<Long> update(@RequestBody ResourceLibSaveReq req) {
        if (Objects.isNull(req.getId())) {
            throw new BusinessException(ApiResultPlatformCodeConstants.PARAM_ERROR, "id can not be null");
        }

        if (req.getStatus() == null) {
            BeanPropertyBindingResult errors = new BeanPropertyBindingResult(req, "req");
            validator.validate(req, errors);
            if (errors.hasErrors()) {
                List<ObjectError> allErrors = errors.getAllErrors();
                if (CollectionUtils.isNotEmpty(allErrors)) {
                    throw new ValidationException(allErrors.get(0).getDefaultMessage());
                }
            }
        }

        req.setOperatorId(getEmployeeId());
        req.setOperatorName(getEmployeeName());
        return DefaultHttpResult.successWithData(resourceLibService.saveOrUpdate(req));
    }

    /**
     * 保存资源库
     *
     * @param req 资源库保存请求参数
     * @return HTTP结果，包含保存结果的HTTP结果
     */
    @PostMapping
    @Operation(summary = "创建新资源库", description = "创建新资源库")
    public IHttpResult<Long> save(@RequestBody @Valid ResourceLibSaveReq req) {
        req.setOperatorId(getEmployeeId());
        req.setOperatorName(getEmployeeName());
        return DefaultHttpResult.successWithData(resourceLibService.saveOrUpdate(req));
    }

    /**
     * 资源库添加资源
     */
    @PostMapping("/{libId}/resources")
    @Operation(summary = "资源库添加资源", description = "资源库添加资源")
    public IHttpResult<Boolean> addResource(
        @PathVariable(name = "libId") @NotNull(message = "resource lib id can not be null") @Parameter(description = "资源库id") Long libId,
        @RequestBody @Valid LibResourceSaveReq req) {
        req.setResourceLibId(libId);
        return DefaultHttpResult.successWithData(resourceLibService.addResource(req));
    }

    /**
     * 资源库添加资源
     */
    @GetMapping("/{libId}/resources")
    @Operation(summary = "分页查询资源列表", description = "分页查询资源列表")
    public IPageResp<ResourceVo> resourcePage(
        @PathVariable(name = "libId") @NotNull(message = "resource lib id can not be null") @Parameter(description = "资源库id")
        Long libId, LibResourceListReq<Void> req) {
        IPage<ResourceVo> page = resourceLibService.pageResources(libId, req);
        return DefaultPageResp.buildPageResp(req, page.getTotal(), page.getRecords());
    }

    /**
     * 资源库添加资源
     */
    @PatchMapping("/{libId}/resources/{resId}")
    @Operation(summary = "修改资源库资源顺序", description = "修改资源库资源顺序")
    public IHttpResult<Boolean> updResource(
        @PathVariable(name = "libId") @NotNull(message = "resource lib id can not be null") @Parameter(description = "资源库id")
        Long libId,
        @PathVariable(name = "resId") @NotNull(message = "resource id can not be null") @Parameter(description = "资源id")
        Long resId,
        @RequestBody @Valid LibResourceUpdateReq req) {
        return DefaultHttpResult.successWithData(resourceLibService.updateResource(libId, resId, req));
    }

    /**
     * 资源库移除资源
     */
    @DeleteMapping("/{libId}/resources")
    @Operation(summary = "资源库移除资源", description = "资源库移除资源")
    public IHttpResult<Boolean> removeResource(
        @PathVariable(name = "libId") @NotNull(message = "resource lib id can not be null") @Parameter(description = "资源库id") Long libId,
        @RequestParam(name = "resourceIds") @NotEmpty(message = "resource id can not be null") @Parameter(description = "资源id列表")
            List<Long> resourceIds) {
        return DefaultHttpResult.successWithData(resourceLibService.removeResource(libId, resourceIds));
    }

    /**
     * 查询资源库被引用记录
     */
    @GetMapping("/{libId}/reference-records")
    @Operation(summary = "查询资源库被引用记录", description = "查询资源库被引用记录")
    public IPageResp<ResourceLibReferencesVo> getReferRecord(
        @PathVariable(name = "libId") @NotNull(message = "resource lib id can not be null") @Parameter(description = "资源库id") Long libId,
        @Valid ResourceLibReferenceListReq req) {
        req.setResourceLibId(libId);
        IPage<ResourceLibReferencesVo> page = businessResourceRelationService.libReferencesPage(req);
        return DefaultPageResp.buildPageResp(req, page.getTotal(), page.getRecords());
    }
}
