package cn.jojo.front.jaguar.admin.controller.activity;


import cn.hutool.core.util.ObjectUtil;
import cn.jojo.front.jaguar.admin.controller.BaseController;
import cn.jojo.front.jaguar.admin.model.convert.QueryTaskActivityReq2BoConvert;
import cn.jojo.front.jaguar.admin.model.convert.SaveTaskActivityReq2BoConvert;
import cn.jojo.front.jaguar.admin.model.convert.ScopeDelReq2BoConvert;
import cn.jojo.front.jaguar.admin.model.convert.ScopeReq2BoConvert;
import cn.jojo.front.jaguar.admin.model.convert.UpdateTaskActivityReq2BoConvert;
import cn.jojo.front.jaguar.admin.model.req.activity.QueryTaskActivityReq;
import cn.jojo.front.jaguar.admin.model.req.activity.SaveTaskActivityReq;
import cn.jojo.front.jaguar.admin.model.req.activity.ScopeDelReq;
import cn.jojo.front.jaguar.admin.model.req.activity.ScopeReq;
import cn.jojo.front.jaguar.admin.model.req.activity.UpdateTaskActivityReq;
import cn.jojo.front.jaguar.biz.service.activitity.IActivitiesBizService;
import cn.jojo.front.jaguar.biz.service.pojo.bo.activity.ActivityGuideResourcePackageBo;
import cn.jojo.front.jaguar.biz.service.pojo.bo.activity.ActivityResourcePackageBo;
import cn.jojo.front.jaguar.biz.service.pojo.bo.activity.TaskActivityBo;
import cn.jojo.front.jaguar.biz.service.pojo.bo.task.TaskVo;
import cn.jojo.front.jaguar.common.enums.task.ActivityResourcePackageTypeEnum;
import cn.jojo.front.jaguar.common.enums.task.ActivityTypeEnum;
import cn.jojo.front.jaguar.common.exception.MyRuntimeException;
import cn.jojo.front.jaguar.common.pojo.bo.activity.ActivityScopeBo;
import cn.jojo.front.jaguar.common.pojo.vo.activity.ScopeAdminVo;
import cn.jojo.front.jaguar.common.pojo.vo.activity.TaskActivityVo;
import cn.jojo.infra.sdk.api.metadata.DefaultHttpResult;
import cn.jojo.infra.sdk.api.metadata.IHttpResult;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.util.List;

/**
 * <AUTHOR>
 */
@Validated
@RestController
@RequestMapping("admin/activities")
@Tag(name = "激励活动")
public class ActivitiesController extends BaseController {

    @Resource
    private IActivitiesBizService activitiesBizService;

    @Operation(summary = "新增活动", description = "新增活动，包括活动信息，活动任务关联，活动奖励信息关联等")
    @PostMapping
    public IHttpResult<Boolean> add(@RequestBody @Valid SaveTaskActivityReq saveTaskActivityReq) {

        TaskActivityBo taskActivityBo = initSaveTaskActivityBo(saveTaskActivityReq);
        taskActivityBo.setCreator(taskActivityBo.getUpdator());
        taskActivityBo.setCreateUserId(taskActivityBo.getUpdateUserId());

        initResource(taskActivityBo);

        return DefaultHttpResult.successWithData(activitiesBizService.add(taskActivityBo));
    }

    @Operation(summary = "查询明细 ", description = "列表点击某条数据加载时用此接口查询，尽量避免ABA问题")
    @GetMapping("/{id}")
    public IHttpResult<TaskActivityVo> getById(@PathVariable @NotNull @Positive Long id) {

        return DefaultHttpResult.successWithData(activitiesBizService.getById(id));
    }

    @Operation(summary = "编辑活动", description = "编辑活动，包括活动信息，活动任务关联，活动奖励信息关联等")
    @PatchMapping("/{id}")
    public IHttpResult<Boolean> update(
        @RequestBody @Valid UpdateTaskActivityReq updateTaskActivityReq, @PathVariable @Positive Long id) {

        TaskActivityBo taskActivityBo = initTaskActivityBo(updateTaskActivityReq);

        initResource(taskActivityBo);

        return DefaultHttpResult.successWithData(activitiesBizService.update(taskActivityBo));
    }

    private void initResource(TaskActivityBo taskActivityBo) {
        ActivityResourcePackageBo resourcePackage = taskActivityBo.getActivityResourcePackage();
        if (ObjectUtil.isNotEmpty(resourcePackage)) {
            if (ObjectUtil.hasEmpty(resourcePackage.getAndroid(), resourcePackage.getIos(),
                resourcePackage.getName())) {
                taskActivityBo.setActivityResourcePackage(null);
            }
        }

        ActivityGuideResourcePackageBo guidePackage = taskActivityBo.getActivityGuideResourcePackage();

        if (ObjectUtil.isNotEmpty(guidePackage)) {

            if (ObjectUtil.hasEmpty(guidePackage.getAndroid(), guidePackage.getIos(), guidePackage.getName())) {

                taskActivityBo.setActivityGuideResourcePackage(null);
            } else {
                taskActivityBo.getActivityGuideResourcePackage()
                    .setResourceType(ActivityResourcePackageTypeEnum.ACTIVITY_GUIDE.getValue());
            }
        }
    }

    @Operation(summary = "活动删除", description = "删除活动")
    @DeleteMapping("/{id}")
    public IHttpResult<Boolean> delete(@PathVariable @Positive @Schema(description = "活动id") Long id) {

        return DefaultHttpResult.successWithData(activitiesBizService.logicalDeleteById(id));
    }

    @Operation(summary = "查询范围 ", description = "活动范围数据详情回显")
    @GetMapping("/{id}/scopes")
    public IHttpResult<ScopeAdminVo> queryScopeByActivityId(@PathVariable @Positive Long id) {

        return DefaultHttpResult.successWithData(activitiesBizService.queryScopeByActivityId(id));
    }

    @Operation(summary = "活动范围插入", description = "活动范围投放， 插入品类、课程、班期数据")
    @PostMapping("/{id}/scopes")
    public IHttpResult<String> addScope(@RequestBody @Valid ScopeReq scopeReq,
                                        @PathVariable @Positive Long id) {

        ActivityScopeBo scopeBo = ScopeReq2BoConvert.INSTANCE.model1ToModel2(scopeReq);
        scopeBo.setActivityId(id);

        String employeeName = getEmployeeName();
        Long employeeId = getEmployeeId();
        scopeBo.setUpdateUserId(ObjectUtil.isEmpty(employeeId) ? 0 : employeeId);
        scopeBo.setUpdator(ObjectUtil.isEmpty(employeeName) ? StringUtils.EMPTY : employeeName);
        scopeBo.setCreator(scopeBo.getUpdator());
        scopeBo.setCreateUserId(scopeBo.getUpdateUserId());

        return DefaultHttpResult.successWithData(activitiesBizService.addScope(scopeBo));
    }

    @Operation(summary = "投放范围删除", description = "投放范围删除")
    @DeleteMapping("/{id}/scopes")
    public IHttpResult<Boolean> deleteScope(@ModelAttribute ScopeDelReq scopeDelReq,
                                            @PathVariable @Positive Long id) {

        ActivityScopeBo scopeBo = ScopeDelReq2BoConvert.INSTANCE.model1ToModel2(scopeDelReq);
        scopeBo.setActivityId(id);
        return DefaultHttpResult.successWithData(activitiesBizService.deleteScope(scopeBo));
    }

    @Operation(summary = "活动id查询任务列表", description = "活动id查询任务列表")
    @GetMapping("/{id}/tasks")
    public IHttpResult<List<TaskVo>> queryTasks(@PathVariable @Positive Long id) {

        return DefaultHttpResult.successWithData(activitiesBizService.queryTasks(id));
    }

    /**
     * /activities?subject_type=a&course_segment_code=b&class_id=c&type=d&name=e&id=f&page=1&limit=10
     */
    @Operation(summary = "活动列表", description = "活动分页查询")
    @GetMapping
    public IHttpResult<IPage<TaskActivityVo>> list(@ModelAttribute QueryTaskActivityReq queryTaskActivityReq) {

        TaskActivityBo taskActivityBo = QueryTaskActivityReq2BoConvert.INSTANCE.model1ToModel2(queryTaskActivityReq);
        return DefaultHttpResult.successWithData(activitiesBizService.list(taskActivityBo));
    }


    private TaskActivityBo initTaskActivityBo(UpdateTaskActivityReq updateTaskActivityReq) {

        if (!ActivityTypeEnum.containsKey(updateTaskActivityReq.getType())) {
            throw new MyRuntimeException("type not exist");
        }

        TaskActivityBo taskActivityBo = UpdateTaskActivityReq2BoConvert.INSTANCE.model1ToModel2(updateTaskActivityReq);
        String employeeName = getEmployeeName();
        Long employeeId = getEmployeeId();
        taskActivityBo.setUpdateUserId(ObjectUtil.isEmpty(employeeId) ? 0 : employeeId);
        taskActivityBo.setUpdator(ObjectUtil.isEmpty(employeeName) ? StringUtils.EMPTY : employeeName);

        return taskActivityBo;
    }

    private TaskActivityBo initSaveTaskActivityBo(SaveTaskActivityReq saveTaskActivityReq) {

        if (!ActivityTypeEnum.containsKey(saveTaskActivityReq.getType())) {
            throw new MyRuntimeException("type not exist");
        }

        TaskActivityBo taskActivityBo = SaveTaskActivityReq2BoConvert.INSTANCE.model1ToModel2(saveTaskActivityReq);
        String employeeName = getEmployeeName();
        Long employeeId = getEmployeeId();
        taskActivityBo.setUpdateUserId(ObjectUtil.isEmpty(employeeId) ? 0 : employeeId);
        taskActivityBo.setUpdator(ObjectUtil.isEmpty(employeeName) ? StringUtils.EMPTY : employeeName);
        return taskActivityBo;
    }

}
