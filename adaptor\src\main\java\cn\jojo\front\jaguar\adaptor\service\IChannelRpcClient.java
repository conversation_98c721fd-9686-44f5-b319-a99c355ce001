package cn.jojo.front.jaguar.adaptor.service;

import cn.tinman.clouds.meta.rpc.api.model.dto.Channel;
import cn.tinman.clouds.meta.rpc.api.model.dto.NameAndValueDto;
import cn.tinman.clouds.meta.rpc.api.model.dto.ParentChannel;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 */
public interface IChannelRpcClient {
    /**
     * 获取所有父级channel
     * @return 父级channel
     */
    List<ParentChannel> getAllParentChannel();

    /**
     * 根据父级渠道获取详情信息以及子渠道
     * @param parentChannels 父级渠道
     * @return 详情信息
     */
    List<Channel> getChannelByParentChannels(List<String> parentChannels);

    /**
     * 根据channelKey查询父渠道
     * @param channelKey
     * @return
     */
    Optional<String> getParentChannelKeyByChannelKey(String channelKey);
}
