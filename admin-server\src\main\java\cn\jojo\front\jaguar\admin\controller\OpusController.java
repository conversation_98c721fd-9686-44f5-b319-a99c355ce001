package cn.jojo.front.jaguar.admin.controller;

import cn.jojo.front.jaguar.common.pojo.bo.EmployeeBo;
import cn.jojo.front.jaguar.common.pojo.entity.opus.Opus;
import cn.jojo.front.jaguar.common.pojo.req.BaseListReq;
import cn.jojo.front.jaguar.common.pojo.req.OpusSaveReq;
import cn.jojo.front.jaguar.common.pojo.req.PriorityUpdateReq;
import cn.jojo.front.jaguar.common.pojo.vo.OpusDetailVo;
import cn.jojo.front.jaguar.common.pojo.vo.OpusListVo;
import cn.jojo.front.jaguar.core.service.opus.OpusService;
import cn.jojo.infra.sdk.api.metadata.DefaultHttpResult;
import cn.jojo.infra.sdk.api.metadata.DefaultPageResp;
import cn.jojo.infra.sdk.api.metadata.IHttpResult;
import cn.jojo.infra.sdk.api.metadata.IPageResp;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import java.util.Collections;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.constraints.NotNull;

import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/admin/opus")
@Tag(name = "Jaguar管理后台")
@Validated
public class OpusController extends BaseController {

    @Resource
    private OpusService opusService;

    @GetMapping("getOpusList")
    @Operation(summary ="opus列表",   description = "获取opus列表")
    public IPageResp<OpusListVo> getOpusList(BaseListReq<Void> req) {
        IPage<OpusListVo> page = opusService.listOpus(req);
        return DefaultPageResp.buildPageResp(req, page.getTotal(), page.getRecords());
    }

    @PostMapping("/updateOpusPriority")
    @Operation(summary ="Opus优先级",   description = "更新Opus优先级")
    public IHttpResult<Boolean> updateOpusPriority(@RequestBody @Validated PriorityUpdateReq req) {
        Opus opus = opusService.updatePriority(req);
        if (opus != null) {
            opusService.deleteCache(Collections.singletonList(opus.getId()));
        }

        return DefaultHttpResult.successWithData(true);
    }

    @GetMapping("getOpusInfo")
    @Operation(summary ="opus详情",   description = "获取opus详情")
    public IHttpResult<OpusDetailVo> getOpusInfo(@RequestParam("opusId") @NotNull Integer opusId) {
        List<OpusDetailVo> dataList = opusService.listOpusDetailByIds(Collections.singleton(opusId));
        return DefaultHttpResult.successWithData(CollectionUtils.isEmpty(dataList) ? null : dataList.get(0));
    }

    @PostMapping("saveOpus")
    @Operation(summary ="Opus保存",   description = "保存Opus")
    public IHttpResult<Integer> saveOpus(@RequestBody @Validated OpusSaveReq req) {
        Opus opus = opusService.saveOrUpdateOpus(req, new EmployeeBo()
            .setEmployeeId(getEmployeeId())
            .setEmployeeName(getEmployeeName()));
        opusService.deleteCache(Collections.singletonList(opus.getId()));
        return DefaultHttpResult.successWithData(opus.getId());
    }

    @Operation(summary ="删除Opus",   description = "删除Opus")
    @GetMapping("/deleteOpus")
    public IHttpResult<Boolean> deleteOpus(@RequestParam("opusId") @NotNull Integer opusId) {
        Opus opus = opusService.deleteOpus(opusId, new EmployeeBo()
            .setEmployeeName(getEmployeeName())
            .setEmployeeId(getEmployeeId()));
        opusService.deleteCache(Collections.singletonList(opus.getId()));
        return DefaultHttpResult.successWithData(true);
    }
}
