package cn.jojo.front.jaguar.adaptor.service.impl;

import cn.jojo.front.jaguar.adaptor.service.ITagBindingRpcClient;
import cn.jojo.infra.sdk.api.metadata.IRpcResult;
import cn.jojo.tag.api.TagBindingService;
import cn.jojo.tag.api.enums.base.TagBizKeyEnum;
import cn.jojo.tag.api.enums.base.TagUseKeyEnum;
import cn.jojo.tag.api.request.EditTargetTagRequest;
import cn.jojo.tag.api.response.EditTargetTagResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class TagBindingRpcClient implements ITagBindingRpcClient {
    @DubboReference
    private TagBindingService tagBindingService;

    @Value("${app.id:fb-jaguar}")
    private String appId;

    @Override
    public void bindTag(Long userId, List<Long> tagIds) {
        if (userId == null || CollectionUtils.isEmpty(tagIds)) {
            log.info("param check failed");
            return;
        }
        EditTargetTagRequest tagRequest = EditTargetTagRequest.builder().addList(tagIds)
                .targetId(userId)
                .contextInfo(
                        EditTargetTagRequest.ContextInfo.builder().appId(appId).time(new Date()).operatorId(-1L).build())
                .tagUseKey(TagUseKeyEnum.C_PERSON.getKey())
                .tagBizKey(TagBizKeyEnum.DEFAULT.getKey())
                .bizId(-1L)
                .build();
        try {
            IRpcResult<EditTargetTagResponse> rpcResult = tagBindingService.editTargetTag(tagRequest);
            if (!rpcResult.checkSuccess()) {
                log.error("调用crm给用户打评级标签失败,message = {}", rpcResult.getMessage());
            }
        } catch (Exception e) {
            log.error("调用crm给用户打评级标签失败,message = {}", e.getMessage());
        }
    }
}
