package cn.jojo.front.jaguar.adaptor.service;

import cn.jojo.front.jaguar.common.pojo.bo.GrayGuideInfoBo;
import cn.jojo.front.jaguar.common.pojo.req.GrayGuideInfoReq;

/**
 * @Description: 灰度引导相关rpc
 * @Copyright: Copyright (c) 2022  ALL RIGHTS RESERVED
 * @Company: 书声科技有限公司
 * @Author: chenHao
 * @CreateDate: 2023/1/3 9:56
 * @UpdateDate: 2023/1/3 9:56
 * @UpdateRemark: init
 * @Version: 1.0
 */
public interface IGrayGuideInfoRpcClient {

    /**
     * 保存灰度信息
     *
     * @param guideInfoReq 灰度信息请求
     */
    void saveGuideInfo(GrayGuideInfoReq guideInfoReq);

    /**
     * 获取用户灰度
     *
     * @param userId 用户ID
     * @return GrayGuideInfoBo 灰度信息
     */
    GrayGuideInfoBo grayScaleByUserId(Long userId);

    /**
     * 更新灰度
     *
     * @param guideInfoReq 灰度信息请求
     */
    void updateGuideInfo(GrayGuideInfoReq guideInfoReq);
}
