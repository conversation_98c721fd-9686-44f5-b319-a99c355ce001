package cn.jojo.front.jaguar.admin.model.convert;

import cn.jojo.edu.common.utils.convert.BaseModelConvert;
import cn.jojo.front.jaguar.admin.model.req.task.StockSaveReq;
import cn.jojo.front.jaguar.admin.model.req.task.StockUpdateReq;
import cn.jojo.front.jaguar.common.pojo.vo.season.StockOperateVo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @date 2025/01/07
 **/
@Mapper
public interface StockUpdateReq2OperateVoConvert extends BaseModelConvert<StockUpdateReq, StockOperateVo> {
    StockUpdateReq2OperateVoConvert INSTANCE = Mappers.getMapper(StockUpdateReq2OperateVoConvert.class);
}
