package cn.jojo.front.jaguar.adaptor.service;

import cn.tinman.sharedservices.mall.product.api.response.sku.SkuDto;
import cn.tinman.sharedservices.mall.product.api.response.sku.SkuSaleInfoResp;

import java.util.List;

public interface SkuServiceRpcClient {

    List<SkuDto> getSkuSaleInfo(List<Long> skuIds);

    /**
     * 获取sku销售信息, 只获取阅读平台的sku(resourcePlatform = 1)
     * @param skuIds
     * @return
     */
    List<SkuSaleInfoResp> listSaleInfosAnyState(List<Long> skuIds);
}
