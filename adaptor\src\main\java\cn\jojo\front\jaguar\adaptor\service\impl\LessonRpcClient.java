package cn.jojo.front.jaguar.adaptor.service.impl;

import cn.jojo.cc.api.domain.request.LessonListByCourseIdReq;
import cn.jojo.cc.api.domain.request.LessonListByIdsReq;
import cn.jojo.cc.api.service.LessonService;
import cn.jojo.cc.common.dto.CourseLessonDto;
import cn.jojo.cc.common.dto.LessonDto;
import cn.jojo.cc.common.dto.SimpleLessonDto;

import cn.jojo.front.jaguar.adaptor.service.ILessonServiceClient;
import cn.jojo.front.jaguar.common.pojo.bo.SimpleLessonBo;
import cn.jojo.infra.sdk.api.metadata.IRpcResult;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Component
@Slf4j
public class LessonRpcClient implements ILessonServiceClient {

    @Reference
    private LessonService lessonService;

    @Override
    public List<SimpleLessonBo> getSimpleLessonByIds(List<Long> lessonIds) {
        if (CollectionUtils.isEmpty(lessonIds)) {
            return Collections.emptyList();
        }
        try {
            LessonListByIdsReq req = new LessonListByIdsReq();
            req.setLessonIds(lessonIds);
            IRpcResult<List<SimpleLessonDto>> rpcResult = lessonService.listSimpleByIds(req);
            if (!rpcResult.checkSuccess()) {
                log.error("query lessonService.listByIds fail,lessonIds:{}", JSON.toJSON(lessonIds));
                return Collections.emptyList();
            }
            return rpcResult.getData().stream().map(data -> {
                SimpleLessonBo simpleLessonBo = new SimpleLessonBo();
                BeanUtils.copyProperties(data, simpleLessonBo);
                return simpleLessonBo;
            }).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("query lessonService.listByIds fail,lessonIds:{}", lessonIds, e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<CourseLessonDto> listByCourseId(Long courseId) {
        if (Objects.isNull(courseId)) {
            return Collections.emptyList();
        }
        try {
            LessonListByCourseIdReq req = new LessonListByCourseIdReq();
            req.setCourseId(courseId);
            IRpcResult<List<CourseLessonDto>> rpcResult = lessonService.listByCourseId(req);
            if (!rpcResult.checkSuccess()) {
                log.error("query lessonService.listByCourseId fail,courseId:{}", courseId);
                return Collections.emptyList();
            }
            return Optional.ofNullable(rpcResult.getData()).orElse(Collections.emptyList());
        } catch (Exception e) {
            log.error("query lessonService.listByCourseId fail,courseId:{}", courseId, e);
            return Collections.emptyList();
        }
    }
}
