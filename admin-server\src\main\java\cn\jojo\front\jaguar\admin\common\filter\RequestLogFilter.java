package cn.jojo.front.jaguar.admin.common.filter;

import cn.jojo.front.jaguar.common.utils.JacksonUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Pattern;
import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.commons.codec.DecoderException;
import org.apache.commons.codec.net.URLCodec;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;
import org.springframework.web.util.ContentCachingRequestWrapper;
import org.springframework.web.util.ContentCachingResponseWrapper;

/**
 * <AUTHOR>
 */
@Component
public class RequestLogFilter extends OncePerRequestFilter {

    private static final Logger LOGGER_REQUEST_LOG = LoggerFactory.getLogger("RequestLog");

    /**
     * 不记录 request 日志的接口
     */
    private static final String HEALTH_CHECK_URI = "/actuator";

    private static final String IGNORE_LOG_REQUEST_AND_RESPINSE_KEY = "ignoreLogRequestAndResponse";
    private static final List<Pattern> IGNORE_LOG_REQUEST_AND_RESPONSE_PATTERN = new ArrayList<>();

    private static final String IGNORE_LOG_REQUEST_KEY = "ignoreLogRequest";
    private static final List<Pattern> IGNORE_LOG_REQUEST_PATTERN = new ArrayList<>();

    /**
     * 不记录 response 日志的接口
     */
    private static final String IGNORE_LOG_RESPONSE_KEY = "ignoreLogResponse";
    private static final List<Pattern> IGNORE_LOG_RESPONSE_PATTERN = new ArrayList<>();

    /**
     * 日志记录检查 cache
     */
    private static final Map<Integer, Boolean> IGNORE_LOG_CHECK_CACHE = new ConcurrentHashMap<>();

    /**
     * 请求记录最大的阈值，单位 byte
     */
    private static final int RECORD_BODY_SIZE_THRESHOLD = 100 * 1024;

    private URLCodec urlCodec = new URLCodec();

    static {
        // 日志记录匹配处理
        IGNORE_LOG_REQUEST_AND_RESPONSE_PATTERN.add(Pattern.compile("^\\/$"));
        IGNORE_LOG_RESPONSE_PATTERN.add(Pattern.compile("^\\/page\\/.+"));
    }

    /**
     * 返回 true 表示忽略，返回 false 表示不能忽略
     *
     * @param key
     * @param patternList
     * @param request
     * @return
     */
    private boolean checkShouldIgnoreKeepLog(String key, List<Pattern> patternList, HttpServletRequest request) {
        boolean shouldIgnoreLog = false;
        String servletPath = request.getServletPath();

        // 读取缓存
        int cacheKey = 3 * key.hashCode() + 5 * patternList.hashCode() + 7 * servletPath.hashCode();
        Boolean shouldIgnoreLogInCache = IGNORE_LOG_CHECK_CACHE.get(cacheKey);
        if (shouldIgnoreLogInCache != null) {
            return shouldIgnoreLogInCache;
        }

        for (Pattern pattern : patternList) {
            if (pattern.matcher(servletPath).find()) {
                shouldIgnoreLog = true;
                break;
            }
        }

        // 更新缓存
        IGNORE_LOG_CHECK_CACHE.put(cacheKey, shouldIgnoreLog);

        return shouldIgnoreLog;
    }

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
        throws ServletException, IOException {
        ContentCachingRequestWrapper requestWrapper = new ContentCachingRequestWrapper(request);
        ContentCachingResponseWrapper responseWrapper = new ContentCachingResponseWrapper(response);

        // 处理用户请求
        filterChain.doFilter(requestWrapper, responseWrapper);
        // 记录完整的请求
        handleLogRequest(requestWrapper, responseWrapper);
        // 返回信息给用户
        responseWrapper.copyBodyToResponse();

    }

    private String getPayLoad(byte[] buf, String characterEncoding) {
        String payload = "";
        if (buf == null) {
            return payload;
        }
        if (buf.length > 0) {
            int length = buf.length;
            try {
                payload = new String(buf, 0, length, characterEncoding);
            } catch (UnsupportedEncodingException ex) {
                payload = "[requestPayloadError]";
            }
        }
        return payload;
    }

    private void handleLogRequest(ContentCachingRequestWrapper requestWrapper,
        ContentCachingResponseWrapper responseWrapper) {
        String requestPayload;
        String responsePayload;

        // 判断请求数据是否超过了阈值
        if (requestWrapper.getContentLength() > RECORD_BODY_SIZE_THRESHOLD) {
            LOGGER_REQUEST_LOG.warn("请求数据大小超过阈值");
            requestPayload = "[requestPayloadOverflow]";
        } else {
            requestPayload = getPayLoad(requestWrapper.getContentAsByteArray(), "UTF-8");
        }
        // 返回数据不会太大，均需要记录
        responsePayload = getPayLoad(responseWrapper.getContentAsByteArray(), "UTF-8");
        doLog(requestWrapper, requestPayload, responsePayload);
    }

    /**
     * 执行日志记录
     */
    private void doLog(HttpServletRequest request, String requestPayload, String responsePayload) {
        // 健康检查的请求不打印日志
        if (request.getRequestURI().contains(HEALTH_CHECK_URI)) {
            return;
        }
        // 判断是否记录日志
        if (checkShouldIgnoreKeepLog(IGNORE_LOG_REQUEST_AND_RESPINSE_KEY, IGNORE_LOG_REQUEST_AND_RESPONSE_PATTERN,
            request)) {
            return;
        }

        // 整理用户请求、返回信息
        Map<Object, Object> requestObject = new HashMap<>();
        requestObject.put("base", makeBase(request));
        requestObject.put("header", makeHeader(request));
        // 判断是否记录请求信息
        if (checkShouldIgnoreKeepLog(IGNORE_LOG_REQUEST_KEY, IGNORE_LOG_REQUEST_PATTERN, request)) {
            requestObject.put("requestPayload", "ignoreRequestByRule");
        } else {
            requestObject.put("requestPayload", requestPayload);
        }
        // 判断是否记录返回信息
        if (checkShouldIgnoreKeepLog(IGNORE_LOG_RESPONSE_KEY, IGNORE_LOG_RESPONSE_PATTERN, request)) {
            requestObject.put("responsePayload", "ignoreResponseByRule");
        } else {
            requestObject.put("responsePayload", responsePayload);
        }
        try {
            String str = JacksonUtil.getInstance().writeValueAsString(requestObject);
            LOGGER_REQUEST_LOG.info(str);
        } catch (JsonProcessingException e) {
            //序列化问题不处理
        }
    }

    /**
     * 生成请求基础信息
     *
     * @param request
     * @return
     */
    private Map<Object, Object> makeBase(HttpServletRequest request) {
        Map<Object, Object> baseObject = new HashMap<>();
        baseObject.put("url", makeUrl(request));
        baseObject.put("method", request.getMethod());
        return baseObject;
    }

    /**
     * 生成请求的完整地址
     *
     * @param request
     * @return
     */
    private String makeUrl(HttpServletRequest request) {
        String url = request.getRequestURL().toString();
        if (request.getQueryString() != null) {
            try {
                String queryStringDecode = urlCodec.decode(request.getQueryString());
                url = url + "?" + queryStringDecode;
            } catch (DecoderException de) {
                LOGGER_REQUEST_LOG.warn("Query parameter decode error", de);
            }
        }

        return url;
    }

    /**
     * 生成请求头部
     *
     * @param request
     * @return
     */
    private Map<Object, Object> makeHeader(HttpServletRequest request) {
        // 收集客户端请求信息
        Map<Object, Object> headerObject = new HashMap<>();
        Enumeration<String> headers = request.getHeaderNames();
        while (headers.hasMoreElements()) {
            String key = headers.nextElement();
            String value = request.getHeader(key);
            headerObject.put(key, value);
        }
        return headerObject;
    }
}
