package cn.jojo.front.jaguar.admin.model.req.activity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 活动主题页面组件相关具体元素
 *
 * <AUTHOR>
 * @since 2025/4/22 14:55
 */
@Data
@Builder
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "新增活动主题组件相关属性")
public class ActivityThemeComponentPropertiesSaveReq {
    /**-------------------------------------- 页头组件属性---------------------------------**/
    /**
     * 手机端背景图
     */
    @Schema(description = "手机端背景图")
    private String phoneBackgroundImg;
    /**
     * 平板端背景图
     */
    @Schema(description = "平板端背景图")
    private String padBackgroundImg;

    /**--------------------------------------- 活动卡片组件属性 ----------------------------**/
    /**
     * ico图标
     */
    @Schema(description = "ico图标")
    private String icon;
    /**
     * 进度动效资源
     */
    @Schema(description = "进度动效资源")
    private String progressRes;

    /**
     * 背景动效资源
     */
    @Schema(description = "背景动效资源")
    private String backgroundRes;

    /**
     * 进度条头部颜色
     */
    @Schema(description = "进度条头部颜色")
    private String progressHeadColor;

    /**
     * 进度条尾部颜色
     */
    @Schema(description = "进度条尾部颜色")
    private String progressTailColor;

    /**
     * 预告文案
     */
    @Schema(description = "预告文案")
    private String advanceText;

    /**
     * 活动文案
     */
    @Schema(description = "活动文案")
    private String activityText;

    /**
     * 预告跳转页面
     */
    @Schema(description = "预告跳转页面")
    private String advancePage;


    /**
     * 活动跳转页面
     */
    @Schema(description = "活动跳转页面")
    private String activityPage;

    /**--------------------------------------- 课时卡片组件属性----------------------------------**/
    /**
     * 收集物
     */
    @Schema(description = "收集物")
    private String collectRes;

    /**---------------------------------------- 活动弹窗组件属性 ----------------------------------**/
    /**
     * 主标题
     */
    @Schema(description = "主标题")
    private String mainTitle;
    /**
     * 副标题
     */
    @Schema(description = "副标题")
    private String subTitle;
    /**
     * 头图
     */
    @Schema(description = "头图")
    private String headImg;
    /**
     * 引导视频
     */
    @Schema(description = "引导视频")
    private String guidingVideo;


    /**
     * 引导音频
     */
    @Schema(description = "引导语音")
    private String guidingAudio;
}
