package cn.jojo.front.jaguar.adaptor.service;

import cn.jojo.edu.fantasy.rpc.api.dto.EduTopicDto;
import cn.jojo.front.jaguar.common.bo.EduTopicBo;
import cn.jojo.front.jaguar.common.pojo.req.EduTopicPageQueryRpcReq;
import cn.jojo.infra.sdk.api.metadata.IPageResp;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

/**
 * <AUTHOR>
 * @date 2023/3/31 10:36
 * @desc
 */
public interface IEduTopicRpcClient {

    /**
     * 分页查询专题接口
     *
     * @param rpcReq 查询参数
     * @return 分页列表
     */
    Page<EduTopicBo> queryTopicPage(EduTopicPageQueryRpcReq rpcReq);

}
