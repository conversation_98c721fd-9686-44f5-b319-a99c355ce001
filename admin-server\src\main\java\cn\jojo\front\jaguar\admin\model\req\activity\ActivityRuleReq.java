package cn.jojo.front.jaguar.admin.model.req.activity;

import cn.jojo.front.jaguar.common.enums.task.ActivityRuleTimeTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ActivityRuleReq {

    @Schema(description = "规则时间定义类型", implementation = ActivityRuleTimeTypeEnum.class)
    private Integer ruleTimeType;

    @Schema(description = "活动自定义规则")
    private ActivityRuleCustomActivityTimeReq activityRuleCustomActivityTime;

    @Schema(description = "活动时间规则")
    private ActivityRuleActivityTimeReq activityRuleActivityTime;

    @Schema(description = "预告时间规则")
    private ActivityRuleAdvanceTimeReq activityRuleAdvanceTime;

    @Schema(description = "通用活动时间")
    private ActivityRuleCommonActivityTimeReq activityRuleCommonActivityTime;

    @Schema(description = "自然时间规则")
    private ActivityRuleNaturalTimeReq activityRuleNaturalTime;

}
