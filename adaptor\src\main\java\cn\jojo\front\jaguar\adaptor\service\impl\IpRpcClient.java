package cn.jojo.front.jaguar.adaptor.service.impl;

import cn.jojo.infra.rpc.ip.IpResponse;
import cn.jojo.infra.rpc.ip.IpService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * 2025/3/17 13:36
 */
@Service
@Slf4j
public class IpRpcClient {
    @DubboReference
    private IpService ipService;

    public IpResponse ip2Region(String ip) {
        if (ip == null) {
            return null;
        }
        try {
            return ipService.ip2Region(ip);
        } catch (Exception e) {
            log.error("invoke ipService.ip2Region failed", e);
            return null;
        }
    }
}
