package cn.jojo.front.jaguar.admin.controller;

import cn.jojo.front.jaguar.common.pojo.bo.EmployeeBo;
import cn.jojo.front.jaguar.common.pojo.entity.tab.HomeTabBubble;
import cn.jojo.front.jaguar.common.pojo.req.HomeTabBubbleListReq;
import cn.jojo.front.jaguar.common.pojo.req.HomeTabBubbleSaveReq;
import cn.jojo.front.jaguar.common.pojo.req.PriorityUpdateReq;
import cn.jojo.front.jaguar.common.pojo.vo.HomeTabBubbleDetailVo;
import cn.jojo.front.jaguar.common.pojo.vo.HomeTabBubbleListVo;
import cn.jojo.front.jaguar.core.service.tab.HomeTabBubbleService;
import cn.jojo.infra.sdk.api.metadata.DefaultHttpResult;
import cn.jojo.infra.sdk.api.metadata.DefaultPageResp;
import cn.jojo.infra.sdk.api.metadata.IHttpResult;
import cn.jojo.infra.sdk.api.metadata.IPageResp;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import java.util.Collections;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.constraints.Positive;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/admin/bubble")
@Tag(name = "Jaguar管理后台")
@Validated
public class HomeTabBubbleController extends BaseController {

    @Resource
    private HomeTabBubbleService homeTabBubbleService;

    @GetMapping("/list")
    @Operation(summary ="bubble列表",   description = "获取bubble列表")
    public IPageResp<HomeTabBubbleListVo> listBubbles(@Validated HomeTabBubbleListReq req) {
        IPage<HomeTabBubbleListVo> page = homeTabBubbleService.listHomeTabBubblePage(req);
        return DefaultPageResp.buildPageResp(req.getPageNum(), req.getPageSize(), page.getTotal(), page.getRecords());
    }

    @PostMapping("/updateBubblePriority")
    @Operation(summary ="bubble优先级",   description = "更新bubble优先级")
    public IHttpResult<Boolean> updateBubblePriority(@RequestBody @Validated PriorityUpdateReq req) {
        HomeTabBubble bubble = homeTabBubbleService.updatePriority(req);
        if (bubble != null) {
            homeTabBubbleService.deleteCache(Collections.singletonList(bubble));
        }

        return DefaultHttpResult.successWithData(true);
    }

    @GetMapping("/bubbleInfo")
    @Operation(summary ="bubble详情",   description = "获取bubble详情")
    public IHttpResult<HomeTabBubbleDetailVo> getBubbleInfo(@RequestParam("id") @Positive Integer id) {
        List<HomeTabBubbleDetailVo> detailList = homeTabBubbleService.listBubbleDetailByIds(Collections.singleton(id));
        return DefaultHttpResult.successWithData(detailList.stream().findFirst().orElse(null));
    }

    @PostMapping("/saveBubble")
    @Operation(summary ="Bubble保存",   description = "保存Bubble")
    public IHttpResult<Integer> saveBubble(@RequestBody @Validated HomeTabBubbleSaveReq req) {
        HomeTabBubble bubble = homeTabBubbleService.saveOrUpdate(req,
            new EmployeeBo().setEmployeeId(getEmployeeId()).setEmployeeName(getEmployeeName()));
        homeTabBubbleService.deleteCache(Collections.singletonList(bubble));
        return DefaultHttpResult.successWithData(bubble.getId());
    }

    @GetMapping("/deleteBubble")
    @Operation(summary ="删除bubble",   description = "删除bubble")
    public IHttpResult<Boolean> deleteBubble(@RequestParam("id") @Positive Integer id) {
        HomeTabBubble bubble = homeTabBubbleService.deleteBubble(id);
        homeTabBubbleService.deleteCache(Collections.singletonList(bubble));
        return DefaultHttpResult.successWithData(true);
    }
}
