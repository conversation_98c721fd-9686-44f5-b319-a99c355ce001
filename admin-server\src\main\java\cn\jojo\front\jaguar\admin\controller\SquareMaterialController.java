package cn.jojo.front.jaguar.admin.controller;

import cn.jojo.common.api.ex.DefaultHttpActionPageResult;
import cn.jojo.common.api.ex.DefaultHttpActionResult;
import cn.jojo.common.api.ex.IHttpActionResult;
import cn.jojo.front.jaguar.common.pojo.req.SquareMaterialCopyReq;
import cn.jojo.front.jaguar.common.pojo.req.SquareMaterialListReq;
import cn.jojo.front.jaguar.common.pojo.req.SquareMaterialMoveReq;
import cn.jojo.front.jaguar.common.pojo.req.SquareMaterialUpdateReq;
import cn.jojo.front.jaguar.common.pojo.vo.SquareMaterialVo;
import cn.jojo.front.jaguar.core.service.square.SquareMaterialService;
import cn.jojo.infra.sdk.api.metadata.IPageResp;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/admin/square/material")
@Tag(name = "Jaguar管理后台")
@Validated
public class SquareMaterialController {

    @Resource
    private SquareMaterialService squareMaterialService;

    @GetMapping("/list")
    @Operation(summary ="广场素材列表",   description = "广场素材列表")
    public IHttpActionResult<IPageResp<SquareMaterialVo>> listSquareMaterial(SquareMaterialListReq req) {
        IPage<SquareMaterialVo> page = squareMaterialService.listSquareMaterial(req);
        return DefaultHttpActionPageResult
            .successWithPageData(page.getCurrent(), page.getSize(), page.getTotal(), page.getRecords());
    }

    @PostMapping("/update")
    @Operation(summary ="更新广场素材",   description = "更新广场素材")
    public IHttpActionResult<Boolean> updateSquareMaterial(@RequestBody @Validated SquareMaterialUpdateReq req) {
        req.setPageId(req.getPageId() == null ? 1L : req.getPageId());
        squareMaterialService.updateSquareMaterial(req);
        return DefaultHttpActionResult.successWithData(true);
    }

    @GetMapping("/move")
    @Operation(summary ="移动广场素材",   description = "移动广场素材")
    public IHttpActionResult<Boolean> moveSquareMaterial(SquareMaterialMoveReq req) {
        req.setPageId(req.getPageId() == null ? 1L : req.getPageId());
        return DefaultHttpActionResult.successWithData(squareMaterialService.move(req));
    }

    @PostMapping("/copying")
    public IHttpActionResult<Boolean> copy(@RequestBody @Validated SquareMaterialCopyReq req) {
        boolean result = squareMaterialService.copy(req);
        return DefaultHttpActionResult.successWithData(result);
    }
}
