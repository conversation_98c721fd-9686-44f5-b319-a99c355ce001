package cn.jojo.front.jaguar.adaptor.service;

import cn.jojo.front.jaguar.common.pojo.req.ChannelNoDimensionListPageReq;
import cn.jojo.front.jaguar.common.pojo.req.ChannelNoEnumListPageReq;
import cn.jojo.front.jaguar.common.pojo.req.OperateChannelDimensionEnumRpcReq;
import cn.jojo.front.jaguar.common.pojo.resp.CreateResPositionEnumResp;
import cn.tinman.sharedservices.mall.channel.api.request.BatchCreateChannelNoReq;
import cn.tinman.sharedservices.mall.channel.api.response.BatchCreateChannelNoResp;
import cn.tinman.sharedservices.mall.channel.api.response.DimensionListPageResp;
import cn.tinman.sharedservices.mall.channel.api.response.EnumListPageResp;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/7/3 13:38
 */
public interface IMallChannelRpcClient {

    /**
     * 创建资源位置枚举
     *
     * @param req
     * @return
     */
    Optional<CreateResPositionEnumResp> createChannelDimensionEnum(OperateChannelDimensionEnumRpcReq req);


    /**
     * 更新资源位置枚举
     *
     * @param req
     * @return
     */
    Optional<CreateResPositionEnumResp> updateChannelDimensionEnum(OperateChannelDimensionEnumRpcReq req);

    /**
     * 删除资源位
     *
     * @param req
     * @return
     */
    Optional<CreateResPositionEnumResp> deleteChannelDimensionEnum(OperateChannelDimensionEnumRpcReq req);

    /**
     * 批量创建渠道号
     * @param req
     * @return
     */
    Optional<BatchCreateChannelNoResp> batchCreateChannelNumber(BatchCreateChannelNoReq req);

    /**
     * 获取渠道号维度列表
     * @param req
     * @return
     */
    Optional<DimensionListPageResp> fetchDimensionListPage(ChannelNoDimensionListPageReq req);

    /**
     * 获取渠道号枚举列表
     * @param req
     * @return
     */
    Optional<EnumListPageResp> fetchEnumListPage(ChannelNoEnumListPageReq req);
}
