package cn.jojo.front.jaguar.adaptor.service.impl;

import cn.jojo.infra.sdk.api.metadata.IRpcResult;
import cn.jojo.infra.sdk.logging.JoJoLogging;
import cn.jojo.uc.api.domain.request.ParentChildRelationReq;
import cn.jojo.uc.api.service.ParentChildRelationRpcService;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * 2024/8/28 14:05
 */
@Service
@Slf4j
public class ParentChildRelationRpcClient {
    @DubboReference
    private ParentChildRelationRpcService parentChildRelationRpcService;

    public boolean existRelation(Long parentId, Long childId) {
        if (parentId == 0L || childId == 0L) {
            return Boolean.FALSE;
        }
        try {
            ParentChildRelationReq req = new ParentChildRelationReq();
            req.setParentUserId(parentId);
            req.setChildUserId(childId);
            IRpcResult<Boolean> rpcResult = parentChildRelationRpcService.existRelation(req);
            JoJoLogging.logger(log)
                .unIndex("req", JSON.toJSONString(req))
                .unIndex("rpcResult", JSON.toJSONString(rpcResult))
                .info("ParentChildRelationService.existRelation, parentUserId:{}, childUserId:{}",
                    parentId, childId);
            if (!rpcResult.checkSuccess()) {
                JoJoLogging.logger(log)
                    .error("ParentChildRelationService.existRelation 查询失败, parentUserId:{}, childUserId:{}",
                        parentId, childId);
                return Boolean.FALSE;
            }
            return rpcResult.getData();
        } catch (Exception e) {
            JoJoLogging.logger(log)
                .error("ParentChildRelationService.existRelation 查询异常 parentUserId:{}, childUserId:{}",
                    parentId, childId, e);
        }
        return Boolean.FALSE;
    }
}
