package cn.jojo.front.jaguar.adaptor.service;

import cn.jojo.front.jaguar.common.enums.ResourceStatus;
import cn.jojo.front.jaguar.common.pojo.bo.*;
import cn.jojo.front.jaguar.common.pojo.req.EduHubRpcReq;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;


/**
 * @Description: 专辑相关rpc接口
 * @Copyright: Copyright (c) 2022  ALL RIGHTS RESERVED.
 * @Company: 书声科技有限公司
 * @Author: chenHao
 * @CreateDate: 2022/9/30 15:43
 * @UpdateDate: 2022/9/30 15:43
 * @UpdateRemark: init
 * @Version: 1.0
 */
public interface IEduHubRpcClient {

    /**
     * 获取专辑库信息
     *
     * @param hubIds   专辑库id
     * @param typeList 类型
     * @param status   资源状态
     * @return 专辑库聚合信息
     */
    List<EduHubAggregationBo> queryHubExtension(List<Long> hubIds, List<Integer> typeList, ResourceStatus status);

    /**
     * 查询专辑库，专辑，资源的层次数据关系
     *
     * @param hubIds       专辑库ID集合
     * @param isAlbumLevel 是否只到专辑层
     * @return 专辑库，专辑，资源关系数据
     */
    List<EduHubRelationBo> queryHubRelation(List<Long> hubIds, Boolean isAlbumLevel);

    /**
     * 根据hubId批量查询信息
     *
     * @param hubIds hubId
     * @return 信息列表
     */
    List<HubBo> queryHubsByHubIds(List<Long> hubIds);

    /**
     * 模糊查询hub相关专辑库数据信息
     *
     * @param hubId       专辑库id
     * @param hubName     专辑库名称
     * @param contentType 内容类型
     * @param pageNum     頁碼
     * @param pageSize    页数据量
     * @return 信息列表
     */
    Page<HubBo> getHubsByFuzzyQuery(String contentType, Long hubId, String hubName,
                                    Integer pageNum, Long pageSize);

    /**
     * 获取hub的更新时间
     *
     * @param hubIds hubid列表
     * @return 更新时间
     */
    List<HubContentUpdateBo> getContentUpdateHubs(List<Long> hubIds);


    /**
     * 获取伴读首页推荐模块推荐资源
     * @return 推荐资源列表
     */
    List<EduHubPageBo> getPlateRecommendAllHub();


    /**
     * 根据courseId查询hub信息
     *
     * @param rpcReq 请求参数
     * @return List<HubBo> 专辑bo
     */
    List<HubBo> queryHubs(EduHubRpcReq rpcReq);
}
