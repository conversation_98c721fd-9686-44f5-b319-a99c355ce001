package cn.jojo.front.jaguar.adaptor.service.impl;

import cn.jojo.uc.common.domain.dto.CommonRespDto;
import cn.jojo.uc.api.domain.dto.UserBabyDto;
import cn.jojo.uc.api.enums.UserServiceErrorStatus;
import cn.jojo.uc.api.service.UserBabyService;
import cn.jojo.front.jaguar.common.pojo.bo.UserBabyBo;
import cn.jojo.front.jaguar.common.utils.ApolloUtil;
import cn.jojo.front.jaguar.common.utils.OssUrlUtils;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Date;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class UserBabyRpcClientTest {

    @InjectMocks
    private UserBabyRpcClient userBabyRpcClient;

    @Mock
    private UserBabyService userBabyService;

    // 测试用例1：正常获取数据
    @Test
    void getUserBabyInfoWithSensitive_NormalCase_ShouldReturnProcessedData() {
        // Mock远程服务返回
        UserBabyDto mockDto = new UserBabyDto()
                .setAvatar("test-avatar")
                .setAvatarUrl("test-url")
                .setNickname("宝宝")
                .setBirthday(new Date())
                .setGrade(2);

        CommonRespDto<UserBabyDto, UserServiceErrorStatus> mockResp = mock(CommonRespDto.class);
        when(mockResp.withoutData()).thenReturn(false);
        when(mockResp.getData()).thenReturn(mockDto);
        when(userBabyService.getUserBabyInfoWithSensitive(any())).thenReturn(mockResp);

        // 执行测试
        UserBabyBo result = userBabyRpcClient.getUserBabyInfoWithSensitive(1L, true);

        // 验证结果
        assertEquals("test-avatar", result.getAvatar());
        assertEquals(OssUrlUtils.genFileUrl("test-url"), result.getAvatarUrl());
        assertEquals("宝宝", result.getNickname());
    }

    // 测试用例2：远程返回空数据
    @Test
    void getUserBabyInfoWithSensitive_WhenNoData_ShouldGenerateDefault() {
        // Mock静态方法
        try (MockedStatic<ApolloUtil> apolloMock = mockStatic(ApolloUtil.class)) {
            apolloMock.when(() -> ApolloUtil.getStringMessage("biz.baby.default.name"))
                    .thenReturn("测试宝宝");

            // Mock空响应
            CommonRespDto<UserBabyDto, UserServiceErrorStatus> mockResp = mock(CommonRespDto.class);
            when(mockResp.withoutData()).thenReturn(true);
            when(userBabyService.getUserBabyInfoWithSensitive(any())).thenReturn(mockResp);

            // 执行测试
            UserBabyBo result = userBabyRpcClient.getUserBabyInfoWithSensitive(1L, false);

            // 验证默认数据
            assertEquals("测试宝宝", result.getNickname());
            assertEquals(UserBabyRpcClient.BABY_DEFAULT_BOY_AVATAR_LINK, result.getAvatar());
            assertEquals(-1, result.getGrade());
        }
    }

    // 测试用例3：远程服务异常
    @Test
    void getUserBabyInfoWithSensitive_WhenServiceException_ShouldGenerateDefault() {
        // Mock静态方法
        try (MockedStatic<ApolloUtil> apolloMock = mockStatic(ApolloUtil.class)) {
            apolloMock.when(() -> ApolloUtil.getStringMessage("biz.baby.default.name"))
                    .thenReturn("异常宝宝");

            // Mock异常
            when(userBabyService.getUserBabyInfoWithSensitive(any()))
                    .thenThrow(new RuntimeException("服务异常"));

            // 执行测试
            UserBabyBo result = userBabyRpcClient.getUserBabyInfoWithSensitive(1L, true);

            // 验证默认数据
            assertEquals("异常宝宝", result.getNickname());
            assertNotNull(result.getBirthday());
            assertEquals(1, result.getSex());
        }
    }

    // 测试用例4：处理空avatar字段
    @Test
    void getUserBabyInfoWithSensitive_WhenAvatarEmpty_ShouldSetDefault() {
        // Mock返回数据
        UserBabyDto mockDto = new UserBabyDto().setAvatar(null);
        CommonRespDto<UserBabyDto, UserServiceErrorStatus> mockResp = mock(CommonRespDto.class);
        when(mockResp.withoutData()).thenReturn(false);
        when(mockResp.getData()).thenReturn(mockDto);
        when(userBabyService.getUserBabyInfoWithSensitive(any())).thenReturn(mockResp);

        // 执行测试
        UserBabyBo result = userBabyRpcClient.getUserBabyInfoWithSensitive(1L, true);

        // 验证默认头像设置
        assertEquals(UserBabyRpcClient.BABY_DEFAULT_BOY_AVATAR_LINK, result.getAvatar());
    }

    // 测试用例5：处理空avatarUrl字段
    @Test
    void getUserBabyInfoWithSensitive_WhenAvatarUrlEmpty_ShouldSetDefault() {
        // Mock返回数据
        UserBabyDto mockDto = new UserBabyDto().setAvatarUrl(null);
        CommonRespDto<UserBabyDto, UserServiceErrorStatus> mockResp = mock(CommonRespDto.class);
        when(mockResp.withoutData()).thenReturn(false);
        when(mockResp.getData()).thenReturn(mockDto);
        when(userBabyService.getUserBabyInfoWithSensitive(any())).thenReturn(mockResp);

        // 执行测试
        UserBabyBo result = userBabyRpcClient.getUserBabyInfoWithSensitive(1L, true);

        // 验证URL处理和默认设置
        String expectedUrl = OssUrlUtils.genFileUrl(UserBabyRpcClient.BABY_DEFAULT_BOY_AVATAR_LINK);
        assertEquals(expectedUrl, result.getAvatarUrl());
    }
}
