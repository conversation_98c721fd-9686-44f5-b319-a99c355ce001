package cn.jojo.front.jaguar.admin.model.dto.task;

import cn.jojo.front.jaguar.common.enums.task.MedalRarityEnum;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @description 分页dto
 * @date 2024/03/11
 **/

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema(description="勋章pageDto")
public class MedalPageDto {
    @Schema(description="id")
    private Long id;
    @Schema(description="称号")
    private String title;
    @Schema(description="稀有度",implementation = MedalRarityEnum.class)
    private Integer rarity;
    @Schema(description="稀有度名称")
    private String rarityName;
    @Schema(description="创建时间")
    private Long createTime;
    @Schema(description="创建人id")
    private Long createUserId;
    @Schema(description="创建人名称")
    private String createUserName;

    @Schema(description="未解锁图片")
    private String unlockedImage;

    @Schema(description="科目")
    private String subjectType;
    @Schema(description="科目名称")
    private String subjectName;
    @Schema(description="阶段")
    private Integer courseSegmentCode;
    @Schema(description = "阶段名称")
    private String courseSegmentName;
    @Schema(description="勋章类型：0 不可升级 1 可升级")
    private Integer type;
    @Schema(description="备注")
    private String remark;
    @Schema(description="次序")
    private Integer groupSort;

    @Schema(description="操作人")
    private Long updateUserId;
    @Schema(description="操作人名称")
    private String updateUserName;
    @Schema(description="更新时间")
    private Long updateTime;
}
