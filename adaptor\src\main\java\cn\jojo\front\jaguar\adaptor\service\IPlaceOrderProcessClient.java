package cn.jojo.front.jaguar.adaptor.service;

import cn.jojo.front.jaguar.common.pojo.bo.PlaceOrderParam;
import cn.jojo.front.jaguar.common.pojo.bo.PlaceOrderResultBo;
import cn.tinman.sharedservices.mall.cashback.api.campaign.response.CampaignInfoResp;
import cn.tinman.sharedservices.mall.cashback.api.campaign.response.CampaignLinkResp;
import cn.tinman.sharedservices.mall.cashback.api.campaign.response.MaterielResp;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 */
public interface IPlaceOrderProcessClient {

    /**
     * 获取活动信息
     *
     * @param linkId 跳转id
     * @param ip
     * @return optional
     */
    Optional<CampaignInfoResp> getCampaignInfo(Long linkId, String ip);

    /**
     * 获取领课链接，通过领课ID
     *
     * @param linkId 领课id
     * @return optional
     */
    Optional<CampaignLinkResp> getCampaignInfo(Long linkId);

    /**
     * 免费课程下单
     *
     * @param param 参数
     * @return 订单号
     */
    Optional<String> placeOrder(PlaceOrderParam param);

    /**
     * 免费课程下单 包含的信息更完整
     *
     * @param param 参数
     * @return 完整订单信息
     */
    Optional<PlaceOrderResultBo> placeOrderV2(PlaceOrderParam param);

    /**
     * 根据活动id获取物料信息
     *
     * @param campaignId 活动id
     * @return 物料信息
     */
    List<MaterielResp> getBycampaignId(Long campaignId);

}
