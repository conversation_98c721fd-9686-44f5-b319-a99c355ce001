package cn.jojo.front.jaguar.admin.controller;

import cn.jojo.front.jaguar.common.pojo.bo.EmployeeBo;
import cn.jojo.front.jaguar.common.pojo.entity.banner.Banner;
import cn.jojo.front.jaguar.common.pojo.req.BannerSaveReq;
import cn.jojo.front.jaguar.common.pojo.req.BaseListReq;
import cn.jojo.front.jaguar.common.pojo.req.PriorityUpdateReq;
import cn.jojo.front.jaguar.common.pojo.vo.BannerDetailVo;
import cn.jojo.front.jaguar.common.pojo.vo.BannerListVo;
import cn.jojo.front.jaguar.core.service.banner.BannerService;
import cn.jojo.infra.sdk.api.metadata.DefaultHttpResult;
import cn.jojo.infra.sdk.api.metadata.DefaultPageResp;
import cn.jojo.infra.sdk.api.metadata.IHttpResult;
import cn.jojo.infra.sdk.api.metadata.IPageResp;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import java.util.Collections;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.constraints.Positive;

import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/admin/banner")
@Tag(name = "Jaguar管理后台")
@Validated
public class BannerController extends BaseController {

    @Resource
    private BannerService bannerService;

    @GetMapping("/getBannerList")
    @Operation(summary ="banner列表",   description = "获取banner列表")
    public IPageResp<BannerListVo> getBannerList(BaseListReq<Void> req) {
        IPage<BannerListVo> page = bannerService.listBanner(req);
        return DefaultPageResp.buildPageResp(req, page.getTotal(), page.getRecords());
    }

    @PostMapping("/updateBannerPriority")
    @Operation(summary ="banner优先级",   description = "更新banner优先级")
    public IHttpResult<Boolean> updateBannerPriority(@RequestBody @Validated PriorityUpdateReq req) {
        bannerService.updatePriority(req);
        bannerService.deleteCache(Collections.singleton(req.getBusinessId()));
        return DefaultHttpResult.successWithData(true);
    }

    @GetMapping("/getBannerInfo")
    @Operation(summary ="banner详情",   description = "获取banner详情")
    public IHttpResult<BannerDetailVo> getBannerInfo(@RequestParam("bannerId") @Positive Integer bannerId) {
        List<BannerDetailVo> result = bannerService.listBannerDetailByIds(Collections.singleton(bannerId));
        return DefaultHttpResult.successWithData(CollectionUtils.isEmpty(result) ? null : result.get(0));
    }

    @PostMapping("saveBanner")
    @Operation(summary ="banner保存",   description = "保存banner")
    public IHttpResult<Integer> saveBanner(@RequestBody @Validated BannerSaveReq req) {
        Banner banner = bannerService.saveOrUpdateBanner(req, new EmployeeBo()
            .setEmployeeId(getEmployeeId())
            .setEmployeeName(getEmployeeName()));
        bannerService.deleteCache(Collections.singleton(banner.getId()));
        return DefaultHttpResult.successWithData(banner.getId());
    }

    @Operation(summary ="删除banner",   description = "删除banner")
    @GetMapping("/deleteBanner")
    public IHttpResult<Boolean> deleteBanner(@RequestParam @Positive Integer bannerId) {
        bannerService.deleteBanner(bannerId, new EmployeeBo()
            .setEmployeeId(getEmployeeId())
            .setEmployeeName(getEmployeeName()));
        bannerService.deleteCache(Collections.singleton(bannerId));
        return DefaultHttpResult.successWithData(true);
    }
}
