package cn.jojo.front.jaguar.admin.controller;

import cn.jojo.common.api.ex.DefaultHttpActionPageResult;
import cn.jojo.common.api.ex.DefaultHttpActionResult;
import cn.jojo.common.api.ex.IHttpActionResult;
import cn.jojo.front.jaguar.common.pojo.bo.PageBo;
import cn.jojo.front.jaguar.common.pojo.req.UserGroupMoveReq;
import cn.jojo.front.jaguar.common.pojo.req.UserGroupPageListReq;
import cn.jojo.front.jaguar.common.pojo.req.UserGroupSaveReq;
import cn.jojo.front.jaguar.common.pojo.vo.PageLocationVo;
import cn.jojo.front.jaguar.common.pojo.vo.UserGroupingDetailVo;
import cn.jojo.front.jaguar.common.pojo.vo.UserGroupingListVo;
import cn.jojo.front.jaguar.common.pojo.vo.UserGroupingPageListVo;
import cn.jojo.front.jaguar.core.service.usergroup.UserGroupingService;
import cn.jojo.infra.sdk.api.metadata.DefaultHttpResult;
import cn.jojo.infra.sdk.api.metadata.IHttpResult;
import cn.jojo.infra.sdk.api.metadata.IPageResp;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.List;

@RestController
@RequestMapping("/admin/user/grouping")
@Tag(name = "Jaguar管理后台")
public class UserGroupingController {

    @Resource
    private UserGroupingService userGroupingService;

    @GetMapping("/list")
    @Operation(summary ="人群分群列表",   description = "人群页面列表")
    public IHttpActionResult<IPageResp<UserGroupingPageListVo>> list(@Validated UserGroupPageListReq req) {
        PageBo<UserGroupingPageListVo> page = userGroupingService.listUserGroup(req);
        return DefaultHttpActionPageResult
            .successWithPageData(page.getPageNum(), page.getPageSize(), page.getTotal(), page.getData());
    }

    @GetMapping("/detail")
    @Operation(summary ="群体页面下详情",   description = "群体页面下详情")
    public IHttpActionResult<UserGroupingDetailVo> getDetail(@NotNull(message = "id is null") Long id) {
        return DefaultHttpActionPageResult.successWithData(userGroupingService.getDetail(id));
    }

    @PostMapping("/save")
    @Operation(summary ="人群群体保存",   description = "人群群体保存")
    public IHttpActionResult<Long> save(@Validated @RequestBody UserGroupSaveReq req) {
        return DefaultHttpActionResult.successWithData(userGroupingService.saveOrUpdate(req));
    }

    @GetMapping("/delete")
    @Operation(summary ="人群群体删除",   description = "人群群体删除")
    public IHttpActionResult<Boolean> delete(@NotNull(message = "id is null") Long id) {
        return DefaultHttpActionResult.successWithData(userGroupingService.delete(id));
    }

    @GetMapping("/listSections")
    @Operation(summary ="群体下，所有板块类型列表",   description = "获取板块类型列表")
    public IHttpResult<List<UserGroupingListVo>> listSections() {
        return DefaultHttpResult.successWithData(userGroupingService.getSectionList());
    }

    @GetMapping("/move")
    @Operation(summary ="移动广场群体",   description = "移动广场群体")
    public IHttpActionResult<Boolean> moveSquareMaterial(UserGroupMoveReq req) {
        req.setGroupId(req.getGroupId() == null ? 1L : req.getGroupId());
        return DefaultHttpActionResult.successWithData(userGroupingService.move(req));
    }

    @GetMapping("/pageLocation")
    @Operation(summary ="页面位置下拉",   description = "页面位置下拉")
    public IHttpActionResult<List<PageLocationVo>> getPageLocation() {
        return DefaultHttpActionResult.successWithData(userGroupingService.getPageLocationList());
    }

}
