package cn.jojo.front.jaguar.adaptor.service.impl;

import cn.jojo.front.jaguar.adaptor.model.req.ObjectStorageUrlCheckReq;
import cn.jojo.front.jaguar.adaptor.service.ObjectStorageAdaptorService;
import cn.jojo.front.jaguar.common.exception.BusinessException;
import cn.jojo.front.jaguar.common.utils.ApolloUtil;
import cn.jojo.front.jaguar.common.utils.JacksonUtil;
import cn.jojo.infra.bs.ds.open.objectstorage.BatchConsistencyCheckDto;
import cn.jojo.infra.sdk.api.constants.ApiResultPlatformCodeConstants;
import cn.jojo.infra.sdk.api.metadata.DefaultHttpResult;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.google.common.collect.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 描述: 存储服务 http://confluence.console.tinman.cn/pages/viewpage.action?pageId=51975792
 * <p>
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">yangxx</a>
 * @version : Ver 1.0
 * @date : 2020-04-30 18:49
 */
@Service
public class ObjectStorageAdaptorServiceImpl implements ObjectStorageAdaptorService {

    private static final Logger logger = LoggerFactory.getLogger(ObjectStorageAdaptorServiceImpl.class);
    /**
     * 生成单个上传token接口
     */
    private static final String GEN_UPLOAD_TOKEN_PATH = "/api/objectStorage/genUploadToken";

    /**
     * 生成批量上传token接口
     */
    private static final String GEN_BATCH_UPLOAD_TOKEN_PATH = "/api/objectStorage/genBatchUploadToken";

    /**
     * 校验地址路径
     */
    private static final String CHECK_CONSISTENCY_PATH = "/api/objectStorage/checkConsistency";

    /**
     * 对象存储服务域名
     */
    @Value("${svc.bs-ds-api-server}")
    private String host;

    private static final String CODE_STR = " code: ";

    @Autowired
    private RestTemplate restTemplate;

    @Override
    public String genUploadToken(String genTokenReq) {
        String url = String.format("%s%s", host, GEN_UPLOAD_TOKEN_PATH);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<String> entity = new HttpEntity<>(genTokenReq, headers);
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, entity, String.class);

        if (responseEntity.getStatusCode().value() != HttpStatus.OK.value()) {
            String error = String.format(ApolloUtil.getStringMessage("error.upload.token.error"), url);
            error += CODE_STR + responseEntity.getStatusCode().toString();
            throw BusinessException.exception(error);
        }
        return responseEntity.getBody();
    }

    @Override
    public String genBatchUploadToken(String genBatchUploadToken) {
        String url = String.format("%s%s", host, GEN_BATCH_UPLOAD_TOKEN_PATH);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<String> entity = new HttpEntity<>(genBatchUploadToken, headers);
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, entity, String.class);

        if (responseEntity.getStatusCode().value() != HttpStatus.OK.value()) {
            String error = String.format(ApolloUtil.getStringMessage("error.upload.token.error"), url);
            error += CODE_STR + responseEntity.getStatusCode().toString();
            throw BusinessException.exception(error);
        }
        return responseEntity.getBody();
    }

    @Override
    public Boolean checkConsistency(String objectStorageUrl) {
        if (StringUtils.isEmpty(objectStorageUrl)) {
            throw BusinessException.paramException(ApolloUtil.getStringMessage("error.upload.check.url.null"));
        }

        String url = String.format("%s%s", host, CHECK_CONSISTENCY_PATH);

        Map<String, Boolean> result = check(url, null, Lists.newArrayList(objectStorageUrl));
        return result.get(objectStorageUrl);
    }

    @Override
    public Map<String, Boolean> checkConsistency(List<String> objectStorageUrlList) {
        if (CollectionUtils.isEmpty(objectStorageUrlList)) {
            throw BusinessException.paramException(ApolloUtil.getStringMessage("error.upload.check.url.null"));
        }

        String url = String.format("%s%s", host, CHECK_CONSISTENCY_PATH);

        return check(url, null, objectStorageUrlList);
    }

    @Override
    public String checkConsistencyOut(String objectStorageUrlPrefix, List<String> objectStorageUrlList) {
        String url = String.format("%s%s", host, CHECK_CONSISTENCY_PATH);

        ObjectStorageUrlCheckReq objectStorageUrlCheckReq = new ObjectStorageUrlCheckReq();
        objectStorageUrlCheckReq.setObjectStorageUrlPrefix(objectStorageUrlPrefix);
        objectStorageUrlCheckReq.setObjectStorageUrlList(objectStorageUrlList);

        String reqJson;
        try {
            reqJson = JacksonUtil.getInstance().writeValueAsString(objectStorageUrlCheckReq);
        } catch (JsonProcessingException e) {
            logger.error(e.getMessage(), e);
            throw BusinessException.exception(ApolloUtil.getStringMessage("error.upload.request.error"));
        }

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<String> entity = new HttpEntity<>(reqJson, headers);
        ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, entity, String.class);

        if (responseEntity.getStatusCode().value() != HttpStatus.OK.value()) {
            String error = String.format(ApolloUtil.getStringMessage("error.upload.check.fail"), url);
            error += CODE_STR + responseEntity.getStatusCode().toString();
            throw BusinessException.exception(error);
        }
        return responseEntity.getBody();
    }

    /**
     * 调用存储服务校验文件完整性
     *
     * @param url                    请求URL
     * @param objectStorageUrlPrefix 存储对象前缀
     * @param objectStorageUrlList   需校验的URL
     * @return 结果
     */
    private Map<String, Boolean> check(String url, String objectStorageUrlPrefix, List<String> objectStorageUrlList) {
        ObjectStorageUrlCheckReq objectStorageUrlCheckReq = new ObjectStorageUrlCheckReq();
        objectStorageUrlCheckReq.setObjectStorageUrlPrefix(objectStorageUrlPrefix);
        objectStorageUrlCheckReq.setObjectStorageUrlList(objectStorageUrlList);

        String req;
        try {
            req = JacksonUtil.getInstance().writeValueAsString(objectStorageUrlCheckReq);
        } catch (JsonProcessingException e) {
            logger.error(e.getMessage(), e);
            throw BusinessException.exception(ApolloUtil.getStringMessage("error.upload.request.error"));
        }

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<String> entity = new HttpEntity<>(req, headers);

        ResponseEntity<DefaultHttpResult<BatchConsistencyCheckDto>> responseEntity = restTemplate.exchange(url,
            HttpMethod.POST, entity, new ParameterizedTypeReference<DefaultHttpResult<BatchConsistencyCheckDto>>() {
            });

        if (responseEntity.getStatusCode().value() != HttpStatus.OK.value()) {
            String error = String.format(ApolloUtil.getStringMessage("error.upload.check.fail"), url);
            error += CODE_STR + responseEntity.getStatusCode().toString();
            throw BusinessException.exception(error);
        }

        DefaultHttpResult<BatchConsistencyCheckDto> result = responseEntity.getBody();
        if (result == null || !ApiResultPlatformCodeConstants.SUCCESS.getCode().equals(result.getCode())) {
            logger.error("上传文件校验完整性失败:{},url:{},req:{}", result == null ? "" : result.getMessage(), url, req);
            throw BusinessException
                .exception(String.format(ApolloUtil.getStringMessage("error.upload.check2.fail"),
                    result == null ? "" : result.getMessage()));
        }

        BatchConsistencyCheckDto checkRespList = result.getData();
        return checkRespList.getCheckRespList().stream()
            .collect(Collectors.toMap(BatchConsistencyCheckDto.CheckResp::getObjectStorageUrl,
                BatchConsistencyCheckDto.CheckResp::getChecksumMatch));
    }

}
