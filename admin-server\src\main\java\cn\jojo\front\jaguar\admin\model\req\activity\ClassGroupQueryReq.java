package cn.jojo.front.jaguar.admin.model.req.activity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2024/07/03
 * @Version 1.0
 * @Description
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description="班期数据查询")
public class ClassGroupQueryReq {

    /**
     * 课程ID
     */
    @Schema(description="课程ID")
    @NotNull(message = "courseId must not null")
    private Long courseId;

}
