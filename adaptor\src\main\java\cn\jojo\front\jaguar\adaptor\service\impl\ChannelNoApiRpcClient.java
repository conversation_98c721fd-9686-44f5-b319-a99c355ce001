package cn.jojo.front.jaguar.adaptor.service.impl;

import cn.jojo.front.jaguar.common.pojo.bo.ChannelNoDimensionBo;
import cn.jojo.front.jaguar.common.pojo.bo.ChannelNoItemBo;
import cn.jojo.front.jaguar.common.pojo.bo.PageBo;
import cn.jojo.infra.sdk.api.metadata.IRpcResult;
import cn.tinman.sharedservices.mall.channel.api.request.ChannelNosReq;
import cn.tinman.sharedservices.mall.channel.api.request.ListChannelNoReq;
import cn.tinman.sharedservices.mall.channel.api.response.ChannelNosResp;
import cn.tinman.sharedservices.mall.channel.api.response.ListChannelNoResp;
import cn.tinman.sharedservices.mall.channel.api.service.IListChannelNoApiService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 2025/3/18 14:44
 */
@Service
@Slf4j
public class ChannelNoApiRpcClient {
    @DubboReference
    private IListChannelNoApiService listChannelNoApiService;

    public PageBo<ChannelNoItemBo> listChannelNoPage(List<String> channelNos, String fuzzyQuery, Integer pageNum, Integer pageSize) {
        ListChannelNoReq req = ListChannelNoReq.builder()
            .channelNos(channelNos)
            .fuzzyQuery(fuzzyQuery)
            .pageNum(pageNum)
            .pageSize(pageSize)
            .build();
        try {
            IRpcResult<ListChannelNoResp> rpcResult = listChannelNoApiService.listV2(req);
            if (!rpcResult.checkSuccess()) {
                log.error("invoke listChannelNoApiService.listV2 failed, message = {}", rpcResult.getMessage());
                return new PageBo<>();
            }
            return convert(rpcResult.getData());
        } catch (Exception e) {
            log.error("listChannelNoPage error", e);
            return PageBo.emptyPage();
        }
    }

    public List<ChannelNoItemBo> listByChannelNos(List<String> channelNos, boolean showDeleted) {
        if (CollectionUtils.isEmpty(channelNos)) {
            return Collections.emptyList();
        }
        List<ChannelNosReq.Item> channelList = channelNos.stream()
            .map(i -> ChannelNosReq.Item.builder().channelNo(i).build())
            .collect(Collectors.toList());
        ChannelNosReq req = ChannelNosReq.builder().channelNos(channelList).showIfDeleted(showDeleted).build();
        try {
            IRpcResult<ChannelNosResp> rpcResult = listChannelNoApiService.list(req);
            if (!rpcResult.checkSuccess()) {
                log.error("invoke listChannelNoApiService.list failed, message = {}", rpcResult.getMessage());
                return Collections.emptyList();
            }
            return convert(rpcResult.getData());
        } catch (Exception e) {
            log.error("invoke listChannelNoApiService.list", e);
        }
        return Collections.emptyList();
    }

    private List<ChannelNoItemBo> convert(ChannelNosResp data) {
        if (data == null) {
            return Collections.emptyList();
        }
        return Optional.ofNullable(data.getList())
            .orElse(Collections.emptyList())
            .stream()
            .map(i -> {
                // 这里代码重复是因为商城的Dimension对象是两个不同的对象，但是字段差不多，所以转换代码类似
                List<ChannelNoDimensionBo> dimensions = Optional.ofNullable(i.getDimensions())
                    .orElse(Collections.emptyList())
                    .stream()
                    .map(j -> new ChannelNoDimensionBo()
                        .setDimensionId(j.getDimensionId())
                        .setDimensionName(j.getDimensionName())
                        .setEnumId(j.getEnumId())
                        .setEnumName(j.getEnumName())
                        .setEnumSign(j.getEnumSign())
                        .setSourceFrom(j.getSourceFrom()))
                    .collect(Collectors.toList());
                return new ChannelNoItemBo()
                    .setChannelNo(i.getChannelNo())
                    .setVersion(String.valueOf(i.getVersion()))
                    .setDimensions(dimensions)
                    .setChannelNoEnums(i.getChannelNoEnums())
                    .setDeleted(i.getDeleted())
                    .setEnable(i.getEnable());
            }).collect(Collectors.toList());
    }

    private PageBo<ChannelNoItemBo> convert(ListChannelNoResp data) {
        if (data == null) {
            return PageBo.emptyPage();
        }
        List<ChannelNoItemBo> list = Optional.ofNullable(data.getData())
            .orElse(Collections.emptyList())
            .stream()
            .map(i -> {
                List<ChannelNoDimensionBo> dimensions = Optional.ofNullable(i.getDimensions())
                    .orElse(Collections.emptyList())
                    .stream()
                    .map(j -> new ChannelNoDimensionBo()
                        .setDimensionId(j.getDimensionId())
                        .setDimensionName(j.getDimensionName())
                        .setEnumId(j.getEnumId())
                        .setEnumName(j.getEnumName())
                        .setEnumSign(j.getEnumSign())
                        .setSourceFrom(j.getSourceFrom()))
                    .collect(Collectors.toList());
                    return new ChannelNoItemBo()
                        .setChannelNo(i.getChannelNo())
                        .setVersion(i.getVersion())
                        .setSimpleVersion(i.getSimpleVersion())
                        .setDimensions(dimensions)
                        .setChannelNoEnums(i.getChannelNoEnums())
                        .setResourceLevelId(i.getResourceLevelId());
            }).collect(Collectors.toList());
        return new PageBo<ChannelNoItemBo>()
            .setData(list)
            .setTotal(data.getTotalRow() == null ? 0 : Long.valueOf(data.getTotalRow()))
            .setPageNum(data.getCurrentPage() == null ? 1 : Long.valueOf(data.getCurrentPage()))
            .setPageSize(data.getPageSize() == null ? 10 : Long.valueOf(data.getPageSize()));
    }
}
