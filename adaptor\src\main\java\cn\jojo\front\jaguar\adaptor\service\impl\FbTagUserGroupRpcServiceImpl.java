package cn.jojo.front.jaguar.adaptor.service.impl;

import cn.jojo.fb.tag.rpc.api.domain.dto.FbTagUserGroupListDto;
import cn.jojo.fb.tag.rpc.api.domain.req.FbTagUserGroupListQueryReq;
import cn.jojo.fb.tag.rpc.api.domain.req.FbTagUserGroupSaveReq;
import cn.jojo.fb.tag.rpc.api.service.UserGroupRpcService;
import cn.jojo.front.jaguar.adaptor.service.FbTagUserGroupRpcService;
import cn.jojo.front.jaguar.common.enums.UserGroupType;
import cn.jojo.front.jaguar.common.pojo.bo.PageResultBo;
import cn.jojo.front.jaguar.common.pojo.req.FbUserGroupListQueryReq;
import cn.jojo.front.jaguar.common.pojo.req.FbUserGroupSaveReq;
import cn.jojo.front.jaguar.common.pojo.vo.FbTagUserGroupListVo;
import cn.jojo.infra.sdk.api.metadata.IPageResp;
import cn.jojo.infra.sdk.api.metadata.IRpcResult;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class FbTagUserGroupRpcServiceImpl implements FbTagUserGroupRpcService {

    @DubboReference
    private UserGroupRpcService userGroupRpcService;

    @Override
    public PageResultBo<FbTagUserGroupListVo> listUserGroupPages(FbUserGroupListQueryReq req, Long employeeId) {
        FbTagUserGroupListQueryReq query = new FbTagUserGroupListQueryReq();
        BeanUtils.copyProperties(req, query);
        query.setCurrentUserId(employeeId);
        IRpcResult<IPageResp<FbTagUserGroupListDto>> rpcResult = userGroupRpcService.listUserGroupPages(query);
        if (!rpcResult.checkSuccess()) {
            log.error("failed invoke userGroupRpcService.listUserGroupPages, message={}", rpcResult.getMessage());
            return new PageResultBo<>();
        }
        return Optional.ofNullable(rpcResult.getData())
                .map(item -> {
                    List<FbTagUserGroupListVo> dataList = Optional.ofNullable(item.getPageRecords()).orElse(
                            Collections.emptyList())
                            .stream()
                            .map(i -> {
                                FbTagUserGroupListVo vo = new FbTagUserGroupListVo();
                                BeanUtils.copyProperties(i, vo);
                                vo.setGroupName(StringUtils.join(i.getGroupName(),
                                    "(", UserGroupType.of(i.getGroupType(), UserGroupType.COMMON).getName() ,")"));
                                return vo;
                            }).collect(Collectors.toList());
                    return new PageResultBo<FbTagUserGroupListVo>()
                            .setPageNum(item.getPageNum())
                            .setPageSize(item.getPageSize())
                            .setRecords(dataList)
                            .setTotal(item.getTotalCount());
                }).orElseGet(PageResultBo::new);
    }

    @Override
    public boolean updateUserGroupStatus(Long id, String status) {
        IRpcResult<Boolean> rpcResult = userGroupRpcService.updateUserGroupStatus(id, status);
        if (!rpcResult.checkSuccess()) {
            log.error("invoke userGroupRpcService.updateUserGroupStatus failed, message = {}", rpcResult.getMessage());
            return false;
        }
        return Optional.ofNullable(rpcResult.getData()).orElse(false);
    }

    @Override
    public boolean addUserGroup(FbUserGroupSaveReq req) {
        FbTagUserGroupSaveReq save = new FbTagUserGroupSaveReq();
        BeanUtils.copyProperties(req, save);
        IRpcResult<Boolean> rpcResult = userGroupRpcService.addUserGroup(save);
        if (!rpcResult.checkSuccess()) {
            log.error("invoke userGroupRpcService.addUserGroup failed, message = {}", rpcResult.getMessage());
            return false;
        }
        return Optional.ofNullable(rpcResult.getData()).orElse(false);
    }

    @Override
    public Map<Long, Boolean> matchUserGroup(List<Long> groupIds, Long userId) {
        if (CollectionUtils.isEmpty(groupIds) || userId == null) {
            return Collections.emptyMap();
        }
        IRpcResult<Map<Long, Boolean>> rpcResult = userGroupRpcService.getUserGroupMatchResult(groupIds, userId);
        if (!rpcResult.checkSuccess()) {
            log.error("invoke userGroupRpcService.getUserGroupMatchResult failed, message={}", rpcResult.getMessage());
            return Collections.emptyMap();
        }
        return Optional.ofNullable(rpcResult.getData()).orElse(Collections.emptyMap());
    }

    @Override
    public Map<Long, String> getGroupNames(List<Long> groupIds) {
        if (CollectionUtils.isEmpty(groupIds)) {
            return Collections.emptyMap();
        }
        IRpcResult<Map<Long, String>> rpcResult = userGroupRpcService.getUserGroupNames(groupIds);
        if (!rpcResult.checkSuccess()) {
            log.error("invoke userGroupRpcService.getUserGroupNames failed, message={}", rpcResult.getMessage());
            return Collections.emptyMap();
        }
        return Optional.ofNullable(rpcResult.getData()).orElse(Collections.emptyMap());
    }
}
