package cn.jojo.front.jaguar.admin.controller;

import cn.jojo.common.api.ex.DefaultHttpActionPageResult;
import cn.jojo.common.api.ex.DefaultHttpActionResult;
import cn.jojo.common.api.ex.IHttpActionResult;
import cn.jojo.front.jaguar.common.pojo.SquarePlateListReq;
import cn.jojo.front.jaguar.common.pojo.req.SquareConfigurationSaveReq;
import cn.jojo.front.jaguar.common.pojo.vo.SquareConfigurationInfoVo;
import cn.jojo.front.jaguar.common.pojo.vo.SquareConfigurationListVo;
import cn.jojo.front.jaguar.common.pojo.req.CommonUpdateReq;
import cn.jojo.front.jaguar.core.service.square.SquareConfigurationService;
import cn.jojo.infra.sdk.api.metadata.IPageResp;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.Collections;

/**
 * <AUTHOR>
 * 2024/6/19 16:42
 * <AUTHOR>
 */
@RestController
@RequestMapping(value = "/admin/square-configurations")
@Tag(name = "Jaguar管理后台")
@Validated
public class SquareConfigurationControllerV2 {

    @Resource
    private SquareConfigurationService squareConfigurationService;

    @PatchMapping("/{id}")
    @Operation(summary ="更新广场配置",   description = "更新广场配置部分信息")
    public IHttpActionResult<Boolean> update(@Validated @RequestBody CommonUpdateReq req, @PathVariable("id") Integer id) {
        squareConfigurationService.update(id, req);
        return DefaultHttpActionResult.successWithData(true);
    }

    @GetMapping
    @Operation(summary = "广场配置列表", description = "广场配置列表")
    public IHttpActionResult<IPageResp<SquareConfigurationListVo>> getSquareConfigurationList(
        @RequestParam(value = "materialId", required = false) @Parameter(description = "素材id") Integer materialId,
        SquarePlateListReq req) {
        IPage<SquareConfigurationListVo> pageResult = squareConfigurationService
            .listSquareConfiguration(materialId, req);
        return DefaultHttpActionPageResult
            .successWithPageData(pageResult.getCurrent(), pageResult.getSize(), pageResult.getTotal(),
                pageResult.getRecords());
    }

    @GetMapping("/{id}")
    @Operation(summary = "广场配置详情", description = "广场配置详情")
    public IHttpActionResult<SquareConfigurationInfoVo> getSquareConfigurationDetail(@PathVariable(value = "id")
                                                                                     @NotNull(message = "id can not be null")
                                                                                     @Parameter(description = "广场配置id")
                                                                                     Integer id) {
        SquareConfigurationInfoVo result = squareConfigurationService
            .listSquareConfigurationDetailByIds(Collections.singleton(id)).stream().findFirst().orElse(null);
        return DefaultHttpActionResult.successWithData(result);
    }

    @PostMapping
    @Operation(summary = "新增广场配置信息", description = "新增广场配置信息")
    public IHttpActionResult<Boolean> save(
        @RequestBody @Validated SquareConfigurationSaveReq req) {
        squareConfigurationService.saveSquareConfiguration(req);
        return DefaultHttpActionResult.successWithData(true);
    }

    @PutMapping("/{id}")
    @Operation(summary = "更新广场配置信息", description = "更新广场配置信息")
    public IHttpActionResult<Boolean> saveOrUpdate(
        @PathVariable(value = "id")
        @NotNull(message = "id can not be null")
        @Parameter(description = "广场配置id")
        Integer id,
        @RequestBody @Validated SquareConfigurationSaveReq req) {
        req.setId(id);
        squareConfigurationService.saveSquareConfiguration(req);
        return DefaultHttpActionResult.successWithData(true);
    }

}
