package cn.jojo.front.jaguar.adaptor.service;


import cn.jojo.edu.malacca.rpc.api.dto.EduUserCourseExtensionDto;
import cn.jojo.edu.malacca.rpc.api.req.EduUserCourseExtensionReq;
import cn.jojo.edu.malacca.rpc.api.req.EduUserCourseQueryReq;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface IEduUserCourseRpcClient {
    /**
     * 查询所有的课程扩展信息
     * @param req
     * @return
     */
    List<EduUserCourseExtensionDto> queryAllExtension(EduUserCourseExtensionReq<EduUserCourseQueryReq> req);

    /**
     * 激活课程
     * @param userId  用户ID
     * @param orderNo 订单信息
     */
    void initOrActivate(Long userId, Long orderNo);
}
