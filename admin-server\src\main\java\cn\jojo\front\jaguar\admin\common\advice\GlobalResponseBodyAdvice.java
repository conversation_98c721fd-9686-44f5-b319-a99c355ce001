package cn.jojo.front.jaguar.admin.common.advice;

import cn.jojo.common.api.ex.IHttpActionResult;
import cn.jojo.front.jaguar.admin.constants.AdminConstants;
import cn.jojo.front.jaguar.common.exception.BusinessException;
import cn.jojo.front.jaguar.common.exception.SystemException;
import cn.jojo.front.jaguar.common.utils.JacksonUtil;
import cn.jojo.infra.sdk.api.constants.ApiResultPlatformCodeConstants;
import cn.jojo.infra.sdk.api.metadata.DefaultHttpResult;
import cn.jojo.infra.sdk.api.metadata.DefaultPageResp;
import cn.jojo.infra.sdk.api.metadata.IHttpResult;
import cn.jojo.infra.sdk.api.metadata.IPageResp;
import com.fasterxml.jackson.core.JsonProcessingException;
import org.apache.commons.lang3.ArrayUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.MethodParameter;
import org.springframework.http.MediaType;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

/**
 * @Description: 统一返回值处理
 * <AUTHOR>
 * @Date 2:37 下午 2020/7/1
 * @return
 **/
@ControllerAdvice(basePackages = "cn.jojo.front.jaguar.admin.controller")
public class GlobalResponseBodyAdvice implements ResponseBodyAdvice<Object> {

    private static final Logger logger = LoggerFactory.getLogger(GlobalResponseBodyAdvice.class);

    private static final String[] WHITE_LIST_PATH = {AdminConstants.API_PATH_PREFIX + "/admin/common/genUploadToken",
        AdminConstants.API_PATH_PREFIX + "/admin/common/genBatchUploadToken",
        AdminConstants.API_PATH_PREFIX + "/admin/common/checkConsistency"};

    @Override
    public boolean supports(MethodParameter returnType, Class converterType) {
        return true;
    }

    @SuppressWarnings({"unchecked"})
    @Override
    public Object beforeBodyWrite(Object body, MethodParameter returnType, MediaType selectedContentType,
        Class selectedConverterType, ServerHttpRequest request, ServerHttpResponse response) {
        String requestPath = request.getURI().getPath();
        if (ArrayUtils.contains(WHITE_LIST_PATH, requestPath)) {
            logger.info("GDX-DEBUG request path:{} in response body advice white list.", requestPath);
            return body;
        }
        // 返回值空值处理
        if (body == null) {
            DefaultHttpResult<Object> httpResult = new DefaultHttpResult<>();
            httpResult.setCode(ApiResultPlatformCodeConstants.SUCCESS.getCode());
            httpResult.setMessage(ApiResultPlatformCodeConstants.SUCCESS.getMessage());
            httpResult.setData(null);
            return httpResult;
        }
        // 兼容Rpc返回值规范
        if (body instanceof IPageResp) {
            IPageResp<Object> pageResp = (IPageResp<Object>) body;
            DefaultHttpResult<IPageResp<Object>> httpResult = new DefaultHttpResult<>();
            httpResult.setCode(ApiResultPlatformCodeConstants.SUCCESS.getCode());
            httpResult.setMessage(ApiResultPlatformCodeConstants.SUCCESS.getMessage());
            httpResult.setData(DefaultPageResp.buildPageResp(pageResp.getPageNum(), pageResp.getPageSize(),
                pageResp.getTotalCount(), pageResp.getPageRecords()));
            return httpResult;
        } // 兼容之前APi规范老代码
        else if (body instanceof IHttpResult) {
            return body;
        } else if (body instanceof IHttpActionResult) {
            return body;
        } else {
            // 将新返回值统一封装到api规范中
            IHttpResult<Object> objectHttpResult = DefaultHttpResult.successWithData(body);
            if (body instanceof String) {
                // String需要特殊处理，SpringMVC会根据返回接口参数为对象时将当前返回值当成String处理，会出现强转异常
                try {
                    return JacksonUtil.getInstance().writeValueAsString(objectHttpResult);
                } catch (JsonProcessingException e) {
                    throw new SystemException("Conversion string result exception");
                }
            }
            return objectHttpResult;
        }
    }
}
