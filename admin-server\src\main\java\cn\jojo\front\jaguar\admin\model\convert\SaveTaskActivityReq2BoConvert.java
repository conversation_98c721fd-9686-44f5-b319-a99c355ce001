package cn.jojo.front.jaguar.admin.model.convert;

import cn.jojo.edu.common.utils.convert.BaseModelConvert;
import cn.jojo.front.jaguar.admin.model.req.activity.SaveTaskActivityReq;
import cn.jojo.front.jaguar.biz.service.pojo.bo.activity.TaskActivityBo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 下拉选查询req 转vo
 *
 * <AUTHOR>
 * @date 2022/05/16
 */
@Mapper
public interface SaveTaskActivityReq2BoConvert extends BaseModelConvert<SaveTaskActivityReq, TaskActivityBo> {

    SaveTaskActivityReq2BoConvert INSTANCE = Mappers.getMapper(SaveTaskActivityReq2BoConvert.class);
}
