package cn.jojo.front.jaguar.adaptor.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.jojo.cc.api.service.BasicService;
import cn.jojo.cc.common.dto.DictDto;
import cn.jojo.cc.common.dto.SubjectTypeDto;
import cn.jojo.front.jaguar.adaptor.service.BasicServiceClient;
import cn.jojo.infra.sdk.api.metadata.IRpcResult;
import cn.jojo.uc.common.exception.BusinessServiceException;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class BasicServiceClientImpl implements BasicServiceClient {
    @DubboReference
    private BasicService basicService;

    @Override
    public List<DictDto> getDictDtoList(String dictType) {
        List<DictDto> dictDtoList = Collections.emptyList();
        try {
            IRpcResult<List<DictDto>> result = basicService.getDictInfoByTypeByTenant(dictType);
            if (Objects.isNull(result) || !result.checkSuccess() || CollUtil.isEmpty(result.getData())) {
                log.warn("调用课程中心获取课程阶段列表失败");
                return dictDtoList;
            }
            dictDtoList = result.getData();
        } catch (Exception e) {
            log.warn("调用课程中心获取课程阶段列表失败:{}",e.getMessage());
        }
        return dictDtoList;

    }

    @Override
    public List<SubjectTypeDto> getAllSubjectType() {

        try {
            IRpcResult<List<SubjectTypeDto>> result = basicService.getAllSubjectType();
            if (!result.checkSuccess()) {
                throw new BusinessServiceException("BasicServiceClient.getAllSubjectType query rpc failed");
            }

            return result.getData();

        } catch (Exception e) {
            log.error("BasicServiceClient.getAllSubjectType failed", e);
            throw new BusinessServiceException("BasicServiceClient.getAllSubjectType failed" );
        }
    }

    @Override
    public List<SubjectTypeDto> listCourseSubjectByTenant() {
        try {
            IRpcResult<List<SubjectTypeDto>> result = basicService.getAllSubjectTypeByTenant();
            if (!result.checkSuccess()) {
                throw new BusinessServiceException("BasicServiceClient.getAllSubjectTypeByTenant query rpc failed");
            }

            return result.getData();

        } catch (Exception e) {
            log.error("BasicServiceClient.getAllSubjectTypeByTenant failed", e);
            throw new BusinessServiceException("BasicServiceClient.getAllSubjectType failed");
        }
    }
}
