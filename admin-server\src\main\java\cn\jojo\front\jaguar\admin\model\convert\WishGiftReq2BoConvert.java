package cn.jojo.front.jaguar.admin.model.convert;

import cn.jojo.edu.common.utils.convert.BaseModelConvert;
import cn.jojo.front.jaguar.admin.model.req.activity.WishGiftReq;
import cn.jojo.front.jaguar.biz.service.pojo.bo.activity.WishGiftBo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 下拉选查询req 转vo
 *
 * <AUTHOR>
 * @date 2022/05/16
 */
@Mapper
public interface WishGiftReq2BoConvert extends BaseModelConvert<WishGiftReq, WishGiftBo> {

    WishGiftReq2BoConvert INSTANCE = Mappers.getMapper(WishGiftReq2BoConvert.class);
}
