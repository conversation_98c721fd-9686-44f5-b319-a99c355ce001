package cn.jojo.front.jaguar.adaptor.service.impl;


import cn.jojo.edu.study.rpc.api.dto.UserLessonRecordFinshDto;
import cn.jojo.edu.study.rpc.api.req.UserStudyRecordCacheQueryReq;
import cn.jojo.edu.study.rpc.api.service.EduUserStudyRecordService;
import cn.jojo.front.jaguar.adaptor.service.IUserLessonRecordRpcClient;
import cn.jojo.front.jaguar.common.pojo.req.UserStudyRecordCacheQueryRpcReq;
import cn.jojo.infra.sdk.api.metadata.IRpcResult;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户内容学习记录 RPC Client 实现
 *
 * <AUTHOR> Date: 2020/9/21 Time: 14:44 Description: No Description
 */

@Slf4j
@Component
public class UserLessonRecordRpcClient implements IUserLessonRecordRpcClient {

    @DubboReference
    private EduUserStudyRecordService userStudyRecordService;


    @Override
    public List<Long> queryFinishLessonIdsFromCache(UserStudyRecordCacheQueryRpcReq rpcReq) {
        if (CollectionUtils.isEmpty(rpcReq.getUserIds()) || CollectionUtils.isEmpty(rpcReq.getClassIds())) {
            return Collections.emptyList();
        }
        try {
            UserStudyRecordCacheQueryReq queryReq = new UserStudyRecordCacheQueryReq();
            queryReq.setUserIds(rpcReq.getUserIds());
            queryReq.setClassIds(rpcReq.getClassIds());
            queryReq.setLessonIds(rpcReq.getLessonIds());
            IRpcResult<List<UserLessonRecordFinshDto>> rpcResult = userStudyRecordService.
                queryLessonRecordFromCache(queryReq);
            if (!rpcResult.checkSuccess()) {
                log.error("调用 userStudyRecordService.queryLessonRecordFromCache 出现异常，req=[{}]",
                    JSON.toJSONString(queryReq));
                return Collections.emptyList();
            }
            if (CollectionUtils.isEmpty(rpcResult.getData())) {
                return Collections.emptyList();
            }
            return rpcResult.getData().stream().map(UserLessonRecordFinshDto::getLessonId).distinct()
                .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("调用 userStudyRecordService.queryLessonRecordFromCache 出现异常", e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<UserLessonRecordFinshDto> queryFinishRecordFromCache(UserStudyRecordCacheQueryRpcReq rpcReq) {
        if (CollectionUtils.isEmpty(rpcReq.getUserIds()) || CollectionUtils.isEmpty(rpcReq.getClassIds())) {
            return Collections.emptyList();
        }
        try {
            UserStudyRecordCacheQueryReq queryReq = new UserStudyRecordCacheQueryReq();
            queryReq.setUserIds(rpcReq.getUserIds());
            queryReq.setClassIds(rpcReq.getClassIds());
            queryReq.setLessonIds(rpcReq.getLessonIds());
            IRpcResult<List<UserLessonRecordFinshDto>> rpcResult = userStudyRecordService.
                queryLessonRecordFromCache(queryReq);
            if (!rpcResult.checkSuccess()) {
                log.error("调用 userStudyRecordService.queryLessonRecordFromCache 出现异常，req=[{}]",
                    JSON.toJSONString(queryReq));
                return Collections.emptyList();
            }
            if (CollectionUtils.isEmpty(rpcResult.getData())) {
                return Collections.emptyList();
            }
            return rpcResult.getData();
        } catch (Exception e) {
            log.error("调用 userStudyRecordService.queryLessonRecordFromCache 出现异常", e);
            return Collections.emptyList();
        }
    }
}
