package cn.jojo.front.jaguar.admin.model.convert;

import cn.jojo.edu.common.utils.convert.BaseModelConvert;
import cn.jojo.front.jaguar.admin.model.req.task.StockSaveReq;
import cn.jojo.front.jaguar.common.pojo.vo.season.StockOperateVo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @date 2025/01/07
 **/
@Mapper
public interface StockSaveReq2OperateVoConvert extends BaseModelConvert<StockSaveReq, StockOperateVo> {
    StockSaveReq2OperateVoConvert INSTANCE = Mappers.getMapper(StockSaveReq2OperateVoConvert.class);
}
