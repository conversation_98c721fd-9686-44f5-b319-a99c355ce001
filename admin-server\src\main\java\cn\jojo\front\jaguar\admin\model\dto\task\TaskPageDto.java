package cn.jojo.front.jaguar.admin.model.dto.task;

import cn.jojo.front.jaguar.common.enums.task.CourseTypeEnum;
import cn.jojo.front.jaguar.common.enums.task.TaskType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description 分页dto
 * @date 2024/03/11
 **/

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema(description="任务分页查询参数")
public class TaskPageDto {

    @Schema(description="id")
    private Long id;

    @Schema(description="任务名称")
    private String name;

    @Schema(description="说明")
    private String description;

    @Schema(description="排序")
    private Integer sort;

    @Schema(description="任务类型",implementation = TaskType.class)
    private String taskType;
    private String taskTypeName;

    @Schema(description="品类类型")
    private Integer subjectType;

    @Schema(description="品类类型")
    private String subjectName;

    @Schema(description="计划id")
    private Long courseId;

    @Schema(description="计划名称")
    private String courseName;

    @Schema(description="备注")
    private String remark;

    private Long updateTime;

    private Long createUserId;

    private String createUserName;

    @Schema(description = "产品类型", implementation = CourseTypeEnum.class)
    private Integer courseType;
    @Schema(description = "产品类型描述", implementation = CourseTypeEnum.class)
    private String courseTypeName;
}
