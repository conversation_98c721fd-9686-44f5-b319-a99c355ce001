package cn.jojo.front.jaguar.adaptor.service.impl;

import cn.jojo.edu.malacca.rpc.api.dto.EduUserCourseExtensionDto;
import cn.jojo.edu.malacca.rpc.api.req.EduUserCourseExtensionReq;
import cn.jojo.edu.malacca.rpc.api.req.EduUserCourseOperateReq;
import cn.jojo.edu.malacca.rpc.api.req.EduUserCourseQueryReq;
import cn.jojo.edu.malacca.rpc.api.service.EduUserCourseOperateService;
import cn.jojo.edu.malacca.rpc.api.service.EduUserCourseRpcService;
import cn.jojo.front.jaguar.adaptor.service.IEduUserCourseRpcClient;
import cn.jojo.front.jaguar.common.exception.BusinessException;
import cn.jojo.front.jaguar.common.exception.SystemException;
import cn.jojo.infra.sdk.api.metadata.IPageResp;
import cn.jojo.infra.sdk.api.metadata.IRpcResult;
import cn.jojo.pagani.common.log.LogHelper;
import cn.tinman.clouds.jojoread.common.api.utils.CommonGsonUtil;
import com.alibaba.fastjson2.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class EduUserCourseRpcClient implements IEduUserCourseRpcClient {

    @DubboReference
    private EduUserCourseRpcService courseRpcService;

    @DubboReference(timeout = 3000)
    private EduUserCourseOperateService eduUserCourseOperateService;

    @Override
    public List<EduUserCourseExtensionDto> queryAllExtension(EduUserCourseExtensionReq<EduUserCourseQueryReq> req) {

        List<EduUserCourseExtensionDto> results = new ArrayList<>();
        if (Objects.isNull(req)) {
            return results;
        }
        IRpcResult<IPageResp<EduUserCourseExtensionDto>> result = null;

        try {
            result = courseRpcService.queryExtension(req);
            log.debug("queryAllExtension req:{} result:{}", JSON.toJSONString(req), JSON.toJSONString(result));
            if (Objects.isNull(result) || !result.checkSuccess()) {
                throw new SystemException("EduUserCourseRpcService.queryExtension fail");
            }
            results = Optional.ofNullable(result.getData()).map(IPageResp::getPageRecords).orElse(results);
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return results;
    }


    @Override
    public void initOrActivate(Long userId, Long orderNo) {
        EduUserCourseOperateReq operateReq = EduUserCourseOperateReq.builder()
            .userId(userId)
            .orderNo(orderNo).build();
        IRpcResult<Void> rpcResult = null;
        try {
            rpcResult = eduUserCourseOperateService.initOrActivate(operateReq);
        } catch (Exception e) {
            log.error("eduUserCourseOperateService.initOrActivate]初始化 或 初始化并激活课程异常，：{}", e.getMessage());
        }
        if (Objects.isNull(rpcResult) || !rpcResult.checkSuccess()) {
            throw new SystemException("eduUserCourseOperateService.initOrActivate fail");
        }
    }
}
