package cn.jojo.front.jaguar.admin.controller;

import cn.jojo.common.api.ex.DefaultHttpActionResult;
import cn.jojo.common.api.ex.IHttpActionResult;
import cn.jojo.front.jaguar.common.pojo.bo.PageBo;
import cn.jojo.front.jaguar.common.pojo.req.ReferenceConditionReq;
import cn.jojo.front.jaguar.common.pojo.req.ReferenceConditionSaveReq;
import cn.jojo.front.jaguar.common.pojo.vo.ReferenceConditionRelationVo;
import cn.jojo.front.jaguar.common.pojo.vo.ReferenceConditionVo;
import cn.jojo.front.jaguar.core.service.impl.referencecondition.ReferenceConditionService;
import cn.jojo.infra.sdk.api.metadata.DefaultPageResp;
import cn.jojo.infra.sdk.api.metadata.IPageResp;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * 2025/5/22 14:45
 */
@Validated
@RequestMapping("/admin")
@RestController
@Tag(name = "Jaguar管理后台")
public class ReferenceConditionController {
    @Resource
    private ReferenceConditionService referenceConditionService;

    @GetMapping("/reference-conditions")
    @Operation(summary = "分页查询引用规则列表", description = "通过分页参数以及过滤条件查询引用规则列表")
    public IPageResp<ReferenceConditionVo> page(ReferenceConditionReq req) {
        PageBo<ReferenceConditionVo> page = referenceConditionService.list(req);
        return DefaultPageResp.buildPageResp(page.getPageNum(), page.getPageSize(), page.getTotal(), page.getData());
    }

    @PostMapping("/reference-conditions")
    @Operation(summary = "保存引用规则", description = "保存引用规则")
    public IHttpActionResult<ReferenceConditionVo> save(@Validated @RequestBody ReferenceConditionSaveReq req) {
        ReferenceConditionVo vo = referenceConditionService.save(req);
        return DefaultHttpActionResult.successWithData(vo);
    }

    @PatchMapping("/reference-conditions/{id}")
    @Operation(summary = "更新引用规则", description = "更新引用规则")
    public IHttpActionResult<Void> update(@NotNull @PathVariable Long id) {
        referenceConditionService.delete(id);
        return DefaultHttpActionResult.successWithoutData();
    }

    @GetMapping("/reference-conditions/{id}/relations")
    @Operation(summary = "查询引用规则关联的规则组", description = "查询引用规则关联的规则组")
    public IHttpActionResult<ReferenceConditionRelationVo> relations(@NotNull @PathVariable Long id) {
        ReferenceConditionRelationVo relationVo = referenceConditionService.relations(id);
        return DefaultHttpActionResult.successWithData(relationVo);
    }
}
