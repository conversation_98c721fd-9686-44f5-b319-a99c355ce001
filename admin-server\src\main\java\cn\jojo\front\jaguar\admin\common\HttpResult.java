package cn.jojo.front.jaguar.admin.common;

import cn.jojo.infra.sdk.api.constants.ApiResultPlatformCodeConstants;
import cn.jojo.infra.sdk.api.metadata.DefaultHttpResult;
import cn.jojo.infra.sdk.api.metadata.IHttpResult;

/**
 * 描述: 错误异常描述
 * <p>
 *
 * <AUTHOR> <a href="mailto:<EMAIL>">yangxx</a>
 * @version : Ver 1.0
 * @date : 2020-04-24 15:58
 */
public class HttpResult<T> extends DefaultHttpResult<T> {

    /**
     *
     */
    private static final long serialVersionUID = 1901418367682140351L;

    /**
     * 参数错误异常，不带参数
     *
     * @param message 错误消息
     * @param <T>
     * @return
     */
    public static <T> IHttpResult<T> paramFailureResponse(String message) {
        return paramFailureResponse(message, null);
    }

    /**
     * 参数错误异常
     *
     * @return 参数错误
     */
    public static <T> IHttpResult<T> paramFailureResponse(String message, T t) {
        DefaultHttpResult<T> httpResult = new DefaultHttpResult<>();
        httpResult.setCode(ApiResultPlatformCodeConstants.PARAM_ERROR.getCode());
        httpResult.setMessage(message);
        httpResult.setData(t);
        return httpResult;
    }

    /**
     * 参数错误异常，不带数据
     *
     * @param message 错误消息
     * @param <T>
     * @return
     */
    public static <T> IHttpResult<T> bizFailureResponse(String message) {
        return paramFailureResponse(message, null);
    }

    /**
     * 参数错误异常，带响应数据
     *
     * @return 参数错误
     */
    public static <T> IHttpResult<T> bizFailureResponse(String message, T t) {
        DefaultHttpResult<T> httpResult = new DefaultHttpResult<>();
        httpResult.setCode(ApiResultPlatformCodeConstants.BIZ_ERROR.getCode());
        httpResult.setMessage(message);
        httpResult.setData(t);
        return httpResult;
    }
}
