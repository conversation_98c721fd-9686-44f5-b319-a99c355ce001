package cn.jojo.front.jaguar.admin.interceptor;

import java.util.ArrayList;
import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

/**
 * User: huangjing Email: <EMAIL> Date: 2016/12/7 Time: 17:25
 */

public class ApiInterceptor extends HandlerInterceptorAdapter {

    private static String allowHeader = "";
    private static List<String> allowHeaderList = new ArrayList<>();

    static {
        allowHeaderList.add("TM-UserAgent-deviceOS");
        allowHeaderList.add("TM-UserAgent-appChannel");
        allowHeaderList.add("TM-UserAgent-appIdentityVersion");
        allowHeaderList.add("TM-UserAgent-appBundleIdentifier");
        allowHeaderList.add("TM-UserAgent-appUserAuthToken");
        allowHeaderList.add("TM-UserAgent-productKey");
        allowHeaderList.add("TM-UserAgent-productVersion");
        allowHeaderList.add("TM-UserAgent-productSecretKey");
        allowHeaderList.add("TM-UserAgent-userTime");
        allowHeaderList.add("TM-UserAgent-deviceOSVersion");
        allowHeaderList.add("TM-UserAgent-deviceUniqueIdentifier");
        allowHeaderList.add("TM-UserAgent-deviceModel");

        // 后台文件上传接口跨域要求
        allowHeaderList.add("Content-Type");

        StringBuilder sb = new StringBuilder();
        sb.append("accept");
        for (String header : allowHeaderList) {
            sb.append(",");
            sb.append(header);
        }

        allowHeader = sb.toString();
    }

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
        throws Exception {
        response.setHeader("Access-Control-Allow-Origin", "*");
        response.setHeader("Access-Control-Allow-Methods", "GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS,TRACE,CONNECT");
        response.setHeader("Access-Control-Allow-Headers", allowHeader);
        response.setHeader("Access-Control-Max-Age", "1728000");
        response.setHeader("access-control-allow-credentials", "true");

        response.addHeader("vary", "Origin");
        response.addHeader("vary", "Access-Control-Request-Method");
        response.addHeader("vary", "Access-Control-Request-Headers");

        return true;
    }
}
