package cn.jojo.front.jaguar.adaptor.service;

import java.util.List;
import java.util.Map;

public interface ObjectStorageAdaptorService {

    /**
     * 调用底层对象存储服务获取上传token
     *
     * @param genTokenReq 上传token请求
     * @return 结果
     */
    String genUploadToken(String genTokenReq);

    /**
     * 获取批量上传token
     *
     * @param genBatchUploadToken 批量token请求
     * @return 结果
     */
    String genBatchUploadToken(String genBatchUploadToken);

    /**
     * 单个地址校验
     *
     * @param objectStorageUrl 需校验的地址
     * @return 结果, 通过/不通过
     */
    Boolean checkConsistency(String objectStorageUrl);

    /**
     * 多资源校验地址
     *
     * @param objectStorageUrlList 校验地址列表
     * @return 结果
     */
    Map<String, Boolean> checkConsistency(List<String> objectStorageUrlList);

    /**
     * 多资源校验地址(对外使用)
     *
     * @param objectStorageUrlPrefix 对象存储空间前缀
     * @param objectStorageUrlList   待校验URL地址列表
     * @return 结果
     */
    String checkConsistencyOut(String objectStorageUrlPrefix, List<String> objectStorageUrlList);
}
