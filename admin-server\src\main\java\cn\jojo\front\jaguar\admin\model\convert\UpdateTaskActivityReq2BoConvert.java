package cn.jojo.front.jaguar.admin.model.convert;

import cn.jojo.edu.common.utils.convert.BaseModelConvert;
import cn.jojo.front.jaguar.admin.model.req.activity.UpdateTaskActivityReq;
import cn.jojo.front.jaguar.biz.service.pojo.bo.activity.TaskActivityBo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 下拉选查询req 转vo
 *
 * <AUTHOR>
 * @date 2022/05/16
 */
@Mapper
public interface UpdateTaskActivityReq2BoConvert extends BaseModelConvert<UpdateTaskActivityReq, TaskActivityBo> {

    UpdateTaskActivityReq2BoConvert INSTANCE = Mappers.getMapper(UpdateTaskActivityReq2BoConvert.class);
}
