package cn.jojo.front.jaguar.admin.config;

import cn.jojo.front.jaguar.admin.interceptor.OperateInfoInterceptor;
import org.hibernate.validator.BaseHibernateValidatorConfiguration;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.validation.Validator;
import org.springframework.validation.beanvalidation.LocalValidatorFactoryBean;
import org.springframework.web.multipart.MultipartResolver;
import org.springframework.web.multipart.commons.CommonsMultipartResolver;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
public class WebMvcConfig implements WebMvcConfigurer {

    @Autowired
    private MessageSource messageSource;

    @Autowired
    private LocalValidatorFactoryBean localValidatorFactoryBean;
    @Autowired
    private OperateInfoInterceptor operateInfoInterceptor;

    @Bean(name = "multipartResolver")
    public MultipartResolver multipartResolver() {
        CommonsMultipartResolver resolver = new CommonsMultipartResolver();
        resolver.setDefaultEncoding("UTF-8");
        //resolveLazily属性启用是为了推迟文件解析，以在在UploadAction中捕获文件大小异常
        resolver.setResolveLazily(true);
        resolver.setMaxInMemorySize(40960);
        //单个上传文件大小 50M 50*1024*1024
        resolver.setMaxUploadSize(50 * 1024 * 1024);
        return resolver;
    }

    @Override
    public Validator getValidator() {
        //该行代码即指定了使用Spring的国际化配置来配置Hibernate-Validator框架的国际化信息
        localValidatorFactoryBean.setValidationMessageSource(messageSource);
        //该行代码是给Hibernate-Validator框架设置一些参数，比如下面一行设置校验快速失败模式，关于什么是快速失败，我在这里不详细展开，不懂的同学google即可
        localValidatorFactoryBean.getValidationPropertyMap().put(BaseHibernateValidatorConfiguration.FAIL_FAST, "true");
        return localValidatorFactoryBean;
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(operateInfoInterceptor).order(2);
    }
}
