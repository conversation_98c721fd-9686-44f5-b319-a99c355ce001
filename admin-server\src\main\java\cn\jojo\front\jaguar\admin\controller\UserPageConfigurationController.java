package cn.jojo.front.jaguar.admin.controller;

import cn.jojo.common.api.ex.DefaultHttpActionPageResult;
import cn.jojo.common.api.ex.DefaultHttpActionResult;
import cn.jojo.common.api.ex.IHttpActionResult;
import cn.jojo.front.jaguar.common.pojo.bo.PageBo;
import cn.jojo.front.jaguar.common.pojo.bo.UserPageSectionBo;
import cn.jojo.front.jaguar.common.pojo.req.PriorityUpdateReq;
import cn.jojo.front.jaguar.common.pojo.req.RuleStatusUpdateReq;
import cn.jojo.front.jaguar.common.pojo.req.UserPageConfigurationListReq;
import cn.jojo.front.jaguar.common.pojo.req.UserPageConfigurationSaveReq;
import cn.jojo.front.jaguar.common.pojo.vo.UserPageConfigurationDetailVo;
import cn.jojo.front.jaguar.common.pojo.vo.UserPageConfigurationListVo;
import cn.jojo.front.jaguar.core.service.userpage.UserPageConfigurationService;
import cn.jojo.infra.sdk.api.metadata.IPageResp;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("admin/user-page/configuration")
public class UserPageConfigurationController extends BaseController {

    @Resource
    private UserPageConfigurationService userPageConfigurationService;

    @GetMapping("/list")
    public IHttpActionResult<IPageResp<UserPageConfigurationListVo>> list(@Validated UserPageConfigurationListReq<Void> req) {
        PageBo<UserPageConfigurationListVo> pageInfo = userPageConfigurationService.pageList(req, getEmployeeId());
        return DefaultHttpActionPageResult
                .successWithPageData(pageInfo.getPageNum(), pageInfo.getPageSize(), pageInfo.getTotal(),
                        pageInfo.getData());
    }

    @GetMapping("/detail")
    @Operation(summary ="查询用户页面配置详情",   description = "查询用户页面配置详情")
    public IHttpActionResult<UserPageConfigurationDetailVo> detail(@NotNull(message = "id is null") Long id) {
        UserPageConfigurationDetailVo detail = userPageConfigurationService.getUserPageDetail(id);
        return DefaultHttpActionResult.successWithData(detail);
    }

    @PostMapping("/save")
    @Operation(summary ="保存用户页面配置",   description = "保存用户页面配置")
    public IHttpActionResult<Long> save(@RequestBody UserPageConfigurationSaveReq req) {
        Long id = userPageConfigurationService.saveOrUpdate(req, getEmployeeId(), getEmployeeName());
        return DefaultHttpActionResult.successWithData(id);
    }

    @GetMapping("update-priority")
    public IHttpActionResult<Boolean> updatePriority(@Validated PriorityUpdateReq req) {
        userPageConfigurationService.updatePriority(req);
        return DefaultHttpActionResult.successWithData(true);
    }

    @GetMapping("/all-sections")
    public IHttpActionResult<List<UserPageSectionBo>> getAllSections() {
        return DefaultHttpActionResult.successWithData(userPageConfigurationService.getAllSections(true));
    }

    @PostMapping("/status")
    public IHttpActionResult<Boolean> updateStatus(@Validated @RequestBody RuleStatusUpdateReq req) {
        boolean result = userPageConfigurationService.updateStatus(req);
        return DefaultHttpActionResult.successWithData(result);
    }
}
