package cn.jojo.front.jaguar.adaptor.service.impl;

import cn.jojo.front.jaguar.common.pojo.bo.PlatformOrderItemBo;
import cn.jojo.front.jaguar.common.utils.CommonUtil;
import cn.jojo.infra.sdk.api.metadata.IRpcResult;
import cn.tinman.sharedservices.mall.order.api.request.QueryPlatformItemReq;
import cn.tinman.sharedservices.mall.order.api.response.PlatformOrderItemResp;
import cn.tinman.sharedservices.mall.order.api.service.PlatformOrderReadApi;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 2025/2/24 14:57
 */
@Slf4j
@Service
public class PlatformOrderReadClient {
    @DubboReference
    private PlatformOrderReadApi platformOrderReadApi;

    public List<PlatformOrderItemBo> queryPlatformOrderItem(Long userId,
                                                            Integer bizType,
                                                            Integer useStatus,
                                                            String app) {
        QueryPlatformItemReq req = new QueryPlatformItemReq();
        req.setUserId(userId);
        req.setBizType(bizType);
        req.setUseStatus(useStatus);
        req.setApp(app);
        req.setSsTenantId(CommonUtil.getSsTenantId());
        try {
            IRpcResult<List<PlatformOrderItemResp>> rpcResult = platformOrderReadApi.queryPlatformOrderItem(req);
            if (!rpcResult.checkSuccess()) {
                log.error("invoke platformOrderReadApi.queryPlatformOrderItem failed, message={}",
                        rpcResult.getMessage());
                return Collections.emptyList();
            }
            return Optional.ofNullable(rpcResult.getData()).orElse(Collections.emptyList())
                .stream()
                .map(i -> new PlatformOrderItemBo()
                    .setId(i.getId())
                    .setProductId(i.getProductId())
                    .setSkuIds(i.getSkuIds())
                    .setBizId(i.getBizId())
                    .setBizType(i.getBizType())
                    .setAutoUse(i.getAutoUse())
                    .setCreateTime(i.getCreateTime())
                    .setMallOrderCreateTime(i.getMallOrderCreateTime())
                    .setFailCode(i.getFailCode())
                    .setFailReason(i.getFailReason())
                    .setChannelArgument(i.getChannelArgument())
                    .setChannelNo(i.getChannelNo())
                    .setChannelVersion(i.getChannelVersion())
                    .setNeedAddress(i.getNeedAddress()))
                .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("invoke platformOrderReadApi.queryPlatformOrderItem failed", e);
            return Collections.emptyList();
        }
    }
}
