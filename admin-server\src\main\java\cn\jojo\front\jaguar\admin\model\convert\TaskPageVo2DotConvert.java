package cn.jojo.front.jaguar.admin.model.convert;

import cn.jojo.edu.common.utils.convert.BaseModelConvert;
import cn.jojo.front.jaguar.admin.model.dto.task.TaskPageDto;
import cn.jojo.front.jaguar.biz.service.pojo.bo.task.TaskPageVo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * vo转换
 *
 * <AUTHOR>
 * @date 2022/05/16
 */
@Mapper
public interface TaskPageVo2DotConvert extends BaseModelConvert<TaskPageVo, TaskPageDto> {

    TaskPageVo2DotConvert INSTANCE = Mappers.getMapper(TaskPageVo2DotConvert.class);
}
