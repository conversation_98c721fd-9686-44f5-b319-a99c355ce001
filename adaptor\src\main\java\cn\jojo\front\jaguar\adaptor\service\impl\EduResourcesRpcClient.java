package cn.jojo.front.jaguar.adaptor.service.impl;

import cn.hutool.core.text.CharSequenceUtil;
import cn.jojo.edu.fantasy.common.dict.ResourcesStatus;
import cn.jojo.edu.fantasy.rpc.api.dto.EduEbookContentDto;
import cn.jojo.edu.fantasy.rpc.api.dto.EduEbookDto;
import cn.jojo.edu.fantasy.rpc.api.dto.EduResourcesAggregationDto;
import cn.jojo.edu.fantasy.rpc.api.dto.EduResourcesDto;
import cn.jojo.edu.fantasy.rpc.api.dto.EduResourcesPagingDto;
import cn.jojo.edu.fantasy.rpc.api.req.EduResourcesExtensionReq;
import cn.jojo.edu.fantasy.rpc.api.req.EduResourcesPagingReq;
import cn.jojo.edu.fantasy.rpc.api.req.EduResourcesQueryReq;
import cn.jojo.edu.fantasy.rpc.api.req.enums.EduResourcesAggregationType;
import cn.jojo.edu.fantasy.rpc.api.service.IEduResourcesRpcService;
import cn.jojo.front.jaguar.adaptor.service.IEduResourcesRpcClient;

import cn.jojo.front.jaguar.common.pojo.bo.DubFragmentBo;
import cn.jojo.front.jaguar.common.pojo.bo.DubResBo;
import cn.jojo.front.jaguar.common.pojo.bo.EduEbookResBo;
import cn.jojo.front.jaguar.common.pojo.bo.EduResourcesBo;
import cn.jojo.front.jaguar.common.pojo.bo.EduResourcesStoryBo;
import cn.jojo.front.jaguar.common.pojo.bo.EduStoryResBo;
import cn.jojo.front.jaguar.common.pojo.bo.ResourcesBo;
import cn.jojo.front.jaguar.common.utils.JsonBeanUtil;
import cn.jojo.infra.sdk.api.metadata.IPageResp;
import cn.jojo.infra.sdk.api.metadata.IRpcResult;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class EduResourcesRpcClient implements IEduResourcesRpcClient {

    @Reference
    private IEduResourcesRpcService eduResourcesRpcService;

    @Value("${res.ids.max.size:200}")
    private Integer resIdsMaxSize;


    @Override
    public List<EduResourcesAggregationDto> queryAggregation(
        EduResourcesExtensionReq req) {
        Function<EduResourcesExtensionReq, List<EduResourcesAggregationDto>> function = rpcReq -> {
            try {
                IRpcResult<List<EduResourcesAggregationDto>> rpcResult = eduResourcesRpcService.queryAggregation(rpcReq);
                if (!rpcResult.checkSuccess()) {
                    log.error("failed query eduResourcesRpcService.queryAggregation, message = {}",
                        rpcResult.getMessage());
                    return Collections.emptyList();
                }
                return rpcResult.getData();
            } catch (Exception e) {
                log.error("query eduResourcesRpcService.queryAggregation error{}", e.getMessage());
                return Collections.emptyList();
            }
        };
        // 当传了resIds参数时，需要分批调用接口
        if (req.getResourcesQueryReq() != null && CollectionUtils.isNotEmpty(req.getResourcesQueryReq().getResIds())) {
            List<Long> resIds = req.getResourcesQueryReq().getResIds();
            return Lists.partition(resIds, resIdsMaxSize).stream()
                .map(ids -> {
                    req.getResourcesQueryReq().setResIds(ids);
                    return function.apply(req);
                })
                .filter(CollectionUtils::isNotEmpty)
                .flatMap(List::stream)
                .collect(Collectors.toList());
        } else {
            return function.apply(req);
        }
    }

    @Override
    public Page<ResourcesBo> getResByFuzzyQuery(String contentType, Long albumId, Long resId, String resName,
                                                Integer pageNum, Integer pageSize) {
        if (CharSequenceUtil.isEmpty(contentType)) {
            return new Page<>();
        }
        EduResourcesPagingReq req = EduResourcesPagingReq.builder()
            .albumIds(Objects.isNull(albumId) ? Collections.emptyList() : Collections.singletonList(albumId))
            .resIds(Objects.isNull(resId) ? Collections.emptyList() : Collections.singletonList(resId))
            .resName(resName)
            .fuzzyQuery(CharSequenceUtil.isNotBlank(resName) ? 1 : 0)
            .pageSize(pageSize)
            .status(ResourcesStatus.ACTIVATED.getStatus())
            .pageNum(pageNum)
            .resTypes(Collections.singletonList(contentType))
            .sortByAsc(false).build();
        return getResourcesBoPages(req);
    }

    @Override
    public List<EduResourcesStoryBo> queryStoryResources(List<Long> resIds) {
        if (CollectionUtils.isEmpty(resIds)) {
            return Collections.emptyList();
        }
        EduResourcesQueryReq resourceReq = EduResourcesQueryReq.builder()
            .resIds(resIds)
            .build();
        EduResourcesExtensionReq req = EduResourcesExtensionReq.builder()
            .extensionTypes(Arrays.asList(EduResourcesAggregationType.STORY, EduResourcesAggregationType.DUB))
            .resourcesQueryReq(resourceReq)
            .build();
        List<EduResourcesAggregationDto> list = queryAggregation(req);
        return list.stream()
            .map(item -> {
                EduResourcesBo resources = Optional.ofNullable(item.getResources())
                    .map(r -> {
                        EduResourcesBo bo = new EduResourcesBo();
                        BeanUtils.copyProperties(r, bo);
                        return bo;
                    }).orElse(null);
                EduStoryResBo story = Optional.ofNullable(item.getStory())
                    .map(r -> {
                        EduStoryResBo bo = new EduStoryResBo();
                        BeanUtils.copyProperties(r, bo);
                        return bo;
                    }).orElse(null);
                DubResBo dub = Optional.ofNullable(item.getDub())
                    .map(r -> {
                        List<DubFragmentBo> fragmentList = Optional.ofNullable(r.getDubContent())
                            .orElse(Collections.emptyList())
                            .stream()
                            .map(fragment -> {
                                DubFragmentBo fragmentBo = new DubFragmentBo();
                                BeanUtils.copyProperties(fragment, fragmentBo);
                                return fragmentBo;
                            }).collect(Collectors.toList());
                        DubResBo bo = new DubResBo();
                        BeanUtils.copyProperties(r, bo);
                        return bo.setDubContent(fragmentList);
                    }).orElse(null);
                return new EduResourcesStoryBo()
                    .setResources(resources)
                    .setStory(story)
                    .setDub(dub);
            }).collect(Collectors.toList());
    }

    @Override
    public List<EduEbookResBo> queryEbooksResources(List<Long> resIds) {
        if (CollectionUtils.isEmpty(resIds)) {
            return Collections.emptyList();
        }
        EduResourcesQueryReq resourceReq = EduResourcesQueryReq.builder()
            .resIds(resIds)
            .build();
        EduResourcesExtensionReq req = EduResourcesExtensionReq.builder()
            .extensionTypes(Collections.singletonList(EduResourcesAggregationType.EBOOK))
            .resourcesQueryReq(resourceReq)
            .build();
        List<EduResourcesAggregationDto> list = queryAggregation(req);
        return list.stream()
            .filter(Objects::nonNull)
            .filter(item -> Objects.nonNull(item.getEbook()) && Objects.nonNull(item.getResources()))
            .map(item -> {
                EduEbookDto e = item.getEbook();
                EduResourcesDto r = item.getResources();
                EduEbookResBo bo = new EduEbookResBo();
                EduEbookContentDto ebookContent = e.getEbookContent();
                bo.setResId(e.getResId());
                bo.setAuthor(e.getAuthor());
                bo.setTotalWordCount(ebookContent.getTotalWordCount());
                bo.setChapters(
                    JsonBeanUtil.beanListTrans(ebookContent.getChapters(), EduEbookResBo.ChapterBo.class));
                bo.setResId(r.getResId());
                bo.setName(r.getResName());
                bo.setResPic(r.getResPic());
                bo.setRemark(r.getRemark());
                return bo;
            }).collect(Collectors.toList());
    }


    @Override
    public List<EduResourcesBo> listResources(List<Long> resIds) {
        if (CollectionUtils.isEmpty(resIds)) {
            return Collections.emptyList();
        }
        EduResourcesQueryReq resourceReq = EduResourcesQueryReq.builder()
            .resIds(resIds)
            .build();
        EduResourcesExtensionReq req = EduResourcesExtensionReq.builder()
            .resourcesQueryReq(resourceReq)
            .build();
        List<EduResourcesAggregationDto> list = queryAggregation(req);
        return list.stream().filter(Objects::nonNull).map(EduResourcesRpcClient::convertEduResources)
            .filter(Objects::nonNull).collect(Collectors.toList());
    }

    private static EduResourcesBo convertEduResources(EduResourcesAggregationDto dto) {
        return Optional.ofNullable(dto.getResources())
            .map(res -> {
                EduResourcesBo resource = new EduResourcesBo();
                BeanUtils.copyProperties(res, resource);
                return resource;
            }).orElse(null);
    }

    /**
     * 获取资源基础信息
     *
     * @param req 资源请求
     * @return Page<ResourcesBo>
     */
    private Page<ResourcesBo> getResourcesBoPages(EduResourcesPagingReq req) {
        IRpcResult<IPageResp<EduResourcesPagingDto>> resourcesRpcResult =
            eduResourcesRpcService.queryResourcesByPage(req);
        if (!resourcesRpcResult.checkSuccess()) {
            log.error("invoke eduResourcesRpcService.queryResourcesByPage failed, messages={}",
                resourcesRpcResult.getMessage());
            return new Page<>();
        }

        Optional<IPageResp<EduResourcesPagingDto>> pageResult = Optional.ofNullable(resourcesRpcResult.getData());
        return new Page<ResourcesBo>()
            .setRecords(pageResult
                .map(IPageResp::getPageRecords).orElse(Collections.emptyList())
                .stream()
                .map(item -> {
                    ResourcesBo bo = new ResourcesBo();
                    BeanUtils.copyProperties(item, bo);
                    bo.setResId(item.getId());
                    bo.setStatus(item.getResStatus());
                    return bo;
                }).collect(Collectors.toList()))
            .setTotal(pageResult.map(IPageResp::getTotalCount).orElse(0L))
            .setCurrent(pageResult.map(IPageResp::getPageNum).orElse(0L))
            .setSize(pageResult.map(IPageResp::getPageSize).orElse(0L));
    }
}
