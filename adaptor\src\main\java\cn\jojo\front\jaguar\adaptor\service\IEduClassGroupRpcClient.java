package cn.jojo.front.jaguar.adaptor.service;


import cn.jojo.front.jaguar.adaptor.model.req.ClassGroupListReq;
import cn.jojo.front.jaguar.common.pojo.vo.eduUserClass.EduClassGroupVo;

import java.util.List;

public interface IEduClassGroupRpcClient {


    List<EduClassGroupVo> listGroups(ClassGroupListReq classGroupListReq);

    List<EduClassGroupVo> queryClassGroupByIds(ClassGroupListReq classGroupListReq);

}
