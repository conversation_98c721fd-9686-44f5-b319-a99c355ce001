package cn.jojo.front.jaguar.adaptor.service.impl;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import cn.hutool.core.bean.BeanUtil;
import cn.jojo.edu.fantasy.rpc.api.dto.EduUserFavorDto;
import cn.jojo.edu.fantasy.rpc.api.req.EduUserFavorCancelReq;
import cn.jojo.edu.fantasy.rpc.api.req.EduUserFavorConfirmReq;
import cn.jojo.edu.fantasy.rpc.api.req.EduUserFavorPageReq;
import cn.jojo.edu.fantasy.rpc.api.service.IEduUserFavorRpcService;
import cn.jojo.front.jaguar.adaptor.service.IEduUserFavorRpcClient;
import cn.jojo.front.jaguar.common.pojo.bo.UserFavorBo;
import cn.jojo.front.jaguar.common.pojo.req.UserFavorCancelReq;
import cn.jojo.front.jaguar.common.pojo.req.UserFavorConfirmReq;
import cn.jojo.front.jaguar.common.pojo.req.UserFavorPageReq;
import cn.jojo.front.jaguar.common.utils.ModelConvertUtil;
import cn.jojo.infra.sdk.api.metadata.IPageResp;
import cn.jojo.infra.sdk.api.metadata.IRpcResult;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;


/**
 * @Description: 收藏相关rpc调用client
 * @Copyright: Copyright (c) 2022  ALL RIGHTS RESERVED.
 * @Company: 书声科技有限公司
 * @Author: chenHao
 * @CreateDate: 2022/10/11 18:48
 * @UpdateDate: 2022/10/11 18:48
 * @UpdateRemark: init
 * @Version: 1.0
 */
@Component
@Slf4j
public class EduUserFavorRpcClient implements IEduUserFavorRpcClient {

    @Reference
    private IEduUserFavorRpcService eduUserFavorRpcService;

    @Override
    public void confirm(UserFavorConfirmReq req) {
        EduUserFavorConfirmReq confirmReq = new EduUserFavorConfirmReq();
        BeanUtil.copyProperties(req, confirmReq);
        IRpcResult<Void> rpcResult = eduUserFavorRpcService.confirm(confirmReq);
        if (!rpcResult.checkSuccess()) {
            log.warn("invoke eduUserFavorRpcService.confirm fail:{}", rpcResult.getMessage());
        }
    }

    @Override
    public void cancel(UserFavorCancelReq req) {
        EduUserFavorCancelReq cancelReq = ModelConvertUtil.build(req, EduUserFavorCancelReq.class);
        IRpcResult<Void> rpcResult = eduUserFavorRpcService.cancel(cancelReq);
        if (!rpcResult.checkSuccess()) {
            log.warn("invoke eduUserFavorRpcService.cancel fail:{}", rpcResult.getMessage());
        }
    }

    @Override
    public Page<UserFavorBo> queryByPage(UserFavorPageReq req) {
        EduUserFavorPageReq pageReq = ModelConvertUtil.build(req, EduUserFavorPageReq.class);
        IRpcResult<IPageResp<EduUserFavorDto>> rpcResult = eduUserFavorRpcService.queryByPage(pageReq);
        if (!rpcResult.checkSuccess()) {
            log.warn("invoke eduUserFavorRpcService.queryByPage fail:{}", rpcResult.getMessage());
        }
        Optional<IPageResp<EduUserFavorDto>> pageResult = Optional.ofNullable(rpcResult.getData());
        return new Page<UserFavorBo>()
                .setRecords(pageResult
                        .map(IPageResp::getPageRecords).orElse(Collections.emptyList())
                        .stream()
                        .map(item -> {
                            UserFavorBo bo = new UserFavorBo();
                            BeanUtils.copyProperties(item, bo);
                            return bo;
                        }).collect(Collectors.toList()))
                .setTotal(pageResult.map(IPageResp::getTotalCount).orElse(0L))
                .setCurrent(pageResult.map(IPageResp::getPageNum).orElse(0L))
                .setSize(pageResult.map(IPageResp::getPageSize).orElse(0L));
    }

    @Override
    public List<UserFavorBo> queryByList(UserFavorPageReq req) {
        EduUserFavorPageReq pageReq = ModelConvertUtil.build(req, EduUserFavorPageReq.class);
        IRpcResult<IPageResp<EduUserFavorDto>> rpcResult = eduUserFavorRpcService.queryByPage(pageReq);
        if (!rpcResult.checkSuccess()) {
            log.warn("invoke eduUserFavorRpcService.queryByPage fail:{}", rpcResult.getMessage());
        }
        return Optional.ofNullable(rpcResult.getData())
                .map(IPageResp::getPageRecords).orElse(Collections.emptyList())
                .stream()
                .map(item -> {
                    UserFavorBo bo = new UserFavorBo();
                    BeanUtils.copyProperties(item, bo);
                    return bo;
                }).collect(Collectors.toList());
    }
}
