package cn.jojo.front.jaguar.adaptor.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.jojo.edu.fantasy.rpc.api.dto.EduTopicCardDto;
import cn.jojo.edu.fantasy.rpc.api.service.IEduTopicRpcService;
import cn.jojo.front.jaguar.adaptor.service.ThematicResourceTopService;
import cn.jojo.front.jaguar.common.pojo.bo.ThematicBo;
import cn.jojo.infra.sdk.api.metadata.IRpcResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import org.apache.dubbo.config.annotation.Reference;

/**
 * @Description: 校验专题卡片专辑信息数据
 * @Copyright: Copyright (c) 2022  ALL RIGHTS RESERVED.
 * @Company: 书声科技有限公司
 * @Author: chenHao
 * @CreateDate: 2022/9/28 9:16
 * @UpdateDate: 2022/9/28 9:16
 * @UpdateRemark: init
 * @Version: 1.0
 */
@Service
@Slf4j
public class ThematicResourceTopServiceImpl implements ThematicResourceTopService {

    @Reference
    private IEduTopicRpcService eduTopicRpcService;

    @Override
    public List<EduTopicCardDto> checkValidThematicResourceTopInfo(List<ThematicBo> thematicList) {
        if (CollUtil.isEmpty(thematicList)) {
            return Collections.emptyList();
        }
        List<Long> thematicIds = thematicList.stream()
                .filter(item-> Objects.nonNull(item.getThematicId()))
                .map(item -> Long.valueOf(item.getThematicId()))
                .collect(Collectors.toList());
        IRpcResult<List<EduTopicCardDto>> rpcResult =
                eduTopicRpcService.topicCardInfo(thematicIds);
        if (!rpcResult.checkSuccess() || CollUtil.isEmpty(rpcResult.getData())) {
            log.warn("调用教务获取专题失败：{}", rpcResult.getMessage());
            return Collections.emptyList();
        }
        return rpcResult.getData().stream().filter(o -> !thematicIds.contains(o.getTopicId()))
                .collect(Collectors.toList());
    }
}
