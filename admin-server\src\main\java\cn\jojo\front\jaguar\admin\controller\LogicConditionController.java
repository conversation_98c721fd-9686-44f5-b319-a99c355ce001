package cn.jojo.front.jaguar.admin.controller;

import cn.jojo.common.api.ex.DefaultHttpActionResult;
import cn.jojo.common.api.ex.IHttpActionResult;
import cn.jojo.front.jaguar.biz.service.common.header.RequestContext;
import cn.jojo.front.jaguar.biz.service.impl.logic.LogicConditionBizService;
import cn.jojo.front.jaguar.common.pojo.UserRequestContext;
import cn.jojo.front.jaguar.common.pojo.bo.PageBo;
import cn.jojo.front.jaguar.common.pojo.req.LogicConditionListReq;
import cn.jojo.front.jaguar.common.pojo.req.LogicConditionPatchReq;
import cn.jojo.front.jaguar.common.pojo.req.LogicConditionSaveReq;
import cn.jojo.front.jaguar.common.pojo.vo.LogicConditionDetailVo;
import cn.jojo.front.jaguar.common.pojo.vo.LogicConditionListVo;
import cn.jojo.front.jaguar.common.pojo.vo.LogicConditionMatchResultVo;
import cn.jojo.front.jaguar.common.pojo.vo.LogicConditionRelationVo;
import cn.jojo.front.jaguar.core.service.common.ConditionService;
import cn.jojo.front.jaguar.core.service.impl.logiccondition.LogicConditionService;
import cn.jojo.infra.sdk.api.metadata.DefaultPageResp;
import cn.jojo.infra.sdk.api.metadata.IPageResp;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * 2025/2/19 17:02
 */
@Validated
@RequestMapping("/admin")
@RestController
@Tag(name = "Jaguar管理后台")
public class LogicConditionController extends BaseController {
    @Resource
    private LogicConditionService logicConditionService;
    @Resource
    private ConditionService conditionService;
    @Resource
    private LogicConditionBizService logicConditionBizService;

    @GetMapping("/logic-conditions")
    @Operation(summary = "分页查询规则列表", description = "通过分页参数以及过滤条件查询规则列表")
    public IPageResp<LogicConditionListVo> page(LogicConditionListReq req) {
        PageBo<LogicConditionListVo> page = logicConditionService.list(req, getEmployeeId());
        return DefaultPageResp.buildPageResp(page.getPageNum(), page.getPageSize(), page.getTotal(), page.getData());
    }

    @GetMapping("/logic-conditions/{id}")
    @Operation(summary = "查询规则详情", description = "通过规则id查询规则详情")
    public IHttpActionResult<LogicConditionDetailVo> detail(@PathVariable(value = "id")
                                                                @NotNull @Min(value = 1)
                                                                @Parameter(description = "规则id")
                                                            Long id) {
        LogicConditionDetailVo logicCondition = logicConditionService.getById(id);
        return DefaultHttpActionResult.successWithData(logicCondition);
    }

    @PostMapping("/logic-conditions")
    @Operation(summary = "保存规则", description = "保存规则")
    public IHttpActionResult<Void> save(@Validated @RequestBody LogicConditionSaveReq req) {
        logicConditionService.saveOrUpdate(req, getEmployeeId(), getEmployeeName());
        return DefaultHttpActionResult.successWithoutData();
    }

    @PatchMapping("/logic-conditions/{id}")
    @Operation(summary = "更新规则", description = "更新规则")
    public IHttpActionResult<Void> update(@PathVariable(value = "id")
                                          @NotNull @Min(value = 1)
                                          @Parameter(description = "规则id") Long id,
                                          @Validated @RequestBody LogicConditionPatchReq req) {
        logicConditionService.patchUpdate(id, req, getEmployeeId(), getEmployeeName());
        return DefaultHttpActionResult.successWithoutData();
    }

    @GetMapping("/logic-conditions/{id}/relations")
    @Operation(summary = "查询规则的引用关系", description = "通过规则id查询规则的引用关系")
    public IHttpActionResult<LogicConditionRelationVo> relations(@PathVariable(value = "id")
                                                 @NotNull @Min(value = 1)
                                                 @Parameter(description = "规则id") Long id) {
        LogicConditionRelationVo data = conditionService.logicConditionRelation(id);
        return DefaultHttpActionResult.successWithData(data);
    }

    @GetMapping("/condition-validate-results")
    @Operation(summary = "逻辑条件匹配结果", description = "逻辑条件匹配结果")
    public IHttpActionResult<LogicConditionMatchResultVo> logicConditionResults(@NotNull @Min(value = 1)
                                                                            @Parameter(description = "规则id") Long logicConditionId,
                                                                            @Parameter(description = "测试id")
                                                                            @NotNull @Min(value = 1) Long testId) {
        try {
            UserRequestContext context = new UserRequestContext();
            context.setUserId(testId);
            RequestContext.setRequestContext(context);
            LogicConditionMatchResultVo result = logicConditionBizService.testLogicCondition(logicConditionId, testId);
            return DefaultHttpActionResult.successWithData(result);
        } finally {
            RequestContext.removeRequestContext();
        }
    }
}
