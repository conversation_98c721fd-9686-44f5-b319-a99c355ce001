package cn.jojo.front.jaguar.admin.model.convert.decorator;

import cn.jojo.front.jaguar.admin.model.convert.TaskSaveReq2VoConvert;
import cn.jojo.front.jaguar.admin.model.req.task.TaskSaveReq;
import cn.jojo.front.jaguar.biz.service.pojo.bo.task.SubTaskVo;
import cn.jojo.front.jaguar.biz.service.pojo.bo.task.TaskEventScopeVo;
import cn.jojo.front.jaguar.biz.service.pojo.bo.task.TaskVo;
import cn.jojo.front.jaguar.common.enums.task.ActivityEntityTypeEnum;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description
 * @date 2024/03/18
 **/
public abstract class TaskSaveReq2VoConvertDecorator implements TaskSaveReq2VoConvert {

    private TaskSaveReq2VoConvert delegate;

    protected TaskSaveReq2VoConvertDecorator(
        TaskSaveReq2VoConvert delegate) {
        this.delegate = delegate;
    }

    @Override
    public TaskVo model1ToModel2(TaskSaveReq taskSaveReq) {
        TaskVo taskVo = delegate.model1ToModel2(taskSaveReq);
        if(Objects.nonNull(taskSaveReq.getCourseId())){
            taskVo.setEntityType(ActivityEntityTypeEnum.COURSE.getValue());
            taskVo.setEntityValue(String.valueOf(taskSaveReq.getCourseId()));
        }
        List<SubTaskVo> subTaskList = taskVo.getSubTaskList();

        subTaskList.forEach(v->{
            TaskEventScopeVo scope = v.getEventScope();
            v.setTargetValue(scope.getTargetValue());
            scope.setTargetValue(null);
            // targetValue若null,默认值为 1
            if(Objects.isNull(v.getTargetValue())){
                v.setTargetValue("1");
            }
        });
        return taskVo;
    }
}
