package cn.jojo.front.jaguar.admin.model.dto.task;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description
 * @date 2024/03/19
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MedalResourcePackageDto {

    @Schema(description="安卓")
    private String android;

    @Schema(description="IOS")
    private String ios;

    @Schema(description="flutter")
    private String flutter;

    @Schema(description="文件名")
    private String name;
}
