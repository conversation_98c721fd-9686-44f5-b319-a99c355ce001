package cn.jojo.front.jaguar.admin.controller.task;

import cn.jojo.front.jaguar.admin.controller.BaseController;
import cn.jojo.front.jaguar.admin.model.convert.TaskPageVo2DotConvert;
import cn.jojo.front.jaguar.admin.model.convert.TaskQueryReq2VoConvert;
import cn.jojo.front.jaguar.admin.model.convert.TaskSaveReq2VoConvert;
import cn.jojo.front.jaguar.admin.model.convert.TaskUpdateReq2VoConvert;
import cn.jojo.front.jaguar.admin.model.convert.TaskVo2DtoConvert;
import cn.jojo.front.jaguar.admin.model.dto.task.TaskDto;
import cn.jojo.front.jaguar.admin.model.dto.task.TaskPageDto;
import cn.jojo.front.jaguar.admin.model.req.task.EventScopeReq;
import cn.jojo.front.jaguar.admin.model.req.task.SubTaskSaveReq;
import cn.jojo.front.jaguar.admin.model.req.task.SubTaskUpdateReq;
import cn.jojo.front.jaguar.admin.model.req.task.TaskPageReq;
import cn.jojo.front.jaguar.admin.model.req.task.TaskSaveReq;
import cn.jojo.front.jaguar.admin.model.req.task.TaskUpdateReq;
import cn.jojo.front.jaguar.admin.model.req.task.TaskVoiceReq;
import cn.jojo.front.jaguar.biz.service.pojo.bo.task.TaskPageVo;
import cn.jojo.front.jaguar.biz.service.pojo.bo.task.TaskVo;
import cn.jojo.front.jaguar.biz.service.task.ITaskBizService;
import cn.jojo.front.jaguar.common.enums.task.TargetTypeScopeEnum;
import cn.jojo.front.jaguar.common.enums.task.TaskType;
import cn.jojo.front.jaguar.common.enums.task.scopeconstraint.TargetTypeScopeConstraint;
import cn.jojo.front.jaguar.common.exception.BusinessException;
import cn.jojo.front.jaguar.common.utils.ApolloUtil;
import cn.jojo.infra.sdk.api.constants.ApiResultPlatformCodeConstants;
import cn.jojo.infra.sdk.api.metadata.DefaultHttpResult;
import cn.jojo.infra.sdk.api.metadata.DefaultPageResp;
import cn.jojo.infra.sdk.api.metadata.IHttpResult;
import cn.jojo.infra.sdk.api.metadata.IPageResp;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;

import java.lang.reflect.Field;
import java.util.stream.Collectors;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ReflectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @description 任务管理controller
 * @date 2024/03/11
 **/
@Slf4j
@Validated
@RestController
@RequestMapping("/admin/tasks")
@Tag(name = "任务管理")
public class TaskController extends BaseController {

    @Autowired
    private ITaskBizService taskBizService;


    @GetMapping
    @Operation(summary = "任务列表", description = "任务分页查询")
    public IPageResp<TaskPageDto> page(TaskPageReq req) {
        IPage<TaskPageVo> page = taskBizService.page(TaskQueryReq2VoConvert.INSTANCE.model1ToModel2(req));
        List<TaskPageDto> records = TaskPageVo2DotConvert.INSTANCE.model1sToModel2s(page.getRecords());
        for (TaskPageDto record : records) {
            record.setCreateUserName(getEmployeeName(record.getCreateUserId()));
            if(Objects.nonNull(record.getTaskType())){
                record.setTaskTypeName(TaskType.name(record.getTaskType()));
            }
        }
        return DefaultPageResp.buildPageResp(req.getPageNum(), req.getPageSize(), page.getTotal(), records);
    }

    @PostMapping
    @Operation(summary = "新增", description = "任务新增，包含条件，奖励")
    public IHttpResult<Void> save(@Validated @RequestBody TaskSaveReq req) {
        Optional.ofNullable(req.getTaskVoices()).ifPresent(v->{
            List<String> result = v.stream().map(TaskVoiceReq::getParam).filter(
                StringUtils::isNotBlank).collect(Collectors.toList());
            if (result.stream().distinct().count() < result.size()) {
                throw new BusinessException(ApiResultPlatformCodeConstants.PARAM_ERROR,"param error");
            }
        });
        validateSaveConstraints(req);
        
        TaskVo taskVo = TaskSaveReq2VoConvert.INSTANCE.model1ToModel2(req);
        taskVo.setCreateUserId(getEmployeeId());
        taskBizService.save(taskVo);
        return DefaultHttpResult.successWithoutData();
    }
    
    private void validateSaveConstraints(TaskSaveReq saveReq) {
        List<SubTaskSaveReq> subTaskList = saveReq.getSubTaskList();
        if (CollectionUtils.isEmpty(subTaskList)) {
            return;
        }

        for (SubTaskSaveReq subTaskReq : subTaskList) {
            validateConstraints(subTaskReq.getTargetType(), subTaskReq.getEventScope());
        }
        
    }

    @GetMapping("/{id}")
    @Operation(summary = "查看明细", description = "查看任务明细，包含条件，奖励")
    public IHttpResult<TaskDto> findById(
        @Schema(description="任务id")
        @NotNull @Positive @PathVariable(value = "id") Long id) {
        TaskVo taskVo = taskBizService.findById(id);
        return DefaultHttpResult.successWithData(TaskVo2DtoConvert.INSTANCE.model1ToModel2(taskVo));
    }

    @PatchMapping("/{id}")
    @Operation(summary = "编辑修改",description = "修改任务，包含条件，奖励")
    public IHttpResult<Void> update(@NotNull @PathVariable(value = "id") Long id,
                                    @Validated @RequestBody TaskUpdateReq req) {
        Optional.ofNullable(req.getTaskVoices()).ifPresent(v->{
            List<String> result = v.stream().map(TaskVoiceReq::getParam).filter(
                StringUtils::isNotBlank).collect(Collectors.toList());
            if (result.stream().distinct().count() < result.size()) {
                throw new BusinessException(ApiResultPlatformCodeConstants.PARAM_ERROR,"param error");
            }
        });
        validateUpdateConstraints(req);
        
        TaskVo taskVo = TaskUpdateReq2VoConvert.INSTANCE.model1ToModel2(req);
        taskVo.setCreateUserId(Optional.ofNullable(getEmployeeId()).orElse(0L));
        taskBizService.update(id, taskVo);
        return DefaultHttpResult.successWithoutData();
    }

    private void validateUpdateConstraints(TaskUpdateReq updateReq) {
        List<SubTaskUpdateReq> subTaskList = updateReq.getSubTaskList();
        if (CollectionUtils.isEmpty(subTaskList)) {
            return;
        }
        
        for (SubTaskUpdateReq subTaskReq : subTaskList) {
            validateConstraints(subTaskReq.getTargetType(), subTaskReq.getEventScope());
        }
    }
    
    private void validateConstraints(Integer targetType, EventScopeReq eventScope) {
        List<TargetTypeScopeConstraint> constraintList = TargetTypeScopeEnum.parseConstraints(targetType);
        String fieldName = "";
        try {
            for (TargetTypeScopeConstraint constraint : constraintList) {
                fieldName = constraint.getField();
    
                Field field = eventScope.getClass().getDeclaredField(fieldName);
                ReflectionUtils.makeAccessible(field);
                Object curValue = field.get(eventScope);
                boolean res = constraint.getType().validate(curValue, constraint.getCondition(), eventScope);
                if (!res) {
                    throw BusinessException.paramException(
                        String.format(ApolloUtil.getStringMessage("error.incentive.task.param.rule.mandatory"),
                            curValue, constraint.getCondition())
                    );
                }
            }
        } catch (NoSuchFieldException e) {
            log.error("[targetTypeConstraint] 无效的约束字段", e);
            throw BusinessException.paramException(
                String.format(ApolloUtil.getStringMessage("error.incentive.task.param.error"), fieldName)
            );
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("[targetTypeConstraint] 解析targetType条件约束异常", e);
            throw BusinessException.paramException(
                String.format(ApolloUtil.getStringMessage("error.incentive.task.param.error"), fieldName)
            );
        }
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "任务删除", description = "删除某个任务，包括条件和奖励")
    public IHttpResult<Void> delete(
        @Schema(description = "任务id")
        @NotNull @Positive @PathVariable("id") Long id) {
        taskBizService.delete(id);
        return DefaultHttpResult.successWithoutData();
    }
}

