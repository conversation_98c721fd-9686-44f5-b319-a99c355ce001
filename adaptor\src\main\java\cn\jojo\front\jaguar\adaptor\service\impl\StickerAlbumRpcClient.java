package cn.jojo.front.jaguar.adaptor.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.jojo.cc.api.domain.request.StickerAlbumPageQueryReq;
import cn.jojo.cc.api.domain.request.StickerAlbumQueryReq;
import cn.jojo.cc.api.service.StickerAlbumService;
import cn.jojo.cc.common.dto.PropGroupDto;
import cn.jojo.cc.common.dto.StickerPageDto;
import cn.jojo.front.jaguar.adaptor.service.IStickerAlbumRpcClient;
import cn.jojo.front.jaguar.common.pojo.req.StickerAlbumReq;
import cn.jojo.front.jaguar.common.pojo.vo.activity.StickerPageVo;
import cn.jojo.infra.sdk.api.metadata.IRpcResult;
import cn.jojo.uc.common.exception.BusinessServiceException;
import com.google.api.client.util.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/3/31 10:50
 * @desc
 */
@Slf4j
@Component
public class StickerAlbumRpcClient implements IStickerAlbumRpcClient {

    @DubboReference
    private StickerAlbumService stickerAlbumService;


    @Override
    public StickerPageVo getStickerPagesByKey(StickerAlbumReq req) {

        if (ObjectUtil.isEmpty(req.getStickerPageKey())) {
            return null;
        }

        StickerAlbumPageQueryReq queryReq = StickerAlbumPageQueryReq.builder()
            .pageKeys(Sets.newHashSet(req.getStickerPageKey())).isProd(req.isProd()).build();

        IRpcResult<Map<String, StickerPageDto>> result = stickerAlbumService.queryStickerPageByPageKeys(queryReq);

        if (ObjectUtil.isEmpty(result) || !result.checkSuccess()) {
            log.error("invoke StickerAlbumRpcClient.queryStickerPageByPageKeys failed, message={}",
                result.getMessage());
            throw new BusinessServiceException("invoke StickerAlbumRpcClient.queryStickerPageByPageKeys failed");
        }

        StickerPageDto dto = result.getData().getOrDefault(req.getStickerPageKey(), null);
        if (ObjectUtil.isEmpty(dto)) {
            log.error("invoke StickerAlbumRpcClient.queryStickerPageByPageKeys failed, message={}",
                result.getMessage());
            return null;
        }

        return StickerPageVo.builder()
            .stickerPageKey(dto.getPageKey())
            .id(dto.getId())
            .build();
    }

    @Override
    public List<StickerPageVo> getStickerPagesByIds(StickerAlbumReq req) {

        if (ObjectUtil.isEmpty(req.getStickerPageIds())) {
            return Lists.newArrayList();
        }

        StickerAlbumPageQueryReq queryReq = StickerAlbumPageQueryReq.builder()
            .pageIds(Sets.newHashSet(req.getStickerPageIds())).isProd(req.isProd()).build();

        IRpcResult<Map<Long, StickerPageDto>> result = stickerAlbumService.queryStickerPageByPageIds(queryReq);

        if (ObjectUtil.isEmpty(result) || !result.checkSuccess()) {
            log.error("invoke StickerAlbumRpcClient.getStickerPagesByIds failed, message={}", result.getMessage());
            throw new BusinessServiceException("invoke StickerAlbumRpcClient.getStickerPagesByIds failed");
        }

        Map<Long, StickerPageDto> data = result.getData();
        if (ObjectUtil.isEmpty(data)) {
            log.error("invoke StickerAlbumRpcClient.getStickerPagesByIds failed, message={}", result.getMessage());
            return Lists.newArrayList();
        }

        return data.values().stream()
            .map(dto -> StickerPageVo.builder()
                .stickerPageKey(dto.getPageKey())
                .id(dto.getId())
                .build())
            .collect(Collectors.toList());
    }



    @Override
    public Map<Long, List<PropGroupDto>> queryStickerPagePropByPageIds(StickerAlbumReq req) {

        if (ObjectUtil.isEmpty(req.getStickerPageIds())) {
            return Maps.newHashMap();
        }

        StickerAlbumQueryReq queryReq = StickerAlbumQueryReq.builder()
            .pageIds(Sets.newHashSet(req.getStickerPageIds())).courseId(req.getCourseId()).build();

        IRpcResult<Map<Long, List<PropGroupDto>>> result = stickerAlbumService.queryStickerPagePropByPageIds(queryReq);

        if (ObjectUtil.isEmpty(result) || !result.checkSuccess()) {
            log.error("invoke StickerAlbumRpcClient.getStickerPagesByIds failed, message={}", result.getMessage());
            throw new BusinessServiceException("invoke StickerAlbumRpcClient.getStickerPagesByIds failed");
        }

        return result.getData();
    }
}
