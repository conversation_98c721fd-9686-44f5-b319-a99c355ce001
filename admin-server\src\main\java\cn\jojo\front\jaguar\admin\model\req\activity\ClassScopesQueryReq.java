package cn.jojo.front.jaguar.admin.model.req.activity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/07/03
 * @Version 1.0
 * @Description
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description="投放范围班级")
public class ClassScopesQueryReq {

    @Schema(description="班级组ids")
    private List<Long> groupIds;

    @Schema(description="最小班级开课时间")
    private Long minScheduleStartTime;

    @Schema(description="活动id")
    private Long activityId;
}
