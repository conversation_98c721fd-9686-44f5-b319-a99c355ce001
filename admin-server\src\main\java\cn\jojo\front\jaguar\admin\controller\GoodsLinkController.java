package cn.jojo.front.jaguar.admin.controller;


import cn.jojo.common.api.ex.DefaultHttpActionResult;
import cn.jojo.common.api.ex.IHttpActionResult;
import cn.jojo.front.jaguar.biz.service.impl.GoodsLinkBizService;
import cn.jojo.front.jaguar.common.enums.GoodsLinkType;
import cn.jojo.front.jaguar.common.exception.BusinessException;
import cn.jojo.front.jaguar.common.pojo.vo.GoodsLinkVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Tag(name = "cms商品链接管理接口")
@RestController
@RequestMapping("admin/goods-links")
@Validated
public class GoodsLinkController {

    @Autowired
    private GoodsLinkBizService goodsLinkBizService;

    @Operation(summary = "查询商品链接列表", description = "查询阶段商品链接列表")
    @GetMapping
    public IHttpActionResult<List<GoodsLinkVo>> findList(
            @Parameter(description = "课程阶段值(可选)，多个用逗号拼接") @RequestParam(required = false)
            String courseSegmentCodes,
            @Parameter(description = "商品链接类型(可选),默认为1：续费商品链接")
            @RequestParam(required = false, defaultValue = "1")
            Integer linkType) {
        List<Integer> courseSegmentCodeList = null;

        if (StringUtils.isNotBlank(courseSegmentCodes)) {
            try {
                courseSegmentCodeList =
                        Stream.of(courseSegmentCodes.split(",")).map(Integer::valueOf).collect(Collectors.toList());
            } catch (Exception e) {
                throw BusinessException.paramException("course segment error");
            }
        }
        if (GoodsLinkType.of(linkType) == null) {
            throw BusinessException.paramException("product link type error");
        }
        return DefaultHttpActionResult.successWithData(
                goodsLinkBizService.findGoodsLinkVoList(courseSegmentCodeList, linkType));
    }

    @Operation(summary = "更新商品链接", description = "更新阶段商品链接")
    @PutMapping
    public IHttpActionResult<Void> saveOrUpdateGoodsLink(@RequestBody @Validated GoodsLinkVo goodsLinkVo) {
        goodsLinkBizService.saveOrUpdateGoodsLink(goodsLinkVo);
        return DefaultHttpActionResult.successWithoutData();
    }
}
