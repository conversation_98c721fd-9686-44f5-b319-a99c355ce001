package cn.jojo.front.jaguar.admin.controller;

import cn.jojo.front.jaguar.common.pojo.req.BannerSaveInfoReq;
import cn.jojo.front.jaguar.common.pojo.req.BannersPageReq;
import cn.jojo.front.jaguar.common.pojo.vo.BannersVo;
import cn.jojo.front.jaguar.core.service.plate.NewBannerService;
import cn.jojo.infra.sdk.api.metadata.DefaultHttpResult;
import cn.jojo.infra.sdk.api.metadata.DefaultPageResp;
import cn.jojo.infra.sdk.api.metadata.IHttpResult;
import cn.jojo.infra.sdk.api.metadata.IPageResp;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;

@Validated
@RequestMapping("/admin")
@RestController
@Tag(name = "Banner管理")
public class BannersController extends BaseController {

    @Resource
    private NewBannerService bannerService;

    @PostMapping("/banners")
    @Operation(summary = "新增Banner", description = "新增Banner")
    public IHttpResult<Long> saveBanner(@Validated @RequestBody BannerSaveInfoReq req) {
        Long id = bannerService.saveOrUpdate(req);
        return DefaultHttpResult.successWithData(id);
    }

    @GetMapping("/banners")
    @Operation(summary = "查询Banner列表", description = "查询Banner列表")
    public IPageResp<BannersVo> getBannerList(BannersPageReq req) {
        IPage<BannersVo> page = bannerService.pageBanners(req);
        return DefaultPageResp.buildPageResp(page.getCurrent(), page.getSize(), page.getTotal(), page.getRecords());
    }

    @PutMapping("/banners/{id}")
    @Operation(summary = "更新Banner", description = "更新Banner")
    public IHttpResult<Long> updateBanner(
        @PathVariable(value = "id") @NotNull @Parameter(description = "bannerId") Long id,
        @Validated @RequestBody BannerSaveInfoReq req) {
        req.setId(id);
        bannerService.saveOrUpdate(req);
        return DefaultHttpResult.successWithData(id);
    }

    @DeleteMapping("/banners/{id}")
    @Operation(summary = "删除Banner", description = "删除Banner")
    public IHttpResult<Boolean> deleteBanner(
        @PathVariable(value = "id") @NotNull @Parameter(description = "bannerId") Long id) {
        bannerService.delete(id);
        return DefaultHttpResult.successWithData(true);
    }
}
