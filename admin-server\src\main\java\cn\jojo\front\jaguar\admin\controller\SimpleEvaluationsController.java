package cn.jojo.front.jaguar.admin.controller;

import cn.jojo.common.api.ex.DefaultHttpActionPageResult;
import cn.jojo.common.api.ex.DefaultHttpActionResult;
import cn.jojo.common.api.ex.IHttpActionResult;
import cn.jojo.front.jaguar.common.enums.ControllerActionType;
import cn.jojo.front.jaguar.common.exception.BusinessException;
import cn.jojo.front.jaguar.common.pojo.bo.PageBo;
import cn.jojo.front.jaguar.common.pojo.req.SimpleEvaluationBindingQuestionSaveReq;
import cn.jojo.front.jaguar.common.pojo.req.SimpleEvaluationDimensionRuleSaveReq;
import cn.jojo.front.jaguar.common.pojo.req.SimpleEvaluationDimensionSaveReq;
import cn.jojo.front.jaguar.common.pojo.req.SimpleEvaluationListReq;
import cn.jojo.front.jaguar.common.pojo.req.SimpleEvaluationReq;
import cn.jojo.front.jaguar.common.pojo.req.SimpleEvaluationRuleSaveReq;
import cn.jojo.front.jaguar.common.pojo.req.SimpleEvaluationUpdateReq;
import cn.jojo.front.jaguar.common.pojo.vo.EvaluationVo;
import cn.jojo.front.jaguar.common.pojo.vo.SimpleEvaluationListVo;
import cn.jojo.front.jaguar.core.service.question.SimpleEvaluationDimensionService;
import cn.jojo.front.jaguar.core.service.question.SimpleEvaluationService;
import cn.jojo.infra.sdk.api.constants.ApiResultPlatformCodeConstants;
import io.swagger.v3.oas.annotations.Operation;
import cn.jojo.infra.sdk.api.metadata.IPageResp;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.Positive;
import java.util.Objects;
import java.util.Optional;

@RestController
@RequestMapping(value = "admin/simple-evaluations")
@Tag(name = "Jaguar管理后台-测评问卷管理(restFull接口)")
public class SimpleEvaluationsController extends BaseController{

    @Autowired
    private SimpleEvaluationService simpleEvaluationService;

    @Autowired
    private SimpleEvaluationDimensionService simpleEvaluationDimensionService;

    @PostMapping()
    @Operation(summary = "新增操作", description = "对测评资源的新增操作接口")
    @ApiResponse(description = "新建成功的测评id，String类型")
    public IHttpActionResult<String> creatEvaluation(@RequestBody SimpleEvaluationReq req) {
        Long id = null;
        if (ControllerActionType.COPY.getType().equals(req.getActionType())) {
            if (Objects.isNull(req.getId())) {
                throw new BusinessException(ApiResultPlatformCodeConstants.PARAM_ERROR, "id can not be null");
            }
            id = simpleEvaluationService.copyEvaluation(req.getId(), getEmployeeId(), getEmployeeName());
        } else if (ControllerActionType.CREATE.getType().equals(req.getActionType())) {
            id = simpleEvaluationService.save(req, getEmployeeId(), getEmployeeName());
        }
        return DefaultHttpActionResult.successWithData(String.valueOf(id));
    }

    @GetMapping()
    @Operation(summary = "测评列表", description = "查询所有有效的测评信息列表")
    @ApiResponse(description = "测评列表分页信息")
    public IHttpActionResult<IPageResp<SimpleEvaluationListVo>> queryList(@Validated SimpleEvaluationListReq req) {
        if (Objects.nonNull(req.getQuerySelf()) && req.getQuerySelf()) {
            req.setCreateUserId(getEmployeeId());
            req.setCreateUserName(getEmployeeName());
        }
        PageBo<SimpleEvaluationListVo> pageResult = simpleEvaluationService.listEvaluations(req);
        return DefaultHttpActionPageResult.successWithPageData(pageResult.getPageNum(), pageResult.getPageSize(),
            pageResult.getTotal(), pageResult.getData());
    }

    @PutMapping("/{evaluationId}")
    @Operation(summary = "设置测评问卷信息", description = "设置测评问卷信息，包含问卷信息、算分规则、打标规则等")
    public IHttpActionResult<Void> updateById(
        @Parameter(description = "测评问卷id") @PathVariable @Positive Long evaluationId,
        @Validated @RequestBody SimpleEvaluationUpdateReq req) {
        Optional.ofNullable(req.getDimensionList()).ifPresent(data ->
            simpleEvaluationDimensionService.saveDimensionList(new SimpleEvaluationDimensionSaveReq()
                    .setId(evaluationId)
                    .setDimensionList(data),
                getEmployeeId(), getEmployeeName()));
        Optional.ofNullable(req.getRuleData()).ifPresent(data ->
            simpleEvaluationService.setRule(new SimpleEvaluationRuleSaveReq()
                    .setId(evaluationId)
                    .setRuleData(data),
                getEmployeeId(), getEmployeeName()));
        Optional.ofNullable(req.getDimensionRule()).ifPresent(data ->
            simpleEvaluationDimensionService.setRule(new SimpleEvaluationDimensionRuleSaveReq()
                    .setId(evaluationId)
                    .setDimensionRule(data),
                getEmployeeId(), getEmployeeName()));
        Optional.ofNullable(req.getQuestionBindingList()).ifPresent(data ->
            simpleEvaluationDimensionService.bindingQuestions(new SimpleEvaluationBindingQuestionSaveReq()
                    .setId(evaluationId)
                    .setQuestionBindingList(data),
                getEmployeeId(), getEmployeeName()));
        return DefaultHttpActionResult.successWithoutData();
    }

    @GetMapping("/{evaluationId}")
    @Operation(summary = "查询测评问卷信息", description = "通过id查询测评问卷详情信息")
    public IHttpActionResult<EvaluationVo> getById(
        @Parameter(description = "测评问卷id") @PathVariable @Positive Long evaluationId) {
        EvaluationVo evaluationInfo = simpleEvaluationService.getEvaluationInfo(evaluationId);
        simpleEvaluationService.flushCommonDimension(evaluationInfo);
        return DefaultHttpActionResult.successWithData(evaluationInfo);
    }
}
