package cn.jojo.front.jaguar.admin.controller;

import cn.jojo.common.api.ex.DefaultHttpActionPageResult;
import cn.jojo.common.api.ex.DefaultHttpActionResult;
import cn.jojo.common.api.ex.IHttpActionResult;
import cn.jojo.front.jaguar.common.enums.HomeTabMaterialType;
import cn.jojo.front.jaguar.common.pojo.req.CommonBaseListReq;
import cn.jojo.front.jaguar.common.pojo.req.HomeTabMaterialSaveReq;
import cn.jojo.front.jaguar.common.pojo.req.HomeTabUpdateReq;
import cn.jojo.front.jaguar.common.pojo.req.PriorityUpdateReq;
import cn.jojo.front.jaguar.common.pojo.vo.HomeTabMaterialDetailVo;
import cn.jojo.front.jaguar.common.pojo.vo.HomeTabMaterialListVo;
import cn.jojo.front.jaguar.common.pojo.vo.HomeTabVo;
import cn.jojo.front.jaguar.core.service.tab.HomeTabMaterialService;
import cn.jojo.front.jaguar.core.service.tab.HomeTabService;
import cn.jojo.infra.sdk.api.metadata.IPageResp;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import java.util.Collections;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/admin/tab")
@Tag(name = "Jaguar管理后台")
@Validated
public class HomeTabController {

    @Resource
    private HomeTabMaterialService homeTabMaterialService;
    @Resource
    private HomeTabService homeTabService;

    @GetMapping("/material/list")
    @Operation(summary ="分页获取素材列表",   description = "分页获取素材列表")
    public IHttpActionResult<IPageResp<HomeTabMaterialListVo>> listHomeTabMaterial(@RequestParam("type") @NotBlank String type,
        @RequestParam("tabKey") @NotBlank String tabKey,
        CommonBaseListReq<Void> req) {
        IPage<HomeTabMaterialListVo> result = homeTabMaterialService.listHomeTabMaterial(
            HomeTabMaterialType.of(type), tabKey, req);
        return DefaultHttpActionPageResult
            .successWithPageData(result.getCurrent(), result.getSize() < 1 ? 1 : result.getSize(), result.getTotal(), result.getRecords());
    }

    @GetMapping("/material/detail")
    @Operation(summary ="获取tab素材详情",   description = "获取tab素材详情")
    public IHttpActionResult<HomeTabMaterialDetailVo> getHomeTabMaterialDetail(@RequestParam("id") @NotNull Integer id) {
        List<HomeTabMaterialDetailVo> list = homeTabMaterialService
            .listDetailByIds(Collections.singleton(id));
        return DefaultHttpActionResult.successWithData(list.stream().findFirst().orElse(null));
    }

    @PostMapping("/material/save")
    @Operation(summary ="保存tab素材",   description = "保存tab素材")
    public IHttpActionResult<Boolean> save(@RequestBody @Validated HomeTabMaterialSaveReq req) {
        homeTabMaterialService.saveOrUpdate(req);
        return DefaultHttpActionResult.successWithData(true);
    }

    @GetMapping("/material/updatePriority")
    @Operation(summary ="更新tab素材优先级",   description = "更新tab素材优先级")
    public IHttpActionResult<Boolean> updatePriority(@Validated PriorityUpdateReq req) {
        homeTabMaterialService.updatePriority(req);
        return DefaultHttpActionResult.successWithData(true);
    }

    @GetMapping("/material/deleteMaterial")
    @Operation(summary ="删除素材",   description = "删除素材")
    public IHttpActionResult<Boolean> deleteMaterial(@RequestParam("id") @NotNull Integer id) {
        homeTabMaterialService.deleteMaterial(id);
        return DefaultHttpActionResult.successWithData(true);
    }

    @GetMapping("/tab/all")
    @Operation(summary ="获取所有tab",   description = "获取所有tab")
    public IHttpActionResult<List<HomeTabVo>> listAllTab() {
        return DefaultHttpActionResult.successWithData(homeTabService.getAllHomeTabsVo());
    }

    @PostMapping("/tab/update")
    @Operation(summary ="更新tab", description = "更新tab")
    public IHttpActionResult<Boolean> updateTab(@RequestBody @Validated HomeTabUpdateReq req) {
        homeTabService.update(req);
        return DefaultHttpActionResult.successWithData(true);
    }
}
