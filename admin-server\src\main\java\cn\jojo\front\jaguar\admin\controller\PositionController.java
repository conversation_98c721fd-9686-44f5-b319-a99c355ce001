package cn.jojo.front.jaguar.admin.controller;

import cn.jojo.front.jaguar.common.pojo.req.resource.ResourcePositionSaveReq;
import cn.jojo.front.jaguar.common.pojo.vo.resource.ResourcePositionGroupListVo;
import cn.jojo.front.jaguar.common.pojo.vo.resource.ResourcePositionListVo;
import cn.jojo.front.jaguar.core.service.resource.IResourcePositionService;
import cn.jojo.infra.sdk.api.metadata.DefaultHttpResult;
import cn.jojo.infra.sdk.api.metadata.IHttpResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2024/1/16 12:07
 * @desc
 */
@RestController
@RequestMapping("/admin")
public class PositionController {

    @Resource
    private IResourcePositionService resourcePositionService;

    @GetMapping("positions")
    @Operation(summary = "查询位置列表", description = "查询位置列表")
    public IHttpResult<ResourcePositionListVo> getPositions(
        @RequestParam(defaultValue = "positions") @Parameter(description = "查询位置类型：positions,scopes")
        @NotBlank String type) {
        ResourcePositionListVo result = resourcePositionService.listResourcePosition(type);
        return DefaultHttpResult.successWithData(result);
    }

    @PutMapping("positions")
    @Operation(summary = "更新位置列表", description = "更新位置列表")
    public IHttpResult<Boolean> savePositions(@RequestBody @Valid ResourcePositionSaveReq req) {
        Boolean result = resourcePositionService.saveResourcePositions(req);
        return DefaultHttpResult.successWithData(result);
    }

    @GetMapping("/positions-groups")
    @Operation(summary = "查询位置分组", description = "对位置按照尺寸分组后返回")
    public IHttpResult<ResourcePositionListVo> getPositionGroup() {
        ResourcePositionListVo result = resourcePositionService.listResourcePosition("scopes");
        return DefaultHttpResult.successWithData(result);
    }
}
