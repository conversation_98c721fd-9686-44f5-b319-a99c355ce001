package cn.jojo.front.jaguar.adaptor.service.impl;

import cn.jojo.edu.fantasy.rpc.api.dto.EduAlbumPagingDto;
import cn.jojo.edu.fantasy.rpc.api.dto.EduTopicDto;
import cn.jojo.edu.fantasy.rpc.api.req.EduTopicPageQueryReq;
import cn.jojo.edu.fantasy.rpc.api.service.IEduTopicRpcService;
import cn.jojo.front.jaguar.adaptor.service.IEduTopicRpcClient;
import cn.jojo.front.jaguar.common.bo.EduTopicBo;
import cn.jojo.front.jaguar.common.pojo.req.EduTopicPageQueryRpcReq;
import cn.jojo.infra.sdk.api.metadata.IPageResp;
import cn.jojo.infra.sdk.api.metadata.IRpcResult;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/3/31 10:50
 * @desc
 */
@Slf4j
@Component
public class EduTopicRpcClient implements IEduTopicRpcClient {

    @DubboReference
    private IEduTopicRpcService eduTopicRpcService;

    @Override
    public Page<EduTopicBo> queryTopicPage(EduTopicPageQueryRpcReq rpcReq) {
        EduTopicPageQueryReq req = new EduTopicPageQueryReq();
        BeanUtils.copyProperties(rpcReq, req);
        try {
            IRpcResult<IPageResp<EduTopicDto>> resp = eduTopicRpcService.queryTopicPage(req);
            if (!resp.checkSuccess()) {
                log.error("EduTopicRpcClient#queryTopicPage failed, req = {}", req);
                return new Page<>();
            }

            Optional<IPageResp<EduTopicDto>> pageResult = Optional.ofNullable(resp.getData());
            return new Page<EduTopicBo>().setRecords(pageResult
                .map(IPageResp::getPageRecords).orElse(Collections.emptyList())
                .stream()
                .map(data -> {
                    EduTopicBo eduTopicBo = new EduTopicBo();
                    BeanUtils.copyProperties(data, eduTopicBo);
                    return eduTopicBo;
                }).collect(Collectors.toList()))
                .setTotal(pageResult.map(IPageResp::getTotalCount).orElse(0L))
                .setCurrent(pageResult.map(IPageResp::getPageNum).orElse(0L))
                .setSize(pageResult.map(IPageResp::getPageSize).orElse(0L));
        } catch (Exception e) {
            log.error("EduTopicRpcClient#queryTopicPage failed", e);
            return new Page<>();
        }
    }
}
