package cn.jojo.front.jaguar.admin.model.req.activity;

import cn.jojo.front.jaguar.common.enums.task.ActivityTaskEffectiveModeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 删除活动任务入参
 * @date 2024/4/11 13:55
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DeleteTaskReq {

    @Schema(description = "任务id")
    private Long taskId;
    @Schema(description = "移除任务原因")
    private String removeReason;

    @Schema(description = "生效模式", implementation = ActivityTaskEffectiveModeEnum.class)
    private Integer effectiveMode;
}
