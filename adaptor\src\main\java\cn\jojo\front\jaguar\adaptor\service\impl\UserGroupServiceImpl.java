package cn.jojo.front.jaguar.adaptor.service.impl;

import cn.jojo.crm.request.GroupReq;
import cn.jojo.crm.request.group.BasePageQueryGroupReq;
import cn.jojo.crm.response.SearchResp;
import cn.jojo.crm.response.group.GroupResp;
import cn.jojo.crm.service.IGroupService;
import cn.jojo.crm.service.ISearchService;
import cn.jojo.front.jaguar.adaptor.service.UserGroupService;
import cn.jojo.front.jaguar.common.constant.CommonConstant;
import cn.jojo.front.jaguar.common.exception.BusinessException;
import cn.jojo.front.jaguar.common.pojo.bo.UserGroupEsBo;
import cn.jojo.front.jaguar.common.pojo.vo.UserGroupListVo;
import cn.jojo.infra.sdk.api.metadata.IRpcResult;
import com.alibaba.fastjson.JSON;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class UserGroupServiceImpl implements UserGroupService {

    @Reference
    private IGroupService groupService;
    @Reference
    private ISearchService searchService;

    @Value("${crm.authorization.staff.ids:0}")
    private String staffIds;
    @Value("${crm.filter.common.group:true}")
    private Boolean filterCommonGroup;

    /**
     * 个人分群scope
     */
    private static final String PERSONAL_GROUP_SCOPE = "personal";

    @Override
    public List<GroupResp.Group> getUserClusterList() {
        List<Long> ids = Stream.of(staffIds.split(",")).map(Long::valueOf).collect(Collectors.toList());
        BasePageQueryGroupReq req = BasePageQueryGroupReq.builder().creatorId(ids).pageNum(1).pageSize(1000).build();
        List<GroupResp.Group> groupList = Collections.emptyList();
        try {
            IRpcResult<GroupResp> rpcResult = groupService.pageQueryGroup(req);
            if (!rpcResult.checkSuccess() || rpcResult.getData() == null
                || CollectionUtils.isEmpty(rpcResult.getData().getList())) {
                return Collections.emptyList();
            }
            groupList = rpcResult.getData().getList();
        } catch (Exception e) {
            log.error("获取用户分群失败", e);
        }
        return Boolean.TRUE.equals(filterCommonGroup)
            ? groupList.stream()
            .filter(item -> PERSONAL_GROUP_SCOPE.equals(item.getUseScope())).collect(Collectors.toList())
            : groupList;
    }

    @Override
    public UserGroupEsBo getUserGroupEsResult(GroupReq req) {
        int errorCount = 0;
        do {
            try {
                IRpcResult<SearchResp<Long>> rpcResult = searchService.searchIdByGroupCode(req);
                if (!rpcResult.checkSuccess()) {
                    throw BusinessException.exception(rpcResult.getMessage());
                }
                if (rpcResult.getData() == null || rpcResult.getData().getData() == null) {
                    return new UserGroupEsBo();
                }
                return new UserGroupEsBo()
                    .setScrollId(rpcResult.getData().getScrollId())
                    .setUserIds(rpcResult.getData().getData());
            } catch (Exception e) {
                log.error("调用crm获取分群对应userId失败，req：{}", JSON.toJSONString(req));
                errorCount++;
            }
        } while (errorCount <= CommonConstant.UserGroupConstants.INQUIRY_RETRY_COUNT);
        log.error("获取分群信息失败，分群{}同步中断，重试次数{}", req.getCode(), CommonConstant.UserGroupConstants.INQUIRY_RETRY_COUNT);
        return new UserGroupEsBo().setFail(true);
    }

    @Override
    public List<UserGroupListVo> listUserGroups() {
        return getUserClusterList().stream().map(item -> new UserGroupListVo()
            .setValue(item.getId().toString())
            .setDescription(item.getName())
            .setTotal(item.getTotal()))
            .collect(Collectors.toList());
    }
}
