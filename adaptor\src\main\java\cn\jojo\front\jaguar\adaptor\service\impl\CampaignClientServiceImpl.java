package cn.jojo.front.jaguar.adaptor.service.impl;

import cn.jojo.front.jaguar.adaptor.service.ICampaignClientService;
import cn.tinman.sharedservices.mall.cashback.api.campaign.request.SoldNoteQueryReq;
import cn.tinman.sharedservices.mall.cashback.api.campaign.response.SoldNoteDto;
import cn.tinman.sharedservices.mall.cashback.api.campaign.service.ICampaignApiService;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/2/10 16:59
 */
@Service
public class CampaignClientServiceImpl extends AbstractRpcClient implements ICampaignClientService {

    @DubboReference
    private ICampaignApiService campaignApiService;

    @Override
    public List<SoldNoteDto> querySoldNotes(SoldNoteQueryReq req) {
        return doRpcList(req, campaignApiService::querySoldNotes, SoldNoteDto.class);
    }
}
