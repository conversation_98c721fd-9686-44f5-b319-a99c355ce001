package cn.jojo.front.jaguar.adaptor.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.jojo.edu.fantasy.common.dict.SortField;
import cn.jojo.edu.fantasy.common.dict.SortType;
import cn.jojo.edu.fantasy.rpc.api.dto.EduUserLabelDto;
import cn.jojo.edu.fantasy.rpc.api.req.EduUserLabelQueryReq;
import cn.jojo.edu.fantasy.rpc.api.service.IUserLabelRpcService;
import cn.jojo.front.jaguar.adaptor.service.IUserLabelRpcClient;
import cn.jojo.front.jaguar.common.pojo.bo.EduUserLabelBo;
import cn.jojo.infra.sdk.api.metadata.IPageResp;
import cn.jojo.infra.sdk.api.metadata.IRpcResult;
import cn.jojo.infra.sdk.logging.JoJoLogging;
import com.alibaba.fastjson2.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.common.utils.LogHelper;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: luohuanrong
 * @create: 2023/5/25
 **/
@Slf4j
@Component
public class UserLabelRpcClient implements IUserLabelRpcClient {

    @Value("${plate.recommend.user.label.max.size:50}")
    private Integer recommendUserLabelMaxSize; //推荐用户标签查询数

    @DubboReference
    private IUserLabelRpcService userLabelRpcService;

    @Override
    public List<EduUserLabelBo> queryUserLabelList(Long userId) {
        try {
            EduUserLabelQueryReq req = new EduUserLabelQueryReq();
            req.setUserIds(Collections.singletonList(userId));
            req.setPageNum(1);
            req.setPageSize(recommendUserLabelMaxSize);
            req.setSortFields(Collections.singletonList(SortField.USED_TIMES.getField()));
            req.setSortType(SortType.DESC.getType());
            IRpcResult<IPageResp<EduUserLabelDto>> rpcResult = userLabelRpcService.queryUserLabels(req);
            JoJoLogging.logger(log)
                    .unIndex("req", JSON.toJSONString(req))
                    .unIndex("rpcResult", JSON.toJSONString(rpcResult))
                    .info("plate recommend queryUserLabelList userId:{}", userId);
            if (!rpcResult.checkSuccess() || Objects.isNull(rpcResult.getData())) {
                return Collections.emptyList();
            }
            IPageResp<EduUserLabelDto> pageResp = rpcResult.getData();
            if (CollUtil.isEmpty(pageResp.getPageRecords())) {
                return Collections.emptyList();
            }
            return convertEduUserLabelBo(pageResp.getPageRecords());
        } catch (Exception e) {
            log.error("queryUserLabelList userId:{} error", userId, e);
            return Collections.emptyList();
        }
    }

    private List<EduUserLabelBo> convertEduUserLabelBo(List<EduUserLabelDto> list) {
        return list.stream().filter(Objects::nonNull)
                .map(t -> new EduUserLabelBo().setId(t.getId()).setUserId(t.getUserId()).setLabelId(t.getLabelId())
                        .setLabelName(t.getLabelName()).setUsedTimes(t.getUsedTimes()))
                .collect(Collectors.toList());
    }


}
