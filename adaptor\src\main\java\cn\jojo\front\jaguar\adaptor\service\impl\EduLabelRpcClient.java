package cn.jojo.front.jaguar.adaptor.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.jojo.edu.fantasy.rpc.api.dto.EduLabelDto;
import cn.jojo.edu.fantasy.rpc.api.req.EduLabelPageReq;
import cn.jojo.edu.fantasy.rpc.api.service.IEduLabelRpcService;
import cn.jojo.front.jaguar.adaptor.service.IEduLabelRpcClient;
import cn.jojo.front.jaguar.common.pojo.bo.EduLabelBo;
import cn.jojo.infra.sdk.api.metadata.IPageResp;
import cn.jojo.infra.sdk.api.metadata.IRpcResult;
import cn.jojo.infra.sdk.logging.JoJoLogging;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Component
public class EduLabelRpcClient implements IEduLabelRpcClient {

    @DubboReference
    private IEduLabelRpcService eduLabelRpcService;

    @Override
    public List<EduLabelBo> queryLabelInfoList(List<Long> labelIdList) {
        try {
            EduLabelPageReq req = new EduLabelPageReq();
            req.setLabelTypes(Collections.singletonList("ALBUM"));
            req.setLabelIds(labelIdList);
            IRpcResult<IPageResp<EduLabelDto>> rpcResult = eduLabelRpcService.queryLabelPage(req);
            JoJoLogging.logger(log)
                    .unIndex("rpcResult", JSON.toJSONString(rpcResult))
                    .info("EduLabelRpcClient queryLabelInfo labelIdList:{}", JSON.toJSONString(labelIdList));
            if (!rpcResult.checkSuccess() || Objects.isNull(rpcResult.getData())) {
                return Collections.emptyList();
            }
            IPageResp<EduLabelDto> pageResp = rpcResult.getData();
            if (CollUtil.isEmpty(pageResp.getPageRecords())) {
                return Collections.emptyList();
            }
            return convertEduLabelBo(pageResp.getPageRecords());
        } catch (Exception e) {
            log.error("EduLabelRpcClient queryLabelInfo labelIdList:{} error", JSON.toJSONString(labelIdList), e);
            return Collections.emptyList();
        }
    }

    private List<EduLabelBo> convertEduLabelBo(List<EduLabelDto> list) {
        return list.stream().filter(Objects::nonNull)
                .map(t -> new EduLabelBo().setId(t.getId()).setLabelName(t.getLabelName())
                        .setLabelType(t.getLabelType()))
                .collect(Collectors.toList());
    }

    @Override
    public Page<EduLabelDto> queryLabelPage(Integer pageNum, Integer pageSize, List<String> labelTypes, List<Long> labelIds, String labelName) {
        EduLabelPageReq req = EduLabelPageReq.builder().pageNum(pageNum).pageSize(pageSize).labelTypes(labelTypes).build();
        if(!CollectionUtils.isEmpty(labelIds)){
            req.setLabelIds(labelIds);
        }
        if(!StringUtils.isEmpty(labelName)){
            req.setLabelName(labelName);
        }
        try{
            IRpcResult<IPageResp<EduLabelDto>> rpcResult = eduLabelRpcService.queryLabelPage(req);
            if (!rpcResult.checkSuccess()) {
                JoJoLogging.logger(log).error("eduLabelRpcService.queryLabelPage failed,req:{},message:{}", req,
                        rpcResult.getMessage());
                return new Page<>();
            }
            Optional<IPageResp<EduLabelDto>> pageResult = Optional.ofNullable(rpcResult.getData());
            return new Page<EduLabelDto>()
                    .setRecords(pageResult.map(IPageResp::getPageRecords).orElse(Collections.emptyList()))
                    .setTotal(pageResult.map(IPageResp::getTotalCount).orElse(0L))
                    .setCurrent(pageResult.map(IPageResp::getPageNum).orElse(0L))
                    .setSize(pageResult.map(IPageResp::getPageSize).orElse(0L));
        }catch (Exception e){
            JoJoLogging.logger(log).error("eduLabelRpcService.queryLabelPage error,req:{},message:{}", req,
                    e.getMessage());
            return new Page<>();
        }
    }

}
