package cn.jojo.front.jaguar.admin.model.dto.task;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @description
 * @date 2024/07/30
 **/
@Data
@Schema(description="任务语音")
public class TaskVoiceDto {

    @Schema(description="配音文字")
    private String text;

    @Schema(description="交流参数")
    private String param;

    @Schema(description="备用音频")
    private String voiceUrl;
}
