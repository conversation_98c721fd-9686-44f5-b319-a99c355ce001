package cn.jojo.front.jaguar.adaptor.service.impl;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.jojo.edu.malacca.rpc.api.dto.EduUserClassDto;
import cn.jojo.edu.malacca.rpc.api.dto.EduUserClassExtensionDto;
import cn.jojo.edu.malacca.rpc.api.req.EduUserClassExtensionReq;
import cn.jojo.edu.malacca.rpc.api.req.EduUserClassQueryReq;
import cn.jojo.edu.malacca.rpc.api.req.enums.EduUserClassAggregationType;
import cn.jojo.edu.malacca.rpc.api.service.EduUserClassRpcService;
import cn.jojo.front.jaguar.adaptor.service.IEduUserClassRpcClient;
import cn.jojo.front.jaguar.common.pojo.bo.EduUserClassBo;
import cn.jojo.front.jaguar.common.pojo.req.QueryUserClassReq;
import cn.jojo.front.jaguar.common.pojo.vo.eduUserClass.EduUserClassExtensionVo;
import cn.jojo.front.jaguar.common.utils.ApolloUtil;
import cn.jojo.infra.sdk.api.metadata.IPageResp;
import cn.jojo.infra.sdk.api.metadata.IRpcResult;
import cn.jojo.infra.sdk.logging.JoJoLogging;
import cn.jojo.pagani.common.utils.CommonUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Component
@Slf4j
public class EduUserClassRpcClient extends AbstractRpcClient implements IEduUserClassRpcClient {

    private static final int NOT_PAGE = 1;

    private static final int START_PAGE_QUERY = 2;

    @Reference
    private EduUserClassRpcService eduUserClassRpcService;



    @Override
    public List<EduUserClassExtensionVo> queryExtensionByParam(Long userId, Integer pageNum, Integer pageSize) {
        EduUserClassExtensionReq req = EduUserClassExtensionReq.builder().build();
        EduUserClassQueryReq queryReq = EduUserClassQueryReq.builder()
                .userId(userId)
                .build();
        req.setReq(queryReq);
        if(pageNum != null){
            req.setPageNum(pageNum);
        }
        if(pageSize != null){
            req.setPageSize(pageSize.longValue());
        }
        req.setExtensionTypes(
            Lists.newArrayList(EduUserClassAggregationType.USER_COURSE, EduUserClassAggregationType.CLASS,
                EduUserClassAggregationType.COURSE, EduUserClassAggregationType.TEACHER,
                EduUserClassAggregationType.UNLOCK_ALL_LESSON));
        IRpcResult<IPageResp<EduUserClassExtensionDto>> rpcResult =
                eduUserClassRpcService.queryExtension(req);
        List<EduUserClassExtensionVo> dtoList = Lists.newArrayList();
        if (!rpcResult.checkSuccess()) {
            log.error("invoke eduUserClassRpcService.queryExtension failed, messages={}",
                    rpcResult.getMessage());
            return dtoList;
        }
        IPageResp<EduUserClassExtensionDto> pageResp = rpcResult.getData();
        if(CollectionUtils.isEmpty(pageResp.getPageRecords())){
            return dtoList;
        }
        JoJoLogging.logger(log)
                .unIndex("当前用户班级聚合信息为: {}", JSON.toJSONString(pageResp.getPageRecords().stream().filter(Objects::nonNull).map(EduUserClassExtensionDto::getUserClassVo).collect(Collectors.toList())))
                .info("request：{}", req);
        dtoList.addAll(pageResp.getPageRecords().stream().filter(Objects::nonNull)
                .map(this::assembleClassExtensionVo).collect(
                        Collectors.toList()));
        int totalCount = (int) pageResp.getTotalCount();
        int currentSize = pageResp.getPageRecords().size();
        int numIndex = 1;
        while (totalCount > currentSize * numIndex) {
            req.setPageNum(++numIndex);
            IRpcResult<IPageResp<EduUserClassExtensionDto>> subDtoIPageResp =
                    eduUserClassRpcService.queryExtension(req);
            if (!subDtoIPageResp.checkSuccess()) {
                log.error("invoke eduUserClassRpcService.queryExtension failed, messages={}",
                        rpcResult.getMessage());
                continue;
            }
            if(subDtoIPageResp.getData() != null && !CollectionUtils.isEmpty(subDtoIPageResp.getData().getPageRecords())){
                JoJoLogging.logger(log)
                        .unIndex("当前用户班级聚合信息为: {}", JSON.toJSONString(subDtoIPageResp.getData().getPageRecords().stream().filter(Objects::nonNull).map(EduUserClassExtensionDto::getUserClassVo).collect(Collectors.toList())))
                        .info("request：{}", req);
                dtoList.addAll(subDtoIPageResp.getData().getPageRecords().stream().filter(Objects::nonNull)
                        .map(this::assembleClassExtensionVo).collect(
                                Collectors.toList()));
            }
        }

        return dtoList.stream()
            .filter(i -> {
                if (!ApolloUtil.getTenantRpcFilterSwitch()) {
                    return true;
                }

                if (i == null) {
                    log.warn("userClassExtCheck.userClassExtensionDto.null");
                    return false;
                }

                if (i.getUserClassVo() == null) {
                    log.warn("userClassExtCheck.userClassVo.null");
                    return true;
                }

                String ssTenantId = CommonUtil.getSsTenantId();
                if (!ssTenantId.equals(i.getUserClassVo().getSsTenantId())) {
                    log.warn("班期租户不一致[userClassExtCheck]");
                    return false;
                }

                return true;
            }).collect(Collectors.toList());
    }

    @Override
    public List<EduUserClassExtensionDto> queryByOrderId(Long userId, String orderId) {
        if (orderId == null || userId == null) {
            return Collections.emptyList();
        }
        if ("-1".equals(orderId)) {
            return Collections.emptyList();
        }
        EduUserClassExtensionReq req = EduUserClassExtensionReq.builder()
                .req(EduUserClassQueryReq.builder().userId(userId).orderNo(orderId).build())
                .build();
        try {
            IRpcResult<IPageResp<EduUserClassExtensionDto>> rpcResult = eduUserClassRpcService.queryExtension(req);
            if (!rpcResult.checkSuccess()) {
                log.error("invoke eduUserClassRpcService.queryExtension failed, message={}", rpcResult.getMessage());
                return Collections.emptyList();
            }
            return Optional.ofNullable(rpcResult.getData())
                    .map(IPageResp::getPageRecords).orElse(Collections.emptyList());
        } catch (Exception e) {
            log.error("invoke eduUserClassRpcService.queryExtension failed", e);
            return Collections.emptyList();
        }
    }

    @Override
    public EduUserClassDto queryByClassId(Long userId, Long classId) {
        if (classId == null || userId == null) {
            return null;
        }
        //在教务校验用户是否有班级信息
        EduUserClassQueryReq req = new EduUserClassQueryReq();
        req.setUserId(userId);
        req.setClassId(classId);
        try {
            IRpcResult<List<EduUserClassDto>> classResult = eduUserClassRpcService.queryUserClassList(req);
            if (!classResult.checkSuccess()) {
                log.error("invoke eduUserClassRpcService.queryUserClassList failed, message={}", classResult.getMessage());
                return null;
            }
            boolean checkResult = CollUtil.isNotEmpty(classResult.getData());
            if (checkResult) {
                return classResult.getData().get(0);
            } else {
                log.info("没有班级信息的用户，userId={}", userId);
                return null;
            }
        } catch (Exception e) {
            log.error("invoke eduUserClassRpcService.queryExtension failed", e);
            return null;
        }
    }

    @Override
    public List<EduUserClassBo> queryUserClasses(QueryUserClassReq req) {
        if (req.getUserId() == null) {
            return Collections.emptyList();
        }
        return doRpcList(BeanUtil.copyProperties(req, EduUserClassQueryReq.class),
            eduUserClassRpcService::queryUserClassList,
            EduUserClassBo.class);

    }


    private EduUserClassExtensionVo assembleClassExtensionVo(EduUserClassExtensionDto eduUserClassExtensionDto) {
        EduUserClassExtensionVo vo = new EduUserClassExtensionVo();
        BeanUtil.copyProperties(eduUserClassExtensionDto, vo);
        return vo;
    }


}
