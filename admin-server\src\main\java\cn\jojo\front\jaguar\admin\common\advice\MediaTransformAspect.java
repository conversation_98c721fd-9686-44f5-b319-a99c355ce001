package cn.jojo.front.jaguar.admin.common.advice;

import cn.jojo.front.jaguar.biz.service.av1.GlobalMediaAv1SceneClient;
import cn.jojo.front.jaguar.common.annotation.Av1Transform;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.lang.annotation.Annotation;
import java.lang.reflect.Field;
import java.util.Collection;
import java.util.HashSet;
import java.util.Set;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 */
@Component
@Aspect
@Slf4j
public class MediaTransformAspect {

    @Resource
    private GlobalMediaAv1SceneClient globalMediaAv1SceneClient;

    @Value("${av1.transform.switch:true}")
    private Boolean av1TransformSwitch;

    @AfterReturning(value = "@annotation(cn.jojo.front.jaguar.common.annotation.Av1Transform)")
    public void processAv1(JoinPoint joinPoint) {
        log.info("开始进行AV1转换");
        if (Boolean.FALSE.equals(av1TransformSwitch)) {
            return;
        }
        Object[] args = joinPoint.getArgs();
        if (args == null || args.length == 0) {
            return;
        }
        MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
        Annotation[][] paramAnnotationTypes = methodSignature.getMethod().getParameterAnnotations();
        Set<String> processUrls = new HashSet<>();
        for (int i = 0; i < args.length; i++) {
            // 处理方法参数上带Av1Transform注解的字符串
            boolean isAv1 = Stream.of(paramAnnotationTypes[i])
                    .anyMatch(item -> item.annotationType() == Av1Transform.class);
            if (args[i] instanceof String && isAv1) {
                processUrls.add((String) args[i]);
                continue;
            }
            if (Collection.class.isAssignableFrom(args[i].getClass())) {
                processCollection((Collection<?>) args[i], processUrls);
            } else {
                for (Field field : args[i].getClass().getDeclaredFields()) {
                    field.setAccessible(true);
                    Object o = null;
                    try {
                        o = field.get(args[i]);
                    } catch (IllegalAccessException e) {
                        log.info("av1转换失败", e);
                    }
                    searchAv1Url(field, o, processUrls);
                }
            }
        }
        // 把链接进行av1转换
        doSubmit(processUrls);
    }

    private void doSubmit(Set<String> processUrls) {
        log.info("目标转换地址为：{}", JSON.toJSONString(processUrls));
        if (CollectionUtils.isEmpty(processUrls)) {
            return;
        }
        for (String url : processUrls) {
            if (isNotMp4(url)) {
                continue;
            }
            globalMediaAv1SceneClient.submitAv1Transform(url);
        }
    }

    private boolean isNotMp4(String url) {
        if (StringUtils.isBlank(url)) {
            return true;
        }
        return Stream.of(url.split("\\?"))
                .findFirst()
                .map(item -> !item.endsWith(".mp4") && !item.endsWith(".MP4"))
                .orElse(true);
    }

    private void processCollection(Collection<?> collection, Set<String> processUrls) {
        if (collection == null) {
            return;
        }
        for (Object o : collection) {
            if (Collection.class.isAssignableFrom(o.getClass())) {
                processCollection((Collection<?>) o, processUrls);
            }
            if (o.getClass().isPrimitive() || o.getClass().getName().startsWith("java.") || o.getClass()
                    .getName().startsWith("sun.") || o.getClass().isEnum()) {
                continue;
            }
            for (Field childField : o.getClass().getDeclaredFields()) {
                childField.setAccessible(true);
                Object childObj = null;
                try {
                    childObj = childField.get(o);
                } catch (IllegalAccessException e) {
                    log.info("av1转换失败", e);
                }
                if (childObj == null) {
                    continue;
                }
                searchAv1Url(childField, childObj, processUrls);
            }
        }
    }

    private void searchAv1Url(Field field, Object arg, Set<String> processUrls) {
        if (arg == null || field == null) {
            return;
        }
        // 字段get方法不存在，不做处理
        if (!getMethodPresent(field)) {
            return;
        }
        if (field.isAnnotationPresent(Av1Transform.class)) {
            if (arg instanceof String) {
                processUrls.add((String) arg);
                return;
            }
            if (Collection.class.isAssignableFrom(arg.getClass())) {
                Collection<?> temp = (Collection<?>) arg;
                for (Object o : temp) {
                    if (o instanceof String) {
                        processUrls.add((String) o);
                    }
                }
                return;
            }
        }
        if (Collection.class.isAssignableFrom(arg.getClass())) {
            processCollection((Collection<?>) arg, processUrls);
        }

        if (field.getType().isPrimitive() || field.getType().getName().startsWith("java.") || field.getType()
                .getName().startsWith("sun.") || arg.getClass().isEnum()) {
            return;
        }
        for (Field childField : arg.getClass().getDeclaredFields()) {
            childField.setAccessible(true);
            Object o = null;
            try {
                o = childField.get(arg);
            } catch (IllegalAccessException e) {
                log.info("av1转换失败", e);
            }
            if (o == null) {
                continue;
            }
            searchAv1Url(childField, o, processUrls);
        }
    }

    private static boolean getMethodPresent(Field field) {
        String methodName = "get" + capitalize(field.getName());
        try {
            field.getDeclaringClass().getDeclaredMethod(methodName);
            return true;
        } catch (NoSuchMethodException e) {
            return false;
        }
    }

    public static String capitalize(String str) {
        if (str == null || str.isEmpty()) {
            return str;
        }
        return Character.toUpperCase(str.charAt(0)) + str.substring(1);
    }
}
