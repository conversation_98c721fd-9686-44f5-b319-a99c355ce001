package cn.jojo.front.jaguar.adaptor.service;


import cn.jojo.edu.study.rpc.api.dto.UserLessonRecordFinshDto;
import cn.jojo.front.jaguar.common.pojo.req.UserStudyRecordCacheQueryRpcReq;

import java.util.List;

/**
 * 用户内容学习记录 RPC Client
 *
 * <AUTHOR> Date: 2020/9/21 Time: 14:40 Description: No Description
 */
public interface IUserLessonRecordRpcClient {

    /**
     * 判断是否完成课时
     *
     * @param userId
     * @param classId
     * @param lessonIds
     * @return
     */
    List<Long> queryFinishLessonIdsFromCache(UserStudyRecordCacheQueryRpcReq rpcReq);


    List<UserLessonRecordFinshDto> queryFinishRecordFromCache(UserStudyRecordCacheQueryRpcReq rpcReq);

}
