package cn.jojo.front.jaguar.admin.controller;

import cn.jojo.common.api.ex.DefaultHttpActionResult;
import cn.jojo.common.api.ex.IHttpActionResult;
import cn.jojo.front.jaguar.common.pojo.bo.EmployeeBo;
import cn.jojo.front.jaguar.common.pojo.entity.picturebook.PictureBookConfiguration;
import cn.jojo.front.jaguar.common.pojo.req.BaseListReq;
import cn.jojo.front.jaguar.common.pojo.req.PictureBookConfigurationSaveReq;
import cn.jojo.front.jaguar.common.pojo.req.PriorityUpdateReq;
import cn.jojo.front.jaguar.common.pojo.vo.PictureBookAgeVo;
import cn.jojo.front.jaguar.common.pojo.vo.PictureBookConfigurationListVo;
import cn.jojo.front.jaguar.common.pojo.vo.PictureBookConfigurationVo;
import cn.jojo.front.jaguar.core.service.picturebook.PictureBookConfigurationDetailService;
import cn.jojo.front.jaguar.core.service.picturebook.PictureBookConfigurationService;
import cn.jojo.infra.sdk.api.metadata.DefaultHttpResult;
import cn.jojo.infra.sdk.api.metadata.IHttpResult;
import cn.jojo.infra.sdk.api.metadata.IPageResp;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;

import java.util.Collections;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.constraints.NotNull;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@Tag(name = "Jaguar管理后台")
@Validated
@RestController
@RequestMapping(value = "admin/picture-book-configuration")
public class PictureBookConfigurationController extends BaseController {

    @Resource
    private PictureBookConfigurationService pictureBookConfigurationService;
    @Resource
    private PictureBookConfigurationDetailService pictureBookConfigurationDetailService;

    /**
     * 查询绘本配置列表
     *
     * @param req 查询参数
     * @return 绘本配置列表
     */
    @GetMapping("/list")
    @Operation(summary ="绘本配置详情列表",   description = "绘本配置详情列表")
    public IHttpActionResult<IPageResp<PictureBookConfigurationListVo>> listPictureBook(BaseListReq<Void> req) {
        IPageResp<PictureBookConfigurationListVo> result = pictureBookConfigurationService.listPictureBook(req);
        return DefaultHttpActionResult.successWithData(result);
    }

    /**
     * 更改优先级
     *
     * @param req 入参
     * @return 操作结果
     */
    @PostMapping("/priority/update")
    @Operation(summary ="绘本配置优先级",   description = "更新绘本配置优先级")
    public IHttpResult<Boolean> updateContentConfigurationPriority(
        @RequestBody @Validated PriorityUpdateReq req) {
        pictureBookConfigurationService.updatePriority(req);
        pictureBookConfigurationService.deleteCache(Collections.singleton(req.getBusinessId().longValue()));
        return DefaultHttpResult.successWithData(true);
    }

    /**
     * 保存绘本配置
     *
     * @param req 保存参数
     * @return 绘本配置id
     */
    @PostMapping("/saveOrUpdate")
    @Operation(summary ="保存绘本配置",   description = "保存绘本配置")
    public IHttpActionResult<Long> saveOrUpdateAdvertisement(@RequestBody @Validated
        PictureBookConfigurationSaveReq req) {
        PictureBookConfiguration configuration = pictureBookConfigurationService.saveOrUpdate(req, new EmployeeBo()
            .setEmployeeId(getEmployeeId())
            .setEmployeeName(getEmployeeName()));
        pictureBookConfigurationService.deleteCache(Collections.singleton(configuration.getId()));
        pictureBookConfigurationDetailService.deleteCache(Collections.singleton(configuration.getId()));
        return DefaultHttpActionResult.successWithData(configuration.getId());
    }

    /**
     * 查询绘本配置详情
     *
     * @param pictureBookConfigurationId 绘本配置id
     * @return 绘本配置详情
     */
    @GetMapping("/detail")
    @Operation(summary ="绘本配置详情",   description = "绘本配置详情")
    public IHttpActionResult<PictureBookConfigurationVo> detail(@RequestParam @NotNull Long pictureBookConfigurationId) {
        PictureBookConfigurationVo result =
            pictureBookConfigurationService.getPictureBookConfigurationInfo(pictureBookConfigurationId);
        return DefaultHttpActionResult.successWithData(result);
    }

    /**
     * 删除绘本配置
     *
     * @param pictureBookConfigurationId 绘本配置id
     * @return 是否成功
     */
    @GetMapping("/delete")
    @Operation(summary ="删除绘本配置",   description = "删除绘本配置")
    public IHttpResult<Boolean> delete(@RequestParam @NotNull Long pictureBookConfigurationId) {
        pictureBookConfigurationService.delete(pictureBookConfigurationId, new EmployeeBo()
            .setEmployeeId(getEmployeeId())
            .setEmployeeName(getEmployeeName()));
        pictureBookConfigurationService.deleteCache(Collections.singleton(pictureBookConfigurationId));
        pictureBookConfigurationDetailService.deleteCache(Collections.singleton(pictureBookConfigurationId));
        return DefaultHttpResult.successWithData(true);
    }

    /**
     * 查询年龄列表
     *
     * @return 年龄列表
     */
    @GetMapping(value = "/findAges")
    @Operation(summary ="年龄列表")
    public IHttpResult<List<PictureBookAgeVo>> findAges() {
        List<PictureBookAgeVo> result = pictureBookConfigurationService.findAllAges();
        return DefaultHttpResult.successWithData(result);
    }
}
