package cn.jojo.front.jaguar.admin.controller;

import cn.jojo.front.jaguar.common.annotation.Av1Transform;
import cn.jojo.front.jaguar.common.pojo.req.BannerCardReq;
import cn.jojo.front.jaguar.common.pojo.req.BannerCardSaveReq;
import cn.jojo.front.jaguar.common.pojo.req.BannerClipsMaterialReq;
import cn.jojo.front.jaguar.common.pojo.req.BannerClipsTaskReq;
import cn.jojo.front.jaguar.common.pojo.vo.BannerCardPageVo;
import cn.jojo.front.jaguar.common.pojo.vo.BannerClipsMaterialContentVo;
import cn.jojo.front.jaguar.core.service.plate.NewBannerService;
import cn.jojo.infra.sdk.api.metadata.DefaultHttpResult;
import cn.jojo.infra.sdk.api.metadata.IHttpResult;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import java.util.List;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


/**
 * @Description: 伴读新banner
 * @Copyright: Copyright (c) 2022  ALL RIGHTS RESERVED.
 * @Company: 书声科技有限公司
 * @Author: chenHao
 * @CreateDate: 2023/1/30 11:29
 * @UpdateDate: 2023/1/30 11:29
 * @UpdateRemark: init
 * @Version: 1.0
 */
@RestController
@Tag(name = "伴读新增banner")
@RequestMapping(value = "/admin/newBanner")
@Slf4j
@Validated
public class NewBannerController {

    @Autowired
    private NewBannerService newBannerService;


    @GetMapping(value = "/getBannerCardList")
    @Operation(summary ="banner数据明细查询",   description = "获取banner数据明细")
    public IHttpResult<List<BannerCardPageVo>> getBannerCardList(@Validated BannerCardReq<Void> req) {
        return DefaultHttpResult.successWithData(newBannerService.getBannerList(req));
    }

    @Av1Transform
    @PostMapping("/saveBannerCard")
    @Operation(summary ="BannerCard数据保存",   description = "保存BannerCard")
    public IHttpResult<Void> saveBannerCard(@RequestBody @Validated BannerCardSaveReq req) {
        newBannerService.saveOrUpdate(req);
        return DefaultHttpResult.successWithoutData();
    }

    @PostMapping(value = "/clipsVideo")
    @Operation(summary ="生成剪辑banner素材视频",   description = "生成剪辑banner资源视频")
    public IHttpResult<String> clipsBannerMaterial(@RequestBody @Validated BannerClipsMaterialReq req) {
        return DefaultHttpResult
            .successWithData(newBannerService.clipsBannerMaterial(req));
    }

    @GetMapping(value = "/getClipsInfo")
    @Operation(summary ="获取剪辑banner素材视频信息",   description = "获取作品结果")
    public IHttpResult<BannerClipsMaterialContentVo> getClipsBannerMaterialInfo(@RequestParam("taskId") @NotNull @Positive Long taskId) {
        return DefaultHttpResult
            .successWithData(newBannerService.getClipsBannerMaterialInfo(taskId));
    }

    @PostMapping("/getClipsInfos")
    @Operation(summary ="获取剪辑banner素材视频信息列表",   description = "获取作品结果列表")
    public IHttpResult<List<BannerClipsMaterialContentVo>> getClipsBannerMaterialInfo(@RequestBody @Validated BannerClipsTaskReq req) {
        return DefaultHttpResult.successWithData(newBannerService.getClipsBannerMaterialInfos(req.getTaskIds()));
    }

}
