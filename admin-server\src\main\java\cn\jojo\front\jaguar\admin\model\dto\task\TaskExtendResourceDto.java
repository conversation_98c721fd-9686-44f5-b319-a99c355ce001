package cn.jojo.front.jaguar.admin.model.dto.task;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 游戏资源模型
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TaskExtendResourceDto implements Serializable {

    /**
     * 节点奖励展示资源
     */
    private String rewardDisplayUrl;

    /**
     * 主文案
     */
    private String mainText;

    /**
     * 副文案
     */
    private String subText;

}
