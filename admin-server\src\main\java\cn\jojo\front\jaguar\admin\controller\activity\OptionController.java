package cn.jojo.front.jaguar.admin.controller.activity;

import cn.jojo.front.jaguar.admin.model.convert.QueryOptionRequest2OptionVoConvert;
import cn.jojo.front.jaguar.admin.model.req.QueryOptionsRequest;
import cn.jojo.front.jaguar.common.pojo.vo.activity.DictionaryAdminVo;
import cn.jojo.front.jaguar.common.pojo.vo.activity.QueryOptionsVo;
import cn.jojo.infra.sdk.api.metadata.DefaultHttpResult;
import cn.jojo.infra.sdk.api.metadata.IHttpResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Map;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @description: 公共枚举接口
 * @author: wangzhilei
 * @date: 2022/12/8
 **/
@Schema(name="公共下拉选枚举查询接口")
@RestController
@RequestMapping("/admin")
public class OptionController {

    @Resource
    private OptionsServiceImpl optionsService;

    /**
     * 下拉选枚举查询接口
     * @see cn.jojo.edu.fantasy.common.dict.video.ResolutionType
     *
     * @param queryOptionsRequest 查询选项请求
     * @return {@link IHttpResult}<{@link Map}<{@link String}, {@link Object}>>
     */
    @GetMapping("/dictionaries")
    @Operation(summary = "公共下拉枚举查询接口", description = "入参为true则查询对应枚举，可查询多个，可通过filterCode过滤父类型")
    public IHttpResult<DictionaryAdminVo> queryOptions(@ModelAttribute QueryOptionsRequest queryOptionsRequest) {
        QueryOptionsVo queryOptionsVo = QueryOptionRequest2OptionVoConvert.INSTANCE.model1ToModel2(queryOptionsRequest);
        try {
            return DefaultHttpResult.successWithData(optionsService.queryOptions(queryOptionsVo));
        } catch (IllegalAccessException e) {
            throw new IllegalStateException("Please contact the administrator");
        }
    }
}
