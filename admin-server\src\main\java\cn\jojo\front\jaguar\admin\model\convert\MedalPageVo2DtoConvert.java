package cn.jojo.front.jaguar.admin.model.convert;

import cn.jojo.edu.common.utils.convert.BaseModelConvert;
import cn.jojo.front.jaguar.admin.model.dto.task.MedalPageDto;
import cn.jojo.front.jaguar.biz.service.pojo.bo.task.MedalPageVo;
import cn.jojo.front.jaguar.common.enums.task.MedalRarityEnum;
import java.util.Optional;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;


/**
 * vo转换
 *
 * <AUTHOR>
 * @date 2022/05/16
 */
@Mapper
public interface MedalPageVo2DtoConvert extends BaseModelConvert<MedalPageVo, MedalPageDto> {

    MedalPageVo2DtoConvert INSTANCE = Mappers.getMapper(MedalPageVo2DtoConvert.class);

    @Override
    @Mapping(target = "rarityName", source = "rarity", qualifiedByName = "rarityName")
    MedalPageDto model1ToModel2(MedalPageVo model);

    /**
     *  获取名称
     * @param rarity
     * @return
     */
    @Named("rarityName")
    default String rarityName(Integer rarity) {
        return Optional.ofNullable(MedalRarityEnum.get(rarity)).map(MedalRarityEnum::getName).orElse("");
    }

}
