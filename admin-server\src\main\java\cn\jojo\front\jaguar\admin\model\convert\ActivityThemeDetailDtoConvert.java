package cn.jojo.front.jaguar.admin.model.convert;

import cn.jojo.edu.common.utils.convert.BaseModelConvert;
import cn.jojo.front.jaguar.admin.model.dto.task.activity.ActivityThemeDetailDto;
import cn.jojo.front.jaguar.common.pojo.vo.activity.ActivityThemeDetailVo;
import org.mapstruct.Mapper;

@Mapper
public interface ActivityThemeDetailDtoConvert extends BaseModelConvert<ActivityThemeDetailVo, ActivityThemeDetailDto> {

    ActivityThemeDetailDtoConvert INSTANCE = org.mapstruct.factory.Mappers.getMapper(
        ActivityThemeDetailDtoConvert.class);
}
