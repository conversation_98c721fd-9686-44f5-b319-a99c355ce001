package cn.jojo.front.jaguar.adaptor.service.impl;


import cn.jojo.edu.fantasy.rpc.api.dto.EduUserHubObtainDto;
import cn.jojo.edu.fantasy.rpc.api.dto.EduUserHubSimpleDto;
import cn.jojo.edu.fantasy.rpc.api.req.EduUserHubListReq;
import cn.jojo.edu.fantasy.rpc.api.req.EduUserHubObtainListReq;
import cn.jojo.edu.fantasy.rpc.api.service.IEduHubRpcService;
import cn.jojo.edu.fantasy.rpc.api.service.IEduUserHubRpcService;
import cn.jojo.front.jaguar.adaptor.service.IEduUserHubRpcClient;
import cn.jojo.front.jaguar.common.bo.UserHub;
import cn.jojo.front.jaguar.common.pojo.bo.EduUserHubObtainBo;
import cn.jojo.front.jaguar.common.pojo.req.UserHubObtainListReq;
import cn.jojo.infra.sdk.api.metadata.IRpcResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Component
@Slf4j
public class EduUserHubRpcClient implements IEduUserHubRpcClient {

    @Reference
    private IEduUserHubRpcService eduUserHubRpcService;

    @Reference
    private IEduHubRpcService eduHubRpcService;


    @Override
    public List<UserHub> queryUserHubList(List<Long> userIds, List<Long> hubIds, String status) {
        if (CollectionUtils.isEmpty(userIds)) {
            return Collections.emptyList();
        }
        EduUserHubListReq req = EduUserHubListReq.builder()
                .hubIds(hubIds)
                .userIds(userIds)
                .status(status)
                .build();
        IRpcResult<List<EduUserHubSimpleDto>> rpcResult = eduUserHubRpcService.queryUserHubList(req);
        if (!rpcResult.checkSuccess()) {
            log.error("invoke eduUserHubRpcService.queryUserHubList failed, message={}", rpcResult.getMessage());
            return Collections.emptyList();
        }
        return Optional.ofNullable(rpcResult.getData())
                .orElse(Collections.emptyList())
                .stream()
                .map(item -> {
                    UserHub userHub = new UserHub();
                    BeanUtils.copyProperties(item, userHub);
                    return userHub.setStatus(item.getStatus());
                }).collect(Collectors.toList());
    }

    @Override
    public List<EduUserHubObtainBo> queryUserHubObtainList(UserHubObtainListReq req){
        if (CollectionUtils.isEmpty(req.getUserIds())) {
            return Collections.emptyList();
        }
        EduUserHubObtainListReq rpcReq = EduUserHubObtainListReq
            .builder()
            .bizIds(req.getBizIds())
            .bizTypes(req.getBizTypes())
            .hubIds(req.getHubIds())
            .status(req.getStatus())
            .userIds(req.getUserIds())
            .build();
        IRpcResult<List<EduUserHubObtainDto>> rpcResult = eduUserHubRpcService.queryUserHubObtainList(rpcReq);
        if (!rpcResult.checkSuccess()) {
            log.error("invoke queryUserHubObtainList failed, message={}", rpcResult.getMessage());
            return Collections.emptyList();
        }
        return Optional.ofNullable(rpcResult.getData()).orElse(Collections.emptyList())
            .stream()
            .map(item -> {
                EduUserHubObtainBo result = new EduUserHubObtainBo();
                BeanUtils.copyProperties(item, result);
                return result;
            }).collect(Collectors.toList());
    }
}
