package cn.jojo.front.jaguar.admin.controller;

import cn.jojo.common.api.ex.DefaultHttpActionPageResult;
import cn.jojo.common.api.ex.DefaultHttpActionResult;
import cn.jojo.common.api.ex.IHttpActionResult;
import cn.jojo.front.jaguar.biz.service.ChannelBizService;
import cn.jojo.front.jaguar.common.pojo.bo.PageBo;
import cn.jojo.front.jaguar.common.pojo.req.PlacementPlansListReq;
import cn.jojo.front.jaguar.common.pojo.vo.ChannelVo;
import cn.jojo.front.jaguar.common.pojo.vo.PlacementPlanListVo;
import cn.jojo.infra.sdk.api.metadata.DefaultHttpPageResult;
import cn.jojo.infra.sdk.api.metadata.IPageResp;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/admin")
@Validated
public class ChannelController {
    @Resource
    private ChannelBizService channelBizService;

    @GetMapping("/channels")
    @Operation(summary = "获取渠道信息", description = "获取app渠道信息，获取所有的渠道")
    public IHttpActionResult<List<ChannelVo>> channelList() {
        List<ChannelVo> channels = channelBizService.getAllParentChannels();
        return DefaultHttpActionResult.successWithData(channels);
    }

    @GetMapping("/placement-plans")
    @Operation(summary = "获取投放计划信息", description = "分页获取投放计划信息，可以根据各种条件进行筛选")
    public IHttpActionResult<IPageResp<PlacementPlanListVo>> placementPlans(PlacementPlansListReq req) {
        if (req.getPageNum() == null || req.getPageNum() <= 0L) {
            req.setPageNum(1L);
        }
        if (req.getPageSize() == null || req.getPageSize() <= 0L) {
            req.setPageSize(20L);
        }
        PageBo<PlacementPlanListVo> page = channelBizService.getPlacementPlans(req);
        return DefaultHttpActionPageResult.successWithPageData(
            page.getPageNum(), page.getPageSize(), page.getTotal(), page.getData());
    }
}
