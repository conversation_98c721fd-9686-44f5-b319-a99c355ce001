package cn.jojo.front.jaguar.admin.model.convert;

import cn.jojo.edu.common.utils.convert.BaseModelConvert;
import cn.jojo.front.jaguar.admin.model.req.QueryOptionsRequest;
import cn.jojo.front.jaguar.common.pojo.vo.activity.QueryOptionsVo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 下拉选查询req 转vo
 *
 * <AUTHOR>
 * @date 2022/05/16
 */
@Mapper
public interface QueryOptionRequest2OptionVoConvert extends BaseModelConvert<QueryOptionsRequest, QueryOptionsVo> {

    QueryOptionRequest2OptionVoConvert INSTANCE = Mappers.getMapper(QueryOptionRequest2OptionVoConvert.class);
}
