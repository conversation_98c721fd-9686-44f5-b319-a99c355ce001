package cn.jojo.front.jaguar.admin.controller;

import cn.jojo.front.jaguar.common.enums.PlateType;
import cn.jojo.front.jaguar.common.pojo.bo.EmployeeBo;
import cn.jojo.front.jaguar.common.pojo.entity.plate.Plate;
import cn.jojo.front.jaguar.common.pojo.req.PlateListReq;
import cn.jojo.front.jaguar.common.pojo.req.PlateSaveReq;
import cn.jojo.front.jaguar.common.pojo.req.PlateStatusUpdateReq;
import cn.jojo.front.jaguar.common.pojo.vo.PlateListVo;
import cn.jojo.front.jaguar.core.service.plate.PlateService;
import cn.jojo.infra.sdk.api.metadata.DefaultHttpResult;
import cn.jojo.infra.sdk.api.metadata.DefaultPageResp;
import cn.jojo.infra.sdk.api.metadata.IHttpResult;
import cn.jojo.infra.sdk.api.metadata.IPageResp;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotNull;
import java.util.Optional;

/**
 * Description:板块接口 date: 2021/4/9 11:07
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/admin/plate")
@Tag(name = "Jaguar管理后台")
@Validated
public class PlateController extends BaseController {

    @Autowired
    private PlateService plateService;

    /**
     * @Description: 查询板块列表
     * @author: xr
     * @date: 2021/4/9
     */
    @GetMapping("/getPlateList")
    @Operation(summary ="板块列表",   description = "获取板块列表")
    public IPageResp<PlateListVo> getPlateList(PlateListReq<Void> req) {
        IPage<PlateListVo> page = plateService.getPlateList(req);
        return DefaultPageResp.buildPageResp(req, page.getTotal(), page.getRecords());
    }

    /**
     * @Description: 保存或修改板块
     * @author: xr
     * @date: 2021/4/9
     */
    @PostMapping("/savePlate")
    @Operation(summary ="plate板块保存和修改",   description = "plate板块保存和修改")
    public IHttpResult<Integer> savePlate(@RequestBody @Validated PlateSaveReq req) {
        req.setPlateType(Optional.ofNullable(req.getPlateType()).orElse(PlateType.JOJO_PLATE));
        Plate plate = plateService.saveOrUpdatePlate(req, new EmployeeBo()
            .setEmployeeId(getEmployeeId())
            .setEmployeeName(getEmployeeName()));
        plateService.deleteCache(plate.getPlateType(), req.getDelaminationPageId());
        return DefaultHttpResult.successWithData(plate.getId());
    }

    /**
     * @Description: 隐藏和取消隐藏和删除
     * @author: xr
     * @date: 2021/4/9
     */
    @PostMapping("/updatePlateStatus")
    @Operation(summary ="plate板块保存和修改",   description = "plate板块保存和修改")
    public IHttpResult<Integer> updatePlateStatus(@RequestBody @Validated PlateStatusUpdateReq req) {
        Plate plate = plateService.updatePlateStatus(req, new EmployeeBo()
            .setEmployeeId(getEmployeeId())
            .setEmployeeName(getEmployeeName()));
        plateService.deleteCache(plate.getPlateType(), plate.getDelaminationPageId());
        return DefaultHttpResult.successWithData(plate.getId());
    }

    /**
     * @Description: 上移或下移
     * @author: xr
     * @date: 2021/4/9
     */
    @PostMapping("/upAndDown")
    @Operation(summary ="plate板块上移或下移",   description = "plate板块上移或下移")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "id", value = "板块ID", required = true),
        @ApiImplicitParam(name = "type", value = "1：上移 2：下移", required = true)
    })
    public IHttpResult<Integer> upAndDown(@RequestParam("id") @NotNull Integer id,
                                          @RequestParam("type") @NotNull Integer type,
                                          @RequestParam(value = "plateType", required = false) String plateType,
                                          @RequestParam(required = false, defaultValue = "1") Long delaminationPageId) {
        PlateType plateTypeEnum = PlateType.of(plateType);

        plateService.updateSort(id, type, plateTypeEnum, delaminationPageId, new EmployeeBo()
            .setEmployeeId(getEmployeeId())
            .setEmployeeName(getEmployeeName()));

        return DefaultHttpResult.successWithoutData();
    }
}
