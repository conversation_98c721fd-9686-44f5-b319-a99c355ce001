package cn.jojo.front.jaguar.adaptor.service;


import cn.jojo.cc.common.dto.PropGroupDto;
import cn.jojo.front.jaguar.common.pojo.req.StickerAlbumReq;
import cn.jojo.front.jaguar.common.pojo.req.course.CardReq;
import cn.jojo.front.jaguar.common.pojo.vo.activity.CardVo;
import cn.jojo.front.jaguar.common.pojo.vo.activity.StickerPageVo;
import java.util.List;
import java.util.Map;

public interface ICourseCardRpcClient {

    /**
     * 根据key获取卡牌
     * @param req
     * @return
     */
    CardVo getCardByKey(CardReq req);


    /**
     * 根据IDS获取活动卡
     * @param req
     * @return
     */
    List<CardVo> getCardByIds(CardReq req);


}
