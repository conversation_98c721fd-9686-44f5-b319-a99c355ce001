package cn.jojo.front.jaguar.adaptor.service.impl;

import cn.jojo.edu.wings.rpc.api.dto.level.LevelDetailDto;
import cn.jojo.edu.wings.rpc.api.req.level.LevelDetailQueryReq;
import cn.jojo.edu.wings.rpc.api.service.ILevelRpcService;
import cn.jojo.front.jaguar.adaptor.service.ILevelRpcClient;
import cn.jojo.infra.sdk.api.metadata.IRpcResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class LevelRpcClient implements ILevelRpcClient {

    @DubboReference
    private ILevelRpcService levelRpcService;

    @Override
    public LevelDetailDto queryLevelDetail(Long levelBindId) {
        if (levelBindId == null) {
            return null;
        }
        LevelDetailQueryReq req = LevelDetailQueryReq.builder().levelTaskId(levelBindId).build();
        try {
            IRpcResult<LevelDetailDto> rpcResult = levelRpcService.queryLevelDetail(req);
            if (!rpcResult.checkSuccess()) {
                log.error("invoke levelRpcService.queryLevelDetail failed, message={}", rpcResult.getMessage());
                return null;
            }
            return rpcResult.getData();
        } catch (Exception e) {
            log.error("invoke levelRpcService.queryLevelDetail failed", e);
            return null;
        }
    }
}
