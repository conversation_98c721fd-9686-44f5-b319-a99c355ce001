package cn.jojo.front.jaguar.admin.model.req.activity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WishGiftReq {

    @Schema(description = "pageNum")
    @NotNull
    private Long pageNum = 1L;
    @Schema(description = "pageSize")
    @NotNull
    private Long pageSize = 10L;
    @Schema(description = "skuIds")
    private List<Long> skuIds;
    @Schema(description = "商品sku名称")
    private String skuName;
}
