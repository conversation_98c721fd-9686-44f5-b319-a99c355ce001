package cn.jojo.front.jaguar.adaptor.service;

import cn.jojo.edu.fantasy.rpc.api.dto.EduResourcesAggregationDto;
import cn.jojo.edu.fantasy.rpc.api.req.EduResourcesExtensionReq;
import cn.jojo.front.jaguar.common.pojo.bo.EduEbookResBo;
import cn.jojo.front.jaguar.common.pojo.bo.EduResourcesBo;
import cn.jojo.front.jaguar.common.pojo.bo.EduResourcesStoryBo;
import cn.jojo.front.jaguar.common.pojo.bo.ResourcesBo;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;

public interface IEduResourcesRpcClient {

    List<EduResourcesAggregationDto> queryAggregation(EduResourcesExtensionReq req);

    /**
     * 模糊查询album相关专辑数据信息
     *
     * @param albumId     专辑id
     * @param resId       资源id
     * @param resName     资源名称
     * @param contentType 内容类型
     * @param pageNum     頁碼
     * @param pageSize    页数据量
     * @return 信息列表
     */
    Page<ResourcesBo> getResByFuzzyQuery(String contentType, Long albumId, Long resId, String resName,
                                         Integer pageNum, Integer pageSize);

    /**
     * 查询故事聚合信息
     * @param resIds  资源id
     * @return 列表信息
     */
    List<EduResourcesStoryBo> queryStoryResources(List<Long> resIds);

    /**
     * 查询电子书聚合信息
     *
     * @param resIds  资源id
     * @return 列表信息
     */
    List<EduEbookResBo> queryEbooksResources(List<Long> resIds);

    /**
     * 查询resource列表
     *
     * @param resIds
     * @return
     */
    List<EduResourcesBo> listResources(List<Long> resIds);
}
