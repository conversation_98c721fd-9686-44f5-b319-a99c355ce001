package cn.jojo.front.jaguar.admin.model.convert.decorator;

import cn.hutool.core.util.ObjectUtil;
import cn.jojo.front.jaguar.admin.model.convert.TaskVo2DtoConvert;
import cn.jojo.front.jaguar.admin.model.dto.task.EventScopeDto;
import cn.jojo.front.jaguar.admin.model.dto.task.SubTaskDto;
import cn.jojo.front.jaguar.admin.model.dto.task.TaskDto;
import cn.jojo.front.jaguar.admin.model.dto.task.TaskExtendResourceDto;
import cn.jojo.front.jaguar.biz.service.pojo.bo.task.SubTaskVo;
import cn.jojo.front.jaguar.biz.service.pojo.bo.task.TaskVo;
import cn.jojo.front.jaguar.common.enums.task.TaskType;
import cn.jojo.front.jaguar.common.utils.CustomBeanUtils;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2024/03/18
 **/
public abstract class TaskVo2DtoConvertDecorator implements TaskVo2DtoConvert {

    private TaskVo2DtoConvert delegate;

    protected TaskVo2DtoConvertDecorator(
        TaskVo2DtoConvert delegate) {
        this.delegate = delegate;
    }

    @Override
    public TaskDto model1ToModel2(TaskVo model) {
        TaskDto taskDto = delegate.model1ToModel2(model);
        if(Objects.isNull(taskDto)){
            return taskDto;
        }
        List<SubTaskDto> subTaskDtos = taskDto.getSubTaskList();
        if(CollectionUtils.isEmpty(subTaskDtos)){
            return taskDto;
        }
        // 兼容老数据- 日常任务 课时id != null,统一放在dailyLessonIds
        if(taskDto.getTaskType().equals(TaskType.DAILY.getValue())
            && subTaskDtos.stream().anyMatch(v -> Objects.nonNull(v.getEventScope().getLessonId()))){
            List<String> dailyLessonIds =subTaskDtos.stream().map(v -> v.getEventScope().getLessonId()).collect(Collectors.toList());
            SubTaskDto subTaskDto = subTaskDtos.stream().findFirst().orElse(new SubTaskDto());
            subTaskDto.setEventScope(EventScopeDto.builder().dailyLessonIds(dailyLessonIds).build());
            taskDto.setSubTaskList(Lists.newArrayList(subTaskDto));
        }
        Map<Long, SubTaskVo> map =
            model.getSubTaskList().stream().collect(Collectors.toMap(SubTaskVo::getId, e -> e));

        taskDto.getSubTaskList().forEach(v->{
            EventScopeDto eventScope = v.getEventScope();
            eventScope.setTargetValue(v.getTargetValue());

            SubTaskVo subTaskVo = map.get(v.getId());
            if (ObjectUtil.isNotEmpty(subTaskVo) && ObjectUtil.isNotEmpty(subTaskVo.getEventScope())
                && ObjectUtil.isNotEmpty(subTaskVo.getEventScope().getLessonIds())) {

                List<String> lessonIds = subTaskVo.getEventScope()
                    .getLessonIds().stream().map(String::valueOf).collect(Collectors.toList());

                eventScope.setLessonIds(lessonIds);
            }

            TaskExtendResourceDto taskExtendResourceDto =
                CustomBeanUtils.copyProperties(subTaskVo.getTaskExtendResource(), TaskExtendResourceDto::new);
            v.setTaskExtendResource(taskExtendResourceDto);
        });

        return taskDto;
    }
}
