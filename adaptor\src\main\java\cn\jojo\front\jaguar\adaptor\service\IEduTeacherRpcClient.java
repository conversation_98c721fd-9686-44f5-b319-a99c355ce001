package cn.jojo.front.jaguar.adaptor.service;

import cn.jojo.edu.malacca.rpc.api.dto.EduTeacherDto;

import java.util.List;
import java.util.Map;

/**
 * 老师Rpc客户端
 *
 * <AUTHOR>
 * @date 2023/5/10 14:58
 */
public interface IEduTeacherRpcClient {

    /**
     * 根据员工号查询老师信息
     * @param employeeId 员工id
     * @return 老师信息
     */
    EduTeacherDto getTeacherInfoByEmployeeId(Long employeeId);

    /**
     * 批量查询老师信息
     * @param teacherIds 老师id
     * @return 老师信息
     */
    List<EduTeacherDto> listTeacherByTeacherIds(List<Long> teacherIds);

    /**
     * 是否添加老师
     *
     * @param userId    用户ID
     * @param teacherId 老师ID
     * @return boolean
     */
    boolean getHasAddTeacher(Long userId, Long teacherId);

    /**
     * 获取添加老师的数量
     * @param userId
     * @param teacherIds
     * @return
     */
    long getAddedTeacherCount(Long userId, List<Long> teacherIds);

    Map<Long, Boolean> getHasAddTeachers(Long userId, List<Long> teacherIds);
}
