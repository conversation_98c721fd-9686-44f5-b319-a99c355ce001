package cn.jojo.front.jaguar.adaptor.service.impl;

import cn.jojo.edu.fantasy.rpc.api.dto.EduShootResourceDto;
import cn.jojo.edu.fantasy.rpc.api.req.EduShootResourceQueryReq;
import cn.jojo.edu.fantasy.rpc.api.service.EduOpusShootRpcService;
import cn.jojo.front.jaguar.adaptor.service.IEduOpusShootRpcClient;
import cn.jojo.infra.sdk.api.metadata.IRpcResult;
import cn.jojo.infra.sdk.logging.JoJoLogging;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.Objects;


@Service
@Slf4j
public class EduOpusShootRpcClient implements IEduOpusShootRpcClient {

    @DubboReference
    private EduOpusShootRpcService eduOpusShootRpcService;


    @Override
    public EduShootResourceDto getShootResourceDetail(EduShootResourceQueryReq req) {
        if(Objects.isNull(req) || req.getSceneId() == null){
            return null;
        }
        try{
            IRpcResult<EduShootResourceDto> rpcResult =
                eduOpusShootRpcService.getShootResourceDetail(req);
            if (!rpcResult.checkSuccess()) {
                JoJoLogging.logger(log).error("eduOpusShootRpcService.getShootResourceDetail fail,req:{},message:{}", req,
                    rpcResult.getMessage());
                return null;
            }
            return rpcResult.getData();
        }catch (Exception e){
            JoJoLogging.logger(log).error("eduOpusShootRpcService.getShootResourceDetail error,req:{},message:{}", req,
                e.getMessage());
        }
        return null;
    }
}
