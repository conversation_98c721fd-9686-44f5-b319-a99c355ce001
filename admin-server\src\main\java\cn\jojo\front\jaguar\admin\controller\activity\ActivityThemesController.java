package cn.jojo.front.jaguar.admin.controller.activity;

import cn.jojo.edu.common.utils.exceptions.ParamValidException;
import cn.jojo.front.jaguar.admin.model.convert.ActivityThemeDetailDtoConvert;
import cn.jojo.front.jaguar.admin.model.convert.ActivityThemePageDtoConvert;
import cn.jojo.front.jaguar.admin.model.convert.ActivityThemeSaveReqConvert;
import cn.jojo.front.jaguar.admin.model.convert.ActivityThemeUpdateReqConvert;
import cn.jojo.front.jaguar.admin.model.dto.task.activity.ActivityThemeDetailDto;
import cn.jojo.front.jaguar.admin.model.dto.task.activity.ActivityThemePageDto;
import cn.jojo.front.jaguar.admin.model.req.activity.ActivityThemeComponentSaveReq;
import cn.jojo.front.jaguar.admin.model.req.activity.ActivityThemeComponentUpdateReq;
import cn.jojo.front.jaguar.admin.model.req.activity.ActivityThemePageQueryReq;
import cn.jojo.front.jaguar.admin.model.req.activity.ActivityThemePageSaveReq;
import cn.jojo.front.jaguar.admin.model.req.activity.ActivityThemePageUpdateReq;
import cn.jojo.front.jaguar.admin.model.req.activity.ActivityThemeSaveReq;
import cn.jojo.front.jaguar.admin.model.req.activity.ActivityThemeUpdateReq;
import cn.jojo.front.jaguar.biz.service.activitity.IActivityThemeBizService;
import cn.jojo.front.jaguar.common.enums.TableSortField;
import cn.jojo.front.jaguar.common.enums.TableSortType;
import cn.jojo.front.jaguar.common.enums.task.ActivityThemeComponentTypeEnum;
import cn.jojo.front.jaguar.common.enums.task.ActivityThemePageTypeEnum;
import cn.jojo.front.jaguar.common.enums.task.ActivityThemeSceneTypeEnum;
import cn.jojo.front.jaguar.common.pojo.vo.activity.ActivityThemeDetailVo;
import cn.jojo.front.jaguar.common.pojo.vo.activity.ActivityThemeOperateVo;
import cn.jojo.front.jaguar.common.pojo.vo.activity.ActivityThemePageVo;
import cn.jojo.front.jaguar.common.pojo.vo.activity.ActivityThemeQueryVo;
import cn.jojo.infra.sdk.api.metadata.DefaultHttpResult;
import cn.jojo.infra.sdk.api.metadata.DefaultPageResp;
import cn.jojo.infra.sdk.api.metadata.IHttpResult;
import cn.jojo.infra.sdk.api.metadata.IPageResp;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.common.collect.Lists;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import java.util.Objects;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 活动主题
 *
 * <AUTHOR>
 * @since 2025/4/24 15:38
 */

@Validated
@RestController
@RequestMapping("/admin/activity-themes")
@Tag(name = "激励活动主题")
public class ActivityThemesController {

    @Autowired
    private IActivityThemeBizService activityThemeBizService;

    @GetMapping
    @Operation(summary = "活动主题列表", description = "活动主题列表")
    public IPageResp<ActivityThemePageDto> page(ActivityThemePageQueryReq req) {
        ActivityThemeQueryVo queryVo = buildQueryVo(req);
        IPage<ActivityThemePageVo> themePage = activityThemeBizService.queryPage(queryVo);
        List<ActivityThemePageDto> pageDtos = ActivityThemePageDtoConvert.INSTANCE.model2sToModel1s(
            themePage.getRecords());
        return DefaultPageResp.buildPageResp(themePage.getCurrent(), themePage.getSize(), themePage.getTotal(),
            pageDtos);
    }

    private ActivityThemeQueryVo buildQueryVo(ActivityThemePageQueryReq req) {
        if (Objects.isNull(req)) {
            return ActivityThemeQueryVo.builder().pageNum(1L).pageSize(10L).build();
        }
        ActivityThemeQueryVo queryVo = ActivityThemeQueryVo.builder()
            .name(req.getName())
            .ids(Objects.nonNull(req.getId()) ? Lists.newArrayList(req.getId()) : null)
            .subjectTypes(Objects.nonNull(req.getSubjectType()) ? Lists.newArrayList(req.getSubjectType()) : null)
            .sortField(TableSortField.UPDATE_TIME)
            .sortType(TableSortType.DESC)
            .pageNum(Objects.isNull(req.getPageNum()) ? 1L : req.getPageNum())
            .pageSize(Objects.isNull(req.getPageSize()) ? 10L : req.getPageSize())
            .build();
        if (Objects.nonNull(req.getScene())) {
            ActivityThemeSceneTypeEnum sceneTypeEnum = ActivityThemeSceneTypeEnum.getByType(req.getScene());
            if (Objects.isNull(sceneTypeEnum)) {
                throw new ParamValidException("scene not exist");
            }
            queryVo.setScenes(Lists.newArrayList(sceneTypeEnum));
        }
        return queryVo;
    }

    @GetMapping("/{id}")
    @Operation(summary = "活动主题详情", description = "活动主题详情")
    public IHttpResult<ActivityThemeDetailDto> getDetail(@NotNull @PathVariable(value = "id") @Positive Long id) {
        ActivityThemeDetailVo activityThemeDetail = activityThemeBizService.getActivityThemeDetail(id);
        ActivityThemeDetailDto activityThemeDetailDto = ActivityThemeDetailDtoConvert.INSTANCE.model1ToModel2(
            activityThemeDetail);
        return DefaultHttpResult.successWithData(activityThemeDetailDto);
    }

    @PostMapping
    @Operation(summary = "新增活动主题", description = "新增活动主题")
    public IHttpResult<Void> save(@Validated @RequestBody ActivityThemeSaveReq req) {
        if (Objects.isNull(req) || req.getScene() == null) {
            throw new ParamValidException("scene is null");
        }
        ActivityThemeSceneTypeEnum sceneTypeEnum = ActivityThemeSceneTypeEnum.getByType(req.getScene());
        if (Objects.isNull(sceneTypeEnum)) {
            throw new ParamValidException("scene is error");
        }
        for (ActivityThemePageSaveReq page : req.getPages()) {
            if (Objects.isNull(page)) {
                continue;
            }
            if (Objects.isNull(page.getPageType())) {
                throw new ParamValidException("pageType is null");
            }
            ActivityThemePageTypeEnum pageTypeEnum = ActivityThemePageTypeEnum.getByType(page.getPageType());
            if (Objects.isNull(pageTypeEnum)) {
                throw new ParamValidException("pageType is error");
            }
            for (ActivityThemeComponentSaveReq component : page.getComponents()) {
                if (Objects.isNull(component)) {
                    continue;
                }
                if (Objects.isNull(component.getComponentType())) {
                    throw new ParamValidException("componentType is null");
                }
                ActivityThemeComponentTypeEnum componentTypeEnum = ActivityThemeComponentTypeEnum.getByType(
                    component.getComponentType());
                if (Objects.isNull(componentTypeEnum)) {
                    throw new ParamValidException("componentType is error");
                }
            }

        }
        ActivityThemeOperateVo activityThemeOperateVo = ActivityThemeSaveReqConvert.INSTANCE.model1ToModel2(req);
        activityThemeBizService.saveActivityTheme(activityThemeOperateVo);
        return DefaultHttpResult.successWithoutData();
    }

    @PatchMapping("/{id}")
    @Operation(summary = "编辑活动主题", description = "修改活动主题信息")
    public IHttpResult<Void> update(@NotNull @PathVariable(value = "id") @Positive Long id,
        @Validated @RequestBody ActivityThemeUpdateReq req) {
        if (Objects.isNull(req) || req.getScene() == null) {
            throw new ParamValidException("scene is null");
        }
        ActivityThemeSceneTypeEnum sceneTypeEnum = ActivityThemeSceneTypeEnum.getByType(req.getScene());
        if (Objects.isNull(sceneTypeEnum)) {
            throw new ParamValidException("scene is error");
        }
        for (ActivityThemePageUpdateReq page : req.getPages()) {
            if (Objects.isNull(page)) {
                continue;
            }

            if (Objects.isNull(page.getPageType())) {
                throw new ParamValidException("pageType is null");
            }
            ActivityThemePageTypeEnum pageTypeEnum = ActivityThemePageTypeEnum.getByType(page.getPageType());
            if (Objects.isNull(pageTypeEnum)) {
                throw new ParamValidException("pageType is error");
            }
            for (ActivityThemeComponentUpdateReq component : page.getComponents()) {
                if (Objects.isNull(component)) {
                    continue;
                }
                if (Objects.isNull(component.getComponentType())) {
                    throw new ParamValidException("componentType is null");
                }
                ActivityThemeComponentTypeEnum componentTypeEnum = ActivityThemeComponentTypeEnum.getByType(
                    component.getComponentType());
                if (Objects.isNull(componentTypeEnum)) {
                    throw new ParamValidException("componentType is error");
                }
            }

        }
        ActivityThemeOperateVo activityThemeOperateVo = ActivityThemeUpdateReqConvert.INSTANCE.model1ToModel2(req);
        activityThemeOperateVo.setId(id);
        activityThemeBizService.updateActivityTheme(activityThemeOperateVo);
        return DefaultHttpResult.successWithoutData();
    }

}
