package cn.jojo.front.jaguar.adaptor.service.impl;

import cn.jojo.front.jaguar.common.exception.BusinessException;
import cn.jojo.infra.sdk.api.metadata.IRpcResult;
import cn.jojo.mall.boom.rpc.api.dto.ActivityDto;
import cn.jojo.mall.boom.rpc.api.req.ActivityReq;
import cn.jojo.mall.boom.rpc.api.service.ActivityRpcService;
import cn.tinman.sharedservices.mall.product.api.service.ISkuApiService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * Unit test for the MallActivityRpcClientImpl class.
 */
@ExtendWith(MockitoExtension.class)
public class MallActivityRpcClientImplTest {
    @InjectMocks
    private MallActivityRpcClientImpl mallActivityRpcClient;

    @Mock
    private ISkuApiService skuApiService;

    @Mock
    private ActivityRpcService activityRpcService;

    /**
     * Test case for listActivity method when activityIds is empty.
     */
    @Test
    public void testListActivityWhenActivityIdsIsEmpty() {
        List<Long> activityIds = Collections.emptyList();
        List<ActivityDto> result = mallActivityRpcClient.listActivity(activityIds);
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * Test case for listActivity method when activityIds is not empty and returns data successfully.
     */
    @Test
    public void testListActivityWhenSuccessful() {
        List<Long> activityIds = Arrays.asList(1L, 2L, 3L);
        
        IRpcResult<List<ActivityDto>> mockResult = mock(IRpcResult.class);
        when(mockResult.checkSuccess()).thenReturn(true);
        when(mockResult.getData()).thenReturn(Arrays.asList(new ActivityDto(), new ActivityDto()));
        
        when(activityRpcService.listActivity(any(ActivityReq.class))).thenReturn(mockResult);
        
        List<ActivityDto> result = mallActivityRpcClient.listActivity(activityIds);
        assertNotNull(result);
        assertEquals(2, result.size());
        
        verify(activityRpcService, times(1)).listActivity(any(ActivityReq.class));
    }

    /**
     * Test case for listActivity method when the RPC call fails.
     */
    @Test
    public void testListActivityWhenRpcCallFails() {
        List<Long> activityIds = Arrays.asList(1L, 2L, 3L);
        
        IRpcResult<List<ActivityDto>> mockResult = mock(IRpcResult.class);
        when(mockResult.checkSuccess()).thenReturn(false);
        when(mockResult.getMessage()).thenReturn("RPC call failed");
        
        when(activityRpcService.listActivity(any(ActivityReq.class))).thenReturn(mockResult);
        
        assertThrows(BusinessException.class, () -> {
            mallActivityRpcClient.listActivity(activityIds);
        });
        
        verify(activityRpcService, times(1)).listActivity(any(ActivityReq.class));
    }

    /**
     * Test case for listActivity method when an exception occurs during the RPC call.
     */
    @Test
    public void testListActivityWhenExceptionOccurs() {
        List<Long> activityIds = Arrays.asList(1L, 2L, 3L);
        
        when(activityRpcService.listActivity(any(ActivityReq.class))).thenThrow(new RuntimeException("Network error"));
        
        assertThrows(BusinessException.class, () -> {
            mallActivityRpcClient.listActivity(activityIds);
        });
        
        verify(activityRpcService, times(1)).listActivity(any(ActivityReq.class));
    }
}