package cn.jojo.front.jaguar.adaptor.service;

import cn.jojo.crm.request.GroupReq;
import cn.jojo.crm.response.group.GroupResp;
import cn.jojo.front.jaguar.common.pojo.bo.UserGroupEsBo;
import cn.jojo.front.jaguar.common.pojo.vo.UserGroupListVo;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface UserGroupService {

    /**
     * 获取用户分群列表
     *
     * @return 分群列表
     */
    List<GroupResp.Group> getUserClusterList();

    /**
     * 获取es分页结果
     *
     * @param req 请求参数
     * @return 包含游标的结果集
     */
    UserGroupEsBo getUserGroupEsResult(GroupReq req);

    /**
     * 获取用户分群列表
     *
     * @return 用户分群列表
     */
    List<UserGroupListVo> listUserGroups();
}
