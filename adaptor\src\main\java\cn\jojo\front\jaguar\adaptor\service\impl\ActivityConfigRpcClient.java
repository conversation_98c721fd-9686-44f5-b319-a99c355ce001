package cn.jojo.front.jaguar.adaptor.service.impl;

import cn.jojo.edu.wings.rpc.api.dto.activity.ActivityBasicInfoDto;
import cn.jojo.edu.wings.rpc.api.dto.activity.ActivityCustomConfigNewDto;
import cn.jojo.edu.wings.rpc.api.req.activity.ActivityBasicInfoListQueryReq;
import cn.jojo.edu.wings.rpc.api.req.activity.ActivityCustomConfigQueryReq;
import cn.jojo.edu.wings.rpc.api.service.IActivityConfigRpcService;
import cn.jojo.front.jaguar.adaptor.service.IActivityConfigRpcClient;
import cn.jojo.infra.sdk.api.metadata.IRpcResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class ActivityConfigRpcClient implements IActivityConfigRpcClient {

    @DubboReference
    private IActivityConfigRpcService activityConfigRpcService;

    @Override
    public List<ActivityBasicInfoDto> getActivityList(List<Long> activityIds, List<Long> classTagActivityIds,
            boolean assembleDetail) {
        if (CollectionUtils.isEmpty(activityIds) && CollectionUtils.isEmpty(classTagActivityIds)) {
            return Collections.emptyList();
        }
        ActivityBasicInfoListQueryReq req = new ActivityBasicInfoListQueryReq();
        req.setActivityIds(activityIds);
        req.setAssembleDetail(assembleDetail);
        req.setClassTagActivityIds(classTagActivityIds);
        try {
            IRpcResult<List<ActivityBasicInfoDto>> rpcResult = activityConfigRpcService.listActivityBasicInfoDtos(req);
            if (!rpcResult.checkSuccess()) {
                log.error("invoke activityConfigRpcService.listActivityBasicInfoDtos failed, message={}",
                        rpcResult.getMessage());
                return Collections.emptyList();
            }
            return Optional.ofNullable(rpcResult.getData()).orElse(Collections.emptyList());
        } catch (Exception e) {
            log.error("invoke activityConfigRpcService.listActivityBasicInfoDtos failed", e);
        }
        return Collections.emptyList();
    }

    @Override
    public ActivityCustomConfigNewDto queryActivityCustomConfig(Long activityId, Long classTagActivityId,
                                                                List<String> types) {
        if (activityId == null) {
            return null;
        }
        ActivityCustomConfigQueryReq req = ActivityCustomConfigQueryReq.builder()
                .classTagActivityId(classTagActivityId)
                .activityId(activityId)
                .customConfigTypes(types)
                .build();
        try {
            IRpcResult<ActivityCustomConfigNewDto> rpcResult = activityConfigRpcService.queryActivityCustomConfig(req);
            if (!rpcResult.checkSuccess()) {
                log.error("invoke activityConfigRpcService.queryActivityCustomConfig failed, message={}",
                        rpcResult.getMessage());
                return null;
            }
            return rpcResult.getData();
        } catch (Exception e) {
            log.error("invoke activityConfigRpcService.queryActivityCustomConfig failed", e);
            return null;
        }
    }
}
