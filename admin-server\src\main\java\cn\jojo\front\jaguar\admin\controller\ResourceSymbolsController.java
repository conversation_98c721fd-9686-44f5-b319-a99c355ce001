package cn.jojo.front.jaguar.admin.controller;

import cn.jojo.front.jaguar.common.pojo.req.resource.ResourceKeyListReq;
import cn.jojo.front.jaguar.common.pojo.req.resource.ResourceKeySaveReq;
import cn.jojo.front.jaguar.common.pojo.vo.resource.ResourceKeyVo;
import cn.jojo.front.jaguar.core.service.resource.IResourceSymbolsService;
import cn.jojo.infra.sdk.api.metadata.DefaultHttpResult;
import cn.jojo.infra.sdk.api.metadata.DefaultPageResp;
import cn.jojo.infra.sdk.api.metadata.IHttpResult;
import cn.jojo.infra.sdk.api.metadata.IPageResp;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2024/6/4 14:12
 */
@Validated
@RestController
@RequestMapping("/admin/resource-symbols")
public class ResourceSymbolsController {

    @Resource
    private IResourceSymbolsService resourceSymbolsService;

    @GetMapping
    @Operation(summary = "查询资源标识列表", description = "分页查询资源标识列表(资源标识用于关联资源,将不同规格的资源在逻辑上关联成相同素材资源)")
    public IPageResp<ResourceKeyVo> page(ResourceKeyListReq req) {
        IPage<ResourceKeyVo> page = resourceSymbolsService.pageResourceSymbol(req);
        return DefaultPageResp.buildPageResp(req, page.getTotal(), page.getRecords());
    }

    @PostMapping
    @Operation(summary = "新增资源标识", description = "新增资源标识")
    public IHttpResult<Boolean> save(@RequestBody @Valid ResourceKeySaveReq req) {
        req.setId(null);
        resourceSymbolsService.saveOrUpdateResourceSymbol(req);
        return DefaultHttpResult.successWithData(true);
    }

    @PutMapping("/{id}")
    @Operation(summary = "更新资源标识", description = "更新资源标识")
    public IHttpResult<Boolean> update(
        @PathVariable @NotNull(message = "id can not be null") @Parameter(description = "资源标识id") Integer id,
        @RequestBody @Valid ResourceKeySaveReq req) {
        req.setId(id);
        resourceSymbolsService.saveOrUpdateResourceSymbol(req);
        return DefaultHttpResult.successWithData(true);
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "删除资源标识", description = "删除资源标识")
    public IHttpResult<Boolean> delete(
        @PathVariable @NotNull(message = "id can not be null") @Parameter(description = "资源标识id") Integer id) {
        resourceSymbolsService.deleteResourceSymbol(id);
        return DefaultHttpResult.successWithData(true);
    }
}
