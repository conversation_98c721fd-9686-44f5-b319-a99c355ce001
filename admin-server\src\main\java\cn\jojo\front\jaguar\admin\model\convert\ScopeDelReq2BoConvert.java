package cn.jojo.front.jaguar.admin.model.convert;

import cn.jojo.edu.common.utils.convert.BaseModelConvert;
import cn.jojo.front.jaguar.admin.model.req.activity.ScopeDelReq;
import cn.jojo.front.jaguar.common.pojo.bo.activity.ActivityScopeBo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 下拉选查询req 转vo
 *
 * <AUTHOR>
 * @date 2024/03/16
 */
@Mapper
public interface ScopeDelReq2BoConvert extends BaseModelConvert<ScopeDelReq, ActivityScopeBo> {

    ScopeDelReq2BoConvert INSTANCE = Mappers.getMapper(ScopeDelReq2BoConvert.class);
}
