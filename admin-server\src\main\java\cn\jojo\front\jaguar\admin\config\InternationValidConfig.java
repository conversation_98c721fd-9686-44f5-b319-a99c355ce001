package cn.jojo.front.jaguar.admin.config;

import cn.jojo.front.jaguar.admin.validation.CustomLocalValidatorFactoryBean;
import cn.jojo.infra.sdk.g11n.i18n.MixedMessageSource;
import org.hibernate.validator.BaseHibernateValidatorConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class InternationValidConfig {

    @Bean
    public CustomLocalValidatorFactoryBean getValidator(MixedMessageSource mixedMessageSource) {
        CustomLocalValidatorFactoryBean validator = new CustomLocalValidatorFactoryBean();
        validator.setValidationMessageSource(mixedMessageSource);
        validator.getValidationPropertyMap().put(BaseHibernateValidatorConfiguration.FAIL_FAST, "true");

        return validator;
    }
}
