package cn.jojo.front.jaguar.admin.model.req.activity;

import cn.jojo.front.jaguar.common.enums.task.ActivityRuleEndTimeTypeEnum;
import cn.jojo.front.jaguar.common.enums.task.ActivityRuleStartTimeTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ActivityRuleCustomActivityTimeReq {

    @Schema(description = "自定义开始时间节点")
    private CustomStartActivityTimeReq customStartActivityTime;
    @Schema(description = "自定义结束时间节点")
    private CustomEndActivityTimeReq customEndActivityTime;

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class CustomStartActivityTimeReq {

        @Schema(description = "自定义开始时间类型", implementation = ActivityRuleStartTimeTypeEnum.class)
        private Integer startActivityTimeType;

        @Schema(description = "活动时间课时序号索引, 课程下课时序号")
        private Integer activityTimeIndex;

        @Schema(description = "活动时间解锁后天数")
        private Integer activityTimeDays;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class CustomEndActivityTimeReq {

        @Schema(description = "自定义结束时间类型", implementation = ActivityRuleEndTimeTypeEnum.class)
        private Integer endActivityTimeType;

        @Schema(description = "活动时间课时序号索引, 课程下课时序号")
        private Integer activityTimeIndex;

        @Schema(description = "活动时间解锁后天数")
        private Integer activityTimeDays;
    }
}
