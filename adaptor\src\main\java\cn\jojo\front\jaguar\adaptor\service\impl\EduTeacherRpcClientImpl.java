package cn.jojo.front.jaguar.adaptor.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.jojo.edu.malacca.rpc.api.dto.EduRUserTeacherDto;
import cn.jojo.edu.malacca.rpc.api.dto.EduTeacherDto;
import cn.jojo.edu.malacca.rpc.api.dto.enums.EduUserFriendStatusEnum;
import cn.jojo.edu.malacca.rpc.api.req.EduRUserTeacherQueryReq;
import cn.jojo.edu.malacca.rpc.api.req.EduTeacherQueryReq;
import cn.jojo.edu.malacca.rpc.api.service.EduTeacherRpcService;
import cn.jojo.front.jaguar.adaptor.service.IEduTeacherRpcClient;
import cn.jojo.infra.sdk.api.metadata.IRpcResult;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 老师rpc
 *
 * <AUTHOR>
 * @date 2023/5/10 18:28
 */
@Slf4j
@Component
public class EduTeacherRpcClientImpl implements IEduTeacherRpcClient {

    @DubboReference
    private EduTeacherRpcService eduTeacherRpcService;

    @Override
    public EduTeacherDto getTeacherInfoByEmployeeId(Long employeeId) {
        try{
            if (employeeId == null || employeeId <= 0) {
                return null;
            }
            EduTeacherQueryReq req = new EduTeacherQueryReq();
            req.setEmployeeIds(Lists.newArrayList(employeeId));
            IRpcResult<List<EduTeacherDto>> rpcResult = eduTeacherRpcService.queryTeachers(req);
            if (!rpcResult.checkSuccess()) {
                log.error("failed query eduTeacherRpcService.queryTeachers, message = {}", rpcResult.getMessage());
                return null;
            }

            return CollUtil.get(rpcResult.getData(), 0);
        }catch (Exception e){
            log.error("query eduTeacherRpcService.queryTeachers error{}",e.getMessage());
        }
        return null;
    }

    @Override
    public List<EduTeacherDto> listTeacherByTeacherIds(List<Long> teacherIds) {
        if (CollectionUtils.isEmpty(teacherIds)) {
            return Collections.emptyList();
        }
        try{
            EduTeacherQueryReq req = new EduTeacherQueryReq();
            req.setTeacherId(teacherIds);
            IRpcResult<List<EduTeacherDto>> rpcResult = eduTeacherRpcService.queryTeachers(req);
            if (!rpcResult.checkSuccess()) {
                log.error("failed query eduTeacherRpcService.queryTeachers, message = {}", rpcResult.getMessage());
                return Collections.emptyList();
            }
            return ListUtils.emptyIfNull(rpcResult.getData());
        }catch (Exception e){
            log.error("query eduTeacherRpcService.queryTeachers error{}",e.getMessage());
        }
        return Collections.emptyList();
    }

    @Override
    public boolean getHasAddTeacher(Long userId, Long teacherId) {
        if (teacherId == null) {
            return false;
        }
        EduRUserTeacherQueryReq req = EduRUserTeacherQueryReq.builder()
            .userIds(Lists.newArrayList(userId))
            .teacherIds(Lists.newArrayList(teacherId)).build();
        IRpcResult<List<EduRUserTeacherDto>> rpcResult = eduTeacherRpcService.queryUserTeacherRelationStatus(req);
        if (!rpcResult.checkSuccess()) {
            return false;
        }
        List<EduRUserTeacherDto> resultList = rpcResult.getData();
        if (CollUtil.isEmpty(resultList)) {
            return false;
        }
        EduRUserTeacherDto rUserTeacherDto = resultList.get(0);
        if (rUserTeacherDto == null) {
            return false;
        }
        return rUserTeacherDto.getRelationshipStatus().equals(EduUserFriendStatusEnum.WE_ARE_FRIEND.getStatus());
    }

    @Override
    public long getAddedTeacherCount(Long userId, List<Long> teacherIds) {
        if (CollectionUtils.isEmpty(teacherIds)) {
            return 0;
        }
        EduRUserTeacherQueryReq req = EduRUserTeacherQueryReq.builder()
            .userIds(Lists.newArrayList(userId))
            .teacherIds(teacherIds).build();
        IRpcResult<List<EduRUserTeacherDto>> rpcResult = eduTeacherRpcService.queryUserTeacherRelationStatus(req);
        if (!rpcResult.checkSuccess()) {
            return 0;
        }
        List<EduRUserTeacherDto> resultList = rpcResult.getData();
        if (CollUtil.isEmpty(resultList)) {
            return 0;
        }

        return resultList.stream().map(EduRUserTeacherDto::getRelationshipStatus)
                .filter(status -> status.equals(EduUserFriendStatusEnum.WE_ARE_FRIEND.getStatus())).count();
    }

    @Override
    public Map<Long, Boolean> getHasAddTeachers(Long userId, List<Long> teacherIds) {
        if (CollectionUtils.isEmpty(teacherIds) || userId == null) {
            return Collections.emptyMap();
        }
        EduRUserTeacherQueryReq req = EduRUserTeacherQueryReq.builder()
            .userIds(Lists.newArrayList(userId))
            .teacherIds(teacherIds).build();
        try {
            IRpcResult<List<EduRUserTeacherDto>> rpcResult = eduTeacherRpcService.queryUserTeacherRelationStatus(req);
            if (!rpcResult.checkSuccess()) {
                return Collections.emptyMap();
            }
            Map<Long, Boolean> resultMap = Optional.ofNullable(rpcResult.getData()).orElse(Collections.emptyList())
                .stream()
                .collect(Collectors.toMap(EduRUserTeacherDto::getTeacherId,
                    dto -> EduUserFriendStatusEnum.WE_ARE_FRIEND.getStatus().equals(dto.getRelationshipStatus()),
                    (v1, v2) -> v1));
            return teacherIds.stream()
                .collect(Collectors.toMap(i -> i, i -> resultMap.getOrDefault(i, false), (v1, v2) -> v1));
        } catch (Exception e) {
            log.error("eduTeacherRpcService.queryUserTeacherRelationStatus error{}",e.getMessage());
        }
        return Collections.emptyMap();
    }
}
