package cn.jojo.front.jaguar.admin.interceptor;

import cn.jojo.edu.common.utils.constants.OperateType;
import cn.jojo.edu.common.utils.context.OperateContext;
import cn.jojo.front.jaguar.adaptor.service.IOAEmployRpcClient;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;


@Component
@Slf4j
public class OperateInfoInterceptor implements HandlerInterceptor {

    @Autowired
    private IOAEmployRpcClient oaEmployQueryService;


    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
        throws Exception {

        Long currentEmployeeId = oaEmployQueryService.currentEmployeeId();
        if (currentEmployeeId != null) {
            OperateContext.setOperatorId(currentEmployeeId);
            OperateContext.setOperateType(OperateType.ADMIN);
        }
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest httpServletRequest,
        HttpServletResponse httpServletResponse,
        Object handler,
        Exception e) throws Exception {
        OperateContext.clean();
    }
}
