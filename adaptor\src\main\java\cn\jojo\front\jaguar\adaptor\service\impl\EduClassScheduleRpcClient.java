package cn.jojo.front.jaguar.adaptor.service.impl;


import cn.jojo.edu.malacca.rpc.api.dto.EduClassScheduleItemDto;
import cn.jojo.edu.malacca.rpc.api.req.EduClassScheduleItemQueryReq;
import cn.jojo.edu.malacca.rpc.api.req.EduUserClassScheduleItemQueryReq;
import cn.jojo.edu.malacca.rpc.api.service.EduClassScheduleRpcService;
import cn.jojo.front.jaguar.adaptor.service.IEduClassScheduleRpcClient;
import cn.jojo.front.jaguar.common.pojo.req.EduUserClassScheduleItemQueryRpcReq;
import cn.jojo.front.jaguar.common.pojo.vo.EduClassScheduleItemVo;
import cn.jojo.infra.sdk.api.metadata.IRpcResult;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Component
@Slf4j
public class EduClassScheduleRpcClient implements IEduClassScheduleRpcClient {

    @Reference
    private EduClassScheduleRpcService eduClassScheduleRpcService;


    @Override
    public List<EduClassScheduleItemVo> queryUserClassScheduleItemsByUserId(Long userId, Long classId,
                                                                            List<Long> lessonIds) {
        try {
            EduUserClassScheduleItemQueryReq req = EduUserClassScheduleItemQueryReq.builder()
                .userId(userId)
                .build();
            if (classId != null) {
                req.setClassId(classId);
            }
            if (!CollectionUtils.isEmpty(lessonIds)) {
                req.setLessonIds(lessonIds);
            }
            IRpcResult<List<EduClassScheduleItemDto>> rpcResult =
                eduClassScheduleRpcService.queryUserClassScheduleItems(req);
            if (!rpcResult.checkSuccess()) {
                log.error("query eduClassScheduleRpcService.queryUserClassScheduleItems fail,userId:{},lessonIds:{}",
                    userId,
                    JSON.toJSON(lessonIds));
                return null;
            }
            return rpcResult.getData().stream().map(data -> {
                EduClassScheduleItemVo vo = new EduClassScheduleItemVo();
                BeanUtils.copyProperties(data, vo);
                return vo;
            }).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("query eduClassScheduleRpcService.queryUserClassScheduleItems fail,error:{},lessonIds:{}", e,
                JSON.toJSON(lessonIds));
        }
        return null;
    }

    @Override
    public List<EduClassScheduleItemVo> queryUserClassScheduleItems(EduUserClassScheduleItemQueryRpcReq rpcReq) {
        try {
            EduUserClassScheduleItemQueryReq req = new EduUserClassScheduleItemQueryReq();
            BeanUtils.copyProperties(rpcReq, req);
            IRpcResult<List<EduClassScheduleItemDto>> rpcResult =
                eduClassScheduleRpcService.queryUserClassScheduleItems(req);
            if (!rpcResult.checkSuccess()) {
                log.error("query eduClassScheduleRpcService.queryUserClassScheduleItems fail,req:{} ", req);
                return Collections.emptyList();
            }
            return rpcResult.getData().stream().map(data -> {
                EduClassScheduleItemVo vo = new EduClassScheduleItemVo();
                BeanUtils.copyProperties(data, vo);
                return vo;
            }).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("query eduClassScheduleRpcService.queryUserClassScheduleItems fail,req:{}", rpcReq, e);
        }
        return Collections.emptyList();
    }
}
