package cn.jojo.front.jaguar.adaptor.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import cn.jojo.cc.api.domain.request.CourseGetByIdReq;
import cn.jojo.cc.api.domain.request.CourseInfoReq;
import cn.jojo.cc.api.domain.request.CourseListByIdsReq;
import cn.jojo.cc.api.domain.request.CourseListByKeysReq;
import cn.jojo.cc.api.domain.request.CourseSearchReq;
import cn.jojo.cc.api.service.CourseService;
import cn.jojo.cc.common.dto.CourseDto;
import cn.jojo.cc.common.dto.CourseInfoDto;
import cn.jojo.cc.common.dto.course.CourseGseExaminationContentDto;
import cn.jojo.cc.common.dto.course.CourseGseExaminationDto;
import cn.jojo.front.jaguar.adaptor.service.ICourseRpcClient;
import cn.jojo.front.jaguar.common.pojo.req.CourseRpcReq;
import cn.jojo.front.jaguar.common.pojo.vo.SimpleCourseVo;
import cn.jojo.front.jaguar.common.utils.CustomBeanUtils;
import cn.jojo.infra.sdk.api.metadata.IRpcResult;
import cn.jojo.infra.sdk.logging.JoJoLogging;
import cn.jojo.uc.common.exception.BusinessServiceException;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Component
@Slf4j
public class CourseRpcClient implements ICourseRpcClient {

    @DubboReference
    private CourseService courseService;

    @Value("${course.list.batch.maxSize:100}")
    private Integer batchListCourseMaxSize;

    @Override
    public SimpleCourseVo getByCourseId(Long courseId) {
        try {
            CourseGetByIdReq req = new CourseGetByIdReq();
            req.setCourseId(courseId);
            IRpcResult<CourseDto> rpcResult = courseService.getByCourseId(req);
            if (!rpcResult.checkSuccess()) {
                log.error("invoke courseService.getByCourseId fail:{}", rpcResult.getMessage());
                return null;
            }
            CourseDto courseDto = rpcResult.getData();
            if (courseDto == null) {
                return SimpleCourseVo.builder().id(courseId).build();
            }
            return SimpleCourseVo.builder()
                .id(courseDto.getId())
                .courseKey(courseDto.getCourseKey())
                .courseName(courseDto.getCourseName())
                .courseType(courseDto.getCourseType())
                .subjectType(courseDto.getSubjectType())
                .courseL2Type(courseDto.getCourseL2Type())
                .courseSegment(courseDto.getCourseSegment())
                    .courseSegmentCode(
                            Optional.ofNullable(courseDto.getCourseSegmentCode()).map(String::valueOf).orElse(""))
                .build();
        } catch (Exception e) {
            log.error("invoke courseService.getByCourseId error" + e);
            return null;
        }
    }

    @Override
    public List<SimpleCourseVo> listByCourseIds(List<Long> courseIds) {
        if (CollectionUtils.isEmpty(courseIds)) {
            return Collections.emptyList();
        }

        return Lists.partition(courseIds, batchListCourseMaxSize).stream()
            .map(ids -> {
                CourseListByIdsReq req = new CourseListByIdsReq();
                req.setCourseIds(ids);
                try {
                    IRpcResult<List<CourseDto>> rpcResult = courseService.listByCourseIds(req);
                    if (!rpcResult.checkSuccess()) {
                        log.error("CourseRpcClient#listByCourseIds failed");
                        return Collections.<SimpleCourseVo>emptyList();
                    }
                    List<CourseDto> courseDtoList = rpcResult.getData();
                    return courseDtoList.stream().map(this::assembleSimpleCourseVo).collect(Collectors.toList());
                } catch (Exception e) {
                    log.error("CourseRpcClient#listByCourseIds failed", e);
                    return Collections.<SimpleCourseVo>emptyList();
                }
            }).flatMap(List::stream).collect(Collectors.toList());
    }

    @Override
    public List<SimpleCourseVo> listByCourseKeys(List<String> courseKeys) {
        if (CollectionUtils.isEmpty(courseKeys)) {
            return Collections.emptyList();
        }

        return Lists.partition(courseKeys, batchListCourseMaxSize).stream()
            .map(keys -> {
                CourseListByKeysReq req = new CourseListByKeysReq();
                req.setCourseKeys(keys);
                try {
                    IRpcResult<List<CourseDto>> rpcResult = courseService.listByCourseKeys(req);
                    if (!rpcResult.checkSuccess()) {
                        log.error("CourseRpcClient#listByCourseKeys failed");
                        return Collections.<SimpleCourseVo>emptyList();
                    }
                    List<CourseDto> courseDtoList = rpcResult.getData();
                    return courseDtoList.stream().map(this::assembleSimpleCourseVo).collect(Collectors.toList());
                } catch (Exception e) {
                    log.error("CourseRpcClient#listByCourseKeys failed", e);
                    return Collections.<SimpleCourseVo>emptyList();
                }
            }).flatMap(List::stream).collect(Collectors.toList());
    }


    @Override
    public List<SimpleCourseVo> listAllCourse() {
        IRpcResult<List<CourseInfoDto>> rpcResult;
        try {
            CourseInfoReq listReq = new CourseInfoReq();
            rpcResult = courseService.listCourseInfo(listReq);
            if (!rpcResult.checkSuccess()) {
                log.error("invoke courseService.listAllCourse failed:{}", rpcResult.getMessage());
                return Collections.emptyList();
            }
            return rpcResult.getData().stream().map(this::assembleSimpleCourseVoForCourseInfoDto)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("invoke courseService.listAllCourse failed", e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<SimpleCourseVo> searchCourse(CourseRpcReq courseRpcReq) {

        IRpcResult<List<CourseDto>> rpcResult;
        try {
            CourseSearchReq searchReq = CustomBeanUtils.copyProperties(courseRpcReq, CourseSearchReq::new);
            rpcResult = courseService.search(searchReq);

            if (!rpcResult.checkSuccess() || rpcResult.getData() == null) {
                log.error("invoke courseService.listAllCourse failed , {}", rpcResult.getMessage());
                throw new BusinessServiceException("CourseRpcClient.searchCourse failed" );
            }
            return rpcResult.getData().stream()
                .map(e -> CustomBeanUtils.copyProperties(e, SimpleCourseVo::new))
                .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("invoke courseService.listAllCourse failed", e);
            throw new BusinessServiceException("CourseRpcClient.searchCourse failed");
        }
    }

    private SimpleCourseVo assembleSimpleCourseVo(CourseDto courseDto) {
        return SimpleCourseVo.builder()
            .id(courseDto.getId())
            .courseKey(courseDto.getCourseKey())
            .courseName(courseDto.getCourseName())
            .courseType(courseDto.getCourseType())
            .subjectType(courseDto.getSubjectType())
            .courseSegment(courseDto.getCourseSegment())
            .courseSegmentCode(
                Optional.ofNullable(courseDto.getCourseSegmentCode()).map(String::valueOf).orElse(""))
            .courseL2Type(courseDto.getCourseL2Type())
            .entranceImage(
                null == courseDto.getEntranceImageResource() ? "" : courseDto.getEntranceImageResource().getUri())
            .build();
    }
    private SimpleCourseVo assembleSimpleCourseVoForCourseInfoDto(CourseInfoDto courseDto) {
        return SimpleCourseVo.builder()
                .id(courseDto.getId())
                .courseKey(courseDto.getCourseKey())
                .courseName(courseDto.getCourseName())
                .courseType(courseDto.getCourseType())
                .subjectType(courseDto.getSubjectType())
                .courseSegment(courseDto.getCourseSegment())
                .courseSegmentCode(
                        Optional.ofNullable(courseDto.getCourseSegmentCode()).map(String::valueOf).orElse(""))
                .build();
    }


    public List<CourseGseExaminationContentDto> getCourseGseExaminationByCourseId(Long courseId) {
        CourseGetByIdReq req = new CourseGetByIdReq();
        if (courseId == null || courseId == 0) {
            return Collections.emptyList();
        }
        req.setCourseId(courseId);
        try {
            IRpcResult<CourseGseExaminationDto> rpcResult = courseService.getCourseGseExaminationByCourseId(req);
            JoJoLogging.logger(log)
                .info("courseService.getCourseGseExaminationByCourseId req:{},rpcResult:{}", JSONUtil.toJsonStr(req), JSONUtil.toJsonStr(rpcResult));
            if (rpcResult == null || !rpcResult.checkSuccess() || rpcResult.getData() == null) {
                log.error("courseService.getCourseGseExaminationByCourseId, req:{}, rpcResult:{}",
                    JSONUtil.toJsonStr(req), JSON.toJSONString(rpcResult));
                return Collections.emptyList();
            }
            return CollUtil.isEmpty(rpcResult.getData().getGseExamContents()) ? Collections.emptyList() :
                rpcResult.getData().getGseExamContents();
        } catch (Exception e) {
            log.error("courseService.getCourseGseExaminationByCourseId error, req:{}", JSONUtil.toJsonStr(req), e);
        }
        return Collections.emptyList();
    }
}
