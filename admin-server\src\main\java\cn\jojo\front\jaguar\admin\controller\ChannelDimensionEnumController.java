package cn.jojo.front.jaguar.admin.controller;

import cn.jojo.front.jaguar.common.enums.ChannelDimensionType;
import cn.jojo.front.jaguar.common.pojo.req.BaseListReq;
import cn.jojo.front.jaguar.common.pojo.req.ResPositionEnumSaveReq;
import cn.jojo.front.jaguar.common.pojo.vo.ResPositionEnumVo;
import cn.jojo.front.jaguar.core.service.respenum.BasicInfoExecutor;
import cn.jojo.infra.sdk.api.metadata.DefaultHttpResult;
import cn.jojo.infra.sdk.api.metadata.DefaultPageResp;
import cn.jojo.infra.sdk.api.metadata.IHttpResult;
import cn.jojo.infra.sdk.api.metadata.IPageResp;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR>
 * @date 2024/7/3 14:48
 */
@RestController
@RequestMapping("/admin/channel-dimensions")
public class ChannelDimensionEnumController extends BaseController {

    @Resource
    private BasicInfoExecutor basicInfoExecutor;


    @GetMapping("/{dimensionType}/enums")
    @Operation(summary = "查询渠道维度枚举列表", description = "分页查询渠道维度枚举列表")
    public IPageResp<ResPositionEnumVo> page(@PathVariable
                                             @Parameter(description = "渠道维度类型", schema = @Schema(implementation = ChannelDimensionType.class))
                                             String dimensionType, BaseListReq<Void> req) {
        IPage<ResPositionEnumVo> page = basicInfoExecutor.page(req, ChannelDimensionType.valOf(dimensionType));
        return DefaultPageResp.buildPageResp(req, page.getTotal(), page.getRecords());
    }

    @PostMapping("/{dimensionType}/enums")
    @Operation(summary = "创建渠道维度枚举", description = "创建渠道维度枚举")
    public IHttpResult<Boolean> create(@PathVariable
                                       @Parameter(description = "渠道维度类型", schema = @Schema(implementation = ChannelDimensionType.class))
                                       String dimensionType,
                                       @RequestBody @Valid ResPositionEnumSaveReq req) {
        basicInfoExecutor.saveOrUpdate(req, ChannelDimensionType.valOf(dimensionType));
        return DefaultHttpResult.successWithData(true);
    }

    @PutMapping("/{dimensionType}/enums/{id}")
    @Operation(summary = "修改渠道维度枚举", description = "修改渠道维度枚举")
    public IHttpResult<Boolean> update(@PathVariable
                                       @Parameter(description = "渠道维度类型", schema = @Schema(implementation = ChannelDimensionType.class))
                                       String dimensionType,
                                       @PathVariable @Parameter(description = "资源位枚举id") Integer id,
                                       @RequestBody @Valid ResPositionEnumSaveReq req) {
        req.setId(id);
        basicInfoExecutor.saveOrUpdate(req, ChannelDimensionType.valOf(dimensionType));
        return DefaultHttpResult.successWithData(true);
    }

    @DeleteMapping("/{dimensionType}/enums/{id}")
    @Operation(summary = "删除渠道维度枚举", description = "删除渠道维度枚举")
    public IHttpResult<Boolean> deleteById(@PathVariable
                                           @Parameter(description = "渠道维度类型", schema = @Schema(implementation = ChannelDimensionType.class))
                                           String dimensionType,
                                           @PathVariable @Parameter(description = "资源位枚举id") Integer id) {
        basicInfoExecutor.delete(id, ChannelDimensionType.valOf(dimensionType));
        return DefaultHttpResult.successWithData(true);
    }
}
