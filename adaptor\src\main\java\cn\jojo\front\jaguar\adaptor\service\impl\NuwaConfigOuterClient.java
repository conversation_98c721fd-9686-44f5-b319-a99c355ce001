package cn.jojo.front.jaguar.adaptor.service.impl;

import cn.jojo.front.jaguar.adaptor.service.INuwaConfigOuterClient;
import cn.jojo.infra.sdk.api.metadata.IRpcResult;
import cn.jojo.vulcan.nuwa.dubbo.model.dto.ConfigInstanceResDTO;
import cn.jojo.vulcan.nuwa.dubbo.remote.RemoteConfigInfoService;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.util.Collections;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class NuwaConfigOuterClient implements INuwaConfigOuterClient {

    @DubboReference
    private RemoteConfigInfoService remoteConfigInfoService;

    @Value("${jojoRead.server.nvwa.product.key:D04JBD}")
    private String productKey;

    @Override
    public List<ConfigInstanceResDTO> getConfig(String configKey, Map<String, String> filterMap) {
        filterMap.put("TM-UserAgent-productKey", productKey);
        filterMap.put("TM-UserAgent-appChannel", "xueYuanJaguarServer");
        return getConfig(configKey, filterMap, 100);
    }

    /**
     * 功能描述:cacheKey必须放到filterMap里面
     * 〈新版本通过广告位集合的方式获取配置信息〉
     *
     * @param configKey       广告key
     * @param filterMap       过滤规则
     * @param expectRuleCount 期待反馈结果数量
     * @return configKey - >  configRes
     */
    private List<ConfigInstanceResDTO> getConfig(String configKey, Map<String, String> filterMap, Integer expectRuleCount) {
        try {
            Assert.notEmpty(filterMap, "filterMap is not empty!");
            IRpcResult<List<ConfigInstanceResDTO>> rpcResult = remoteConfigInfoService.listConfigV2(configKey, filterMap, null, expectRuleCount);
            if (!rpcResult.checkSuccess()) {
                log.error("getConfig  failed, req:{}, message:{}",
                        JSON.toJSONString(filterMap), rpcResult.getMessage());
            }
            return rpcResult.getData();
        } catch (Exception e) {
            log.error("getConfig failed, req:{}, message:{}", JSON.toJSONString(filterMap), e);
        }
        return Collections.emptyList();
    }


}