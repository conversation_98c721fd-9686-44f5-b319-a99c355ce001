package cn.jojo.front.jaguar.adaptor.service.impl;

import cn.jojo.front.jaguar.common.utils.JsonUtil;
import cn.jojo.infra.sdk.api.metadata.IRpcResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.function.Supplier;

/**
 * <AUTHOR>
 * @date 2023/9/14 17:38
 * @desc
 */
@Slf4j
public abstract class AbstractRpcClient {

    protected <P, R> Optional<R> doRpc(Function<P, IRpcResult<R>> function, P param) {
        return doRpc(param, function);
    }


    protected <P, R> Optional<R> doRpc(P param, Function<P, IRpcResult<R>> function) {
        try {
            IRpcResult<R> rpcResult = function.apply(param);
            if (!rpcResult.checkSuccess()) {
                log.error("do rpc call failed, param={}, msg={}", param, rpcResult.getMessage());
                return Optional.empty();
            }
            return Optional.ofNullable(rpcResult.getData());
        } catch (Exception e) {
            log.error("do rpc call failed, param={}", param, e);
            return Optional.empty();
        }
    }

    protected <P, R> R doRpc(P param, Function<P, IRpcResult<R>> function, Supplier<R> defaultValue) {
        return doRpc(param, function).orElseGet(defaultValue);
    }

    protected <P, R, T> Optional<T> doRpc(P param, Function<P, IRpcResult<R>> function, Class<T> targetClass) {
        return doRpc(param, function).map(data -> covert(data, targetClass));
    }

    protected <P, R, T> List<T> doRpcList(P param, Function<P, IRpcResult<List<R>>> function,
                                          Class<T> targetClass) {
        return doRpc(param, function).map(data -> {
            if (data instanceof Collection) {
                return covert(data, targetClass);
            }
            return Collections.<T>emptyList();
        }).orElse(Collections.emptyList());
    }

    protected <P, R> List<R> doRpcList(P param, Function<P, IRpcResult<List<R>>> function) {
        return doRpc(param, function).map(data -> {
            if (data instanceof Collection) {
                return data;
            }
            return Collections.<R>emptyList();
        }).orElse(Collections.emptyList());
    }

    protected <R> R covert(Object source, Class<R> targetClass) {
        if (Objects.isNull(source)) {
            return null;
        }
        return JsonUtil.beanToBean(source, targetClass);
    }

    protected <P, R> List<R> covert(Collection<P> source, Class<R> targetClass) {
        if (CollectionUtils.isEmpty(source)) {
            return Collections.emptyList();
        }
        return JsonUtil.beanListTrans(source, targetClass);
    }
}
