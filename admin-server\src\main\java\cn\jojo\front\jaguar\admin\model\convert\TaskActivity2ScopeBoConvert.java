package cn.jojo.front.jaguar.admin.model.convert;

import cn.jojo.edu.common.utils.convert.BaseModelConvert;
import cn.jojo.front.jaguar.common.pojo.bo.activity.ActivityScopeBo;
import cn.jojo.front.jaguar.common.pojo.entity.task.TaskActivity;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 下拉选查询req 转vo
 *
 * <AUTHOR>
 * @date 2024/03/16
 */
@Mapper
public interface TaskActivity2ScopeBoConvert extends BaseModelConvert<TaskActivity, ActivityScopeBo> {

    TaskActivity2ScopeBoConvert INSTANCE = Mappers.getMapper(TaskActivity2ScopeBoConvert.class);
}
