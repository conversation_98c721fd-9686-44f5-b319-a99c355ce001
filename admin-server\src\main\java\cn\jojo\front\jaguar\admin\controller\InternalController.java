package cn.jojo.front.jaguar.admin.controller;

import cn.jojo.front.jaguar.common.enums.ConfigType;
import cn.jojo.front.jaguar.common.pojo.entity.grayscale.GrayTestGroup;
import cn.jojo.front.jaguar.common.pojo.entity.grayscale.Grayscale;
import cn.jojo.front.jaguar.common.pojo.entity.grayscale.GrayscaleGroupItem;
import cn.jojo.front.jaguar.core.dao.mapper.GrayscaleGroupItemMapper;
import cn.jojo.front.jaguar.core.dao.mapper.GrayscaleMapper;
import cn.jojo.front.jaguar.core.service.grayscale.GrayTestGroupService;
import cn.jojo.front.jaguar.tasks.job.UserGroupingSynchronizationJobHandler;
import cn.jojo.infra.sdk.api.metadata.DefaultHttpResult;
import cn.jojo.infra.sdk.api.metadata.IHttpResult;
import cn.jojo.infra.sdk.g11n.i18n.MixedMessageSource;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;

import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/admin/internal")
@Tag(name = "Jaguar管理后台")
@Validated
public class InternalController {

    @Resource
    private MixedMessageSource mixedMessageSource;
    @Resource
    private UserGroupingSynchronizationJobHandler userGroupingSynchronizationJobHandler;
    @Resource
    private GrayscaleMapper grayscaleMapper;
    @Resource
    private GrayTestGroupService grayTestGroupService;
    @Resource
    private GrayscaleGroupItemMapper grayscaleGroupItemMapper;

//    @GetMapping("/user-grouping/synchronization")
//    @Operation(summary ="手动执行同步用户分群",   description = "手动执行同步用户分群")
//    public IHttpResult<Boolean> manualExecuteSynchronizationUserGrouping() {
//        userGroupingSynchronizationJobHandler.execute(null);
//        return DefaultHttpResult.successWithData(true);
//    }

//    @GetMapping("/user-grouping/delete")
//    @Operation(summary ="手动执行删除用户分群",   description = "手动执行删除用户分群")
//    public IHttpResult<Boolean> manualExecuteDeleteUserGrouping() {
//        userGroupingSynchronizationJobHandler.executeDeleteExpireData(null);
//        return DefaultHttpResult.successWithData(true);
//    }

    @GetMapping("/grayscale/refresh")
    public IHttpResult<Boolean> refreshData() {
        List<Grayscale> grayscaleList = grayscaleMapper.selectList(new QueryWrapper<Grayscale>().lambda()
            .eq(Grayscale::getConfigType, ConfigType.MANUAL).select(Grayscale::getId));
        List<Integer> grayIds = grayscaleList.stream().map(Grayscale::getId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(grayIds)) {
            return DefaultHttpResult.successWithData(true);
        }
        List<GrayTestGroup> groups = grayTestGroupService.listGrayTestGroupByGrayscaleIds(grayIds);
        groups.forEach(item -> {
            GrayscaleGroupItem insert = new GrayscaleGroupItem()
                .setBusinessId(item.getBusinessId())
                .setSectionType(item.getSectionType())
                .setPageType(item.getPageType())
                .setGroupId(item.getId());
            grayscaleGroupItemMapper.insert(insert);
        });
        return DefaultHttpResult.successWithData(true);
    }

    @GetMapping("/getConfig")
    public IHttpResult<String> getConfig(@RequestParam("key") @NotBlank String key) {
        String value = mixedMessageSource
            .getMessageAutoReplaceLocaleResourceBundle(key);
        return DefaultHttpResult.successWithData(value);
    }
}
