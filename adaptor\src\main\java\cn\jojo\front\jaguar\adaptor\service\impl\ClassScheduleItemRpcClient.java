package cn.jojo.front.jaguar.adaptor.service.impl;

import cn.jojo.edu.malacca.rpc.api.dto.EduClassScheduleCountDto;
import cn.jojo.edu.malacca.rpc.api.dto.EduClassScheduleItemDto;
import cn.jojo.edu.malacca.rpc.api.req.EduClassScheduleItemBatchQueryReq;
import cn.jojo.edu.malacca.rpc.api.req.EduClassScheduleItemQueryReq;
import cn.jojo.edu.malacca.rpc.api.req.EduUserClassScheduleItemQueryReq;
import cn.jojo.edu.malacca.rpc.api.service.EduClassScheduleRpcService;
import cn.jojo.front.jaguar.adaptor.service.IClassScheduleItemRpcClient;
import cn.jojo.front.jaguar.common.pojo.req.EduUserClassScheduleItemQueryRpcReq;
import cn.jojo.front.jaguar.common.utils.ModelConvertUtil;
import cn.jojo.infra.sdk.api.metadata.IRpcResult;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
@Slf4j
public class ClassScheduleItemRpcClient implements IClassScheduleItemRpcClient {

    @DubboReference
    private EduClassScheduleRpcService eduClassScheduleRpcService;

    @Value("${classId.batch.size:20}")
    private Integer classIdBatchSize;

    @Override
    public Map<Long, Integer> batchQueryClassScheduleItemCount(List<Long> classIds) {
        if (CollectionUtils.isEmpty(classIds)) {
            return Collections.emptyMap();
        }

        return Lists.partition(classIds, classIdBatchSize)
            .stream().map(ids -> {
                IRpcResult<List<EduClassScheduleCountDto>> rpcResult = null;
                try {
                    rpcResult = eduClassScheduleRpcService.queryBatchClassScheduleItemCount(
                        EduClassScheduleItemBatchQueryReq.builder()
                            .classIds(ids)
                            .build());
                    if (!rpcResult.checkSuccess()) {
                        log.error("eduClassScheduleRpcService.batchQueryClassScheduleItemCount fail. classIds:{}",
                            classIds);
                        return Collections.<EduClassScheduleCountDto>emptyList();
                    }
                    if (CollectionUtils.isEmpty(rpcResult.getData())) {
                        return Collections.<EduClassScheduleCountDto>emptyList();
                    }
                    return rpcResult.getData();

                } catch (Exception e) {
                    log.error("eduClassScheduleRpcService.batchQueryClassScheduleItemCount fail, classIds: {}",
                        classIds, e);
                    return Collections.<EduClassScheduleCountDto>emptyList();
                }
            })
            .flatMap(Collection::stream).filter(Objects::nonNull)
            .collect(Collectors
                .toMap(EduClassScheduleCountDto::getClassId, EduClassScheduleCountDto::getLessonCount, (a, b) -> a));
    }

    @Override
    public List<EduClassScheduleItemDto> queryUserClassScheduleItems(EduUserClassScheduleItemQueryRpcReq rpcReq) {
        try {
            EduUserClassScheduleItemQueryReq req = ModelConvertUtil.build(rpcReq,EduUserClassScheduleItemQueryReq.class);
            IRpcResult<List<EduClassScheduleItemDto>> rpcResult =
                eduClassScheduleRpcService.queryUserClassScheduleItems(req);
            if (!rpcResult.checkSuccess()) {
                log.error("query eduClassScheduleRpcService.queryUserClassScheduleItems fail,req:{} ", req);
                return Collections.emptyList();
            }
            return rpcResult.getData();
        } catch (Exception e) {
            log.error("query eduClassScheduleRpcService.queryUserClassScheduleItems fail,req:{}", rpcReq, e);
        }
        return Collections.emptyList();
    }

    @Override
    public List<EduClassScheduleItemDto> queryClassScheduleItems(EduClassScheduleItemQueryReq rpcReq) {
        try {
            EduClassScheduleItemQueryReq req = ModelConvertUtil.build(rpcReq, EduClassScheduleItemQueryReq.class);
            IRpcResult<List<EduClassScheduleItemDto>> rpcResult =
                eduClassScheduleRpcService.queryClassScheduleItems(req);
            if (!rpcResult.checkSuccess()) {
                log.error("query eduClassScheduleRpcService.queryClassScheduleItems fail,req:{} ", req);
                return Collections.emptyList();
            }
            return rpcResult.getData();
        } catch (Exception e) {
            log.error("query eduClassScheduleRpcService.queryClassScheduleItems fail,req:{}", rpcReq, e);
        }
        return Collections.emptyList();
    }
}
