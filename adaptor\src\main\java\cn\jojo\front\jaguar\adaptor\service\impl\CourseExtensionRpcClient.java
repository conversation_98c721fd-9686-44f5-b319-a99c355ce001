package cn.jojo.front.jaguar.adaptor.service.impl;

import cn.jojo.dourm.rpc.api.req.CourseSegmentExtensionQueryReq;
import cn.jojo.dourm.rpc.api.service.ICourseExtensionRpcService;
import cn.jojo.front.jaguar.adaptor.service.ICourseExtensionRpcClient;
import cn.jojo.front.jaguar.common.bo.courseextension.CourseSegmentExtensionBo;
import cn.jojo.front.jaguar.common.pojo.req.CourseSegmentExtensionQueryRpcReq;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2023/11/3 10:43
 * @desc
 */
@Component
public class CourseExtensionRpcClient extends AbstractRpcClient implements ICourseExtensionRpcClient {

    @DubboReference
    private ICourseExtensionRpcService courseExtensionRpcService;

    @Override
    public Optional<CourseSegmentExtensionBo> queryCourseSegmentExtension(CourseSegmentExtensionQueryRpcReq rpcReq) {
        return doRpc(CourseSegmentExtensionQueryReq.builder().userId(rpcReq.getUserId()).build(),
            courseExtensionRpcService::queryCourseSegmentExtension, CourseSegmentExtensionBo.class);
    }
}
