package cn.jojo.front.jaguar.admin.model.req.activity;

import cn.jojo.front.jaguar.common.enums.task.ActivityThemeComponentTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@Builder
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "新增活动主题组件")
public class ActivityThemeComponentSaveReq implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 组件类型
     *
     * @see ActivityThemeComponentTypeEnum
     */
    @Schema(description = "组件类型", implementation = ActivityThemeComponentTypeEnum.class)
    private String componentType;

    /**
     * 在页面中，该组件的序号
     */
    @Schema(description = "序号")
    private Integer orderNum;

    /**
     * 组件属性
     */
    @Schema(description = "组件属性")
    private ActivityThemeComponentPropertiesSaveReq properties;


}